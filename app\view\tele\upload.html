<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <script src="/public/vue/vue.min.js"></script>
  <script src="/public/element/index.js"></script>
  <link rel="stylesheet" href="/public/element/index.css">
  <script src="/public/js/axios.min.js"></script>
  <title>Element Plus demo</title>
</head>
<body>
<div id="app">
  <div class="common-layout">
    <el-container>
      <el-header>Header</el-header>
      <el-main>
        <el-upload
          class="upload-demo"
          action="/tele/upload"
          :on-success="handleSuccess"
        >
          <el-button type="primary">Click to upload</el-button>
          <template #tip>
            <div class="el-upload__tip">
              jpg/png files with a size less than 500kb
            </div>
          </template>
        </el-upload>
        <el-row>
          <div v-for="(o, index) in this.imglist" :key="o" class="text item" style="display: flex;">
            <el-card class="box-card" style="width: 960px" :key="o">
              <div style="display: flex;">
                <img
                  :src="`https://img1.101616.xyz${o}`"
                  class="image"
                  style="width: 200px; height: 200px;"
                />
                <div style="display: flex; flex-direction: column; justify-content: space-between;width: 100%">
                  <div>
                    直接链接
                    <el-input placeholder="Please input" :value="`https://img1.101616.xyz${o}`"
                              :class="'input1-' + index" ref="input1">
                      <template #append>
                        <el-button type="primary" plain @click="copyInput1(index)">Copy</el-button>
                      </template>
                    </el-input>

                  </div>
                  <div>
                    markdown
                    <el-input placeholder="Please input" :value="`![image](${`https://img1.101616.xyz${o}`})`"
                              :class="'input2-' + index" ref="input2">
                      <template #append>
                        <el-button type="primary" plain @click="copyInput2(index)">Copy</el-button>
                      </template>
                    </el-input>
                  </div>
                  <div>
                    HTML
                    <el-input placeholder="Please input" :value="`<img src='https://img1.101616.xyz${o}' width='20%'>`"
                              :class="'input3-' + index" ref="input3">
                      <template #append>
                        <el-button type="primary" plain @click="copyInput3(index)">Copy</el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-row>

      </el-main>
      <el-footer>Footer</el-footer>
    </el-container>
  </div>

</div>

<script>
  new Vue({
    el: "#app",
    data: function() {
      return {
        imglist: []
      };
    },
    created() {
    },
    mounted() {
    },
    methods: {
      handleSuccess(response, file, fileList) {
        this.imglist.push(response[0].src);
        console.log(this.imglist);
      },
      copyInput1(index) {
        const input = this.$refs[`input1`][index].$refs.input.value;
        this.copyToClipboard(input);
      },
      copyInput2(index) {
        const input = this.$refs[`input2`][index].$refs.input.value;
        this.copyToClipboard(input);
      },
      copyInput3(index) {
        const input = this.$refs[`input3`][index].$refs.input.value;
        this.copyToClipboard(input);
      },
      copyToClipboard(text) {
        try {
          navigator.clipboard.writeText(text);
          this.$message.success("复制成功!");
        } catch (err) {
          console.error("Failed to copy text: ", err);
          this.$message.error("复制失败!");
        }
      }
    }
  });


</script>

<style>
    .text {
        font-size: 14px;
    }

    .item {
        padding: 18px 0;
    }

    .box-card {
        width: 100%;
    }

    .image {
        height: 62px;
        width: 62px;
    }
</style>
</body>
</html>

'use strict';

const Controller = require('egg').Controller;
const fs = require('fs');
const path = require('path');

class ScheduleController extends Controller {
  // 显示定时任务管理页面
  async index() {
    const { ctx } = this;

    try {
      // 获取所有定时任务文件
      const schedules = await this.getScheduleFiles();

      // 渲染页面
      await ctx.render('dingshi.html', {
        title: '定时任务管理',
        schedules: schedules || [],
        timestamp: new Date().toLocaleString('zh-CN'),
      });
    } catch (error) {
      ctx.logger.error('获取定时任务数据失败:', error);
      await ctx.render('dingshi.html', {
        title: '定时任务管理',
        schedules: [],
        error: '获取定时任务数据失败',
        timestamp: new Date().toLocaleString('zh-CN'),
      });
    }
  }

  // 获取定时任务数据API
  async getSchedules() {
    const { ctx } = this;

    try {
      const schedules = await this.getScheduleFiles();

      ctx.body = {
        success: true,
        data: schedules || [],
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('获取定时任务数据失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '获取定时任务数据失败',
        error: error.message,
      };
    }
  }

  // 立即执行定时任务
  async executeSchedule() {
    const { ctx } = this;
    const { filename } = ctx.request.body;

    try {
      // 验证参数
      if (!filename) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '参数错误：filename必须提供',
        };
        return;
      }

      // 检查任务文件是否存在
      const schedulePath = path.join(this.app.baseDir, 'app/schedule');
      const filePath = path.join(schedulePath, filename);

      if (!fs.existsSync(filePath)) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: '定时任务文件不存在',
        };
        return;
      }

      // 尝试手动执行任务
      try {
        // 动态加载任务模块
        delete require.cache[filePath]; // 清除缓存
        const taskModule = require(filePath);

        if (taskModule && typeof taskModule.task === 'function') {
          // 创建模拟的上下文
          const mockCtx = {
            app: this.app,
            logger: ctx.logger,
            service: ctx.service,
            helper: ctx.helper,
          };

          // 执行任务
          await taskModule.task.call(mockCtx);

          ctx.logger.info(`手动执行定时任务成功: ${filename}`);

          ctx.body = {
            success: true,
            message: '任务执行成功',
            data: {
              filename: filename,
              executedAt: new Date().toISOString(),
            },
          };
        } else {
          ctx.status = 500;
          ctx.body = {
            success: false,
            message: '任务文件格式错误，缺少task函数',
          };
        }
      } catch (taskError) {
        ctx.logger.error(`执行定时任务失败 ${filename}:`, taskError);
        ctx.status = 500;
        ctx.body = {
          success: false,
          message: `任务执行失败: ${taskError.message}`,
        };
      }
    } catch (error) {
      ctx.logger.error('执行定时任务失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '执行定时任务失败',
        error: error.message,
      };
    }
  }

  // 更新定时任务配置
  async updateSchedule() {
    const { ctx } = this;
    const { filename, config } = ctx.request.body;

    try {
      // 验证参数
      if (!filename || !config) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '参数错误：filename和config必须提供',
        };
        return;
      }

      // 更新定时任务文件
      const result = await this.updateScheduleFile(filename, config);

      if (result.success) {
        ctx.logger.info(`定时任务更新成功: ${filename}`);

        ctx.body = {
          success: true,
          message: '定时任务更新成功',
          data: result.data,
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          message: result.message || '更新定时任务失败',
        };
      }
    } catch (error) {
      ctx.logger.error('更新定时任务失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '更新定时任务失败',
        error: error.message,
      };
    }
  }

  // 获取定时任务文件列表
  async getScheduleFiles() {
    const schedulePath = path.join(this.app.baseDir, 'app/schedule');
    const files = fs.readdirSync(schedulePath);
    const schedules = [];

    for (const file of files) {
      if (file.endsWith('.js') && file !== 'README.md') {
        try {
          const filePath = path.join(schedulePath, file);
          const content = fs.readFileSync(filePath, 'utf8');

          // 解析文件内容获取配置
          const scheduleInfo = this.parseScheduleFile(file, content);
          schedules.push(scheduleInfo);
        } catch (error) {
          console.error(`解析定时任务文件 ${file} 失败:`, error);
        }
      }
    }

    return schedules;
  }

  // 解析定时任务文件
  parseScheduleFile(filename, content) {
    const scheduleInfo = {
      filename: filename,
      name: filename.replace('.js', ''),
      enabled: true,
      interval: '未知',
      type: 'all',
      env: [],
      immediate: false,
      content: content,
      status: 'active',
    };

    try {
      // 优先 interval
      const intervalMatch = content.match(
        /interval:\s*['\"`]?([^'\"`\n]+)['\"`]?/,
      );
      if (intervalMatch) {
        scheduleInfo.interval = intervalMatch[1];
        scheduleInfo.type = 'interval';
      } else {
        // 支持 cron
        const cronMatch = content.match(/cron:\s*['\"`]?([^'\"`\n]+)['\"`]?/);
        if (cronMatch) {
          scheduleInfo.interval = 'cron: ' + cronMatch[1];
          scheduleInfo.type = 'cron';
        }
      }

      const typeMatch = content.match(/type:\s*['\"`]([^'\"`]+)['\"`]/);
      if (typeMatch) {
        scheduleInfo.type = typeMatch[1];
      }

      const envMatch = content.match(/env:\s*\[([^\]]+)\]/);
      if (envMatch) {
        const envStr = envMatch[1].replace(/['\"`]/g, '');
        scheduleInfo.env = envStr.split(',').map((e) => e.trim());
      }

      const enabledMatch = content.match(/enabled:\s*(true|false)/);
      if (enabledMatch) {
        scheduleInfo.enabled = enabledMatch[1] === 'true';
      }

      const immediateMatch = content.match(/immediate:\s*(true|false)/);
      if (immediateMatch) {
        scheduleInfo.immediate = immediateMatch[1] === 'true';
      }
    } catch (error) {
      console.error(`解析定时任务配置失败 ${filename}:`, error);
    }

    return scheduleInfo;
  }

  // 更新定时任务文件
  async updateScheduleFile(filename, config) {
    try {
      const schedulePath = path.join(this.app.baseDir, 'app/schedule');
      const filePath = path.join(schedulePath, filename);

      if (!fs.existsSync(filePath)) {
        return { success: false, message: '定时任务文件不存在' };
      }

      let content = fs.readFileSync(filePath, 'utf8');

      // 更新配置项
      if (config.interval !== undefined) {
        content = content.replace(
          /interval:\s*['"`][^'"`]+['"`]/,
          `interval: '${config.interval}'`,
        );
      }

      if (config.enabled !== undefined) {
        content = content.replace(
          /enabled:\s*(true|false)/,
          `enabled: ${config.enabled}`,
        );
      }

      if (config.env !== undefined) {
        const envArray = Array.isArray(config.env) ? config.env : [config.env];
        const envStr = envArray.map((e) => `'${e}'`).join(', ');
        content = content.replace(/env:\s*\[[^\]]*\]/, `env: [${envStr}]`);
      }

      if (config.immediate !== undefined) {
        content = content.replace(
          /immediate:\s*(true|false)/,
          `immediate: ${config.immediate}`,
        );
      }

      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');

      return {
        success: true,
        data: this.parseScheduleFile(filename, content),
      };
    } catch (error) {
      return {
        success: false,
        message: `更新文件失败: ${error.message}`,
      };
    }
  }
}

module.exports = ScheduleController;

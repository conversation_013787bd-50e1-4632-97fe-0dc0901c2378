{
  "version": "0.2.0",
  "configurations": [
    // ==================== Egg.js 应用调试 ====================
    {
      "name": "🥚 Egg.js 开发模式调试",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/egg-bin",
      "args": ["dev", "--debug", "--inspect=9229"],
      "env": {
        "NODE_ENV": "local",
        "EGG_DEBUG": "true"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/**/*.js"],
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "Egg.js",
        "order": 1
      },
      "preLaunchTask": null,
      "postDebugTask": null
    },
    {
      "name": "🚀 Egg.js 生产模式调试",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/egg-scripts",
      "args": ["start", "--daemon", "--title=egg-server-example"],
      "env": {
        "NODE_ENV": "prod",
        "EGG_SERVER_ENV": "prod"
      },
      "console": "integratedTerminal",
      "restart": false,
      "protocol": "inspector",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/**/*.js"],
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "Egg.js",
        "order": 2
      }
    },
    
    // ==================== 测试调试 ====================
    {
      "name": "🧪 调试单元测试",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/egg-bin",
      "args": ["test", "--inspect=9229"],
      "env": {
        "NODE_ENV": "unittest",
        "EGG_DEBUG": "true"
      },
      "console": "integratedTerminal",
      "restart": false,
      "protocol": "inspector",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/**/*.js"],
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "测试",
        "order": 1
      }
    },
    {
      "name": "🎯 调试当前测试文件",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/egg-bin",
      "args": ["test", "${relativeFile}", "--inspect=9229"],
      "env": {
        "NODE_ENV": "unittest",
        "EGG_DEBUG": "true"
      },
      "console": "integratedTerminal",
      "restart": false,
      "protocol": "inspector",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/**/*.js"],
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "测试",
        "order": 2
      }
    },
    
    // ==================== 脚本调试 ====================
    {
      "name": "📜 调试当前 JavaScript 文件",
      "type": "node",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "restart": false,
      "protocol": "inspector",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "脚本",
        "order": 1
      }
    },
    {
      "name": "🔧 调试 scripts 目录脚本",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/scripts/${input:scriptName}",
      "console": "integratedTerminal",
      "restart": false,
      "protocol": "inspector",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "sourceMaps": true,
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "脚本",
        "order": 2
      }
    },
    
    // ==================== 远程调试 ====================
    {
      "name": "🌐 附加到远程 Egg.js 进程",
      "type": "node",
      "request": "attach",
      "address": "localhost",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}",
      "protocol": "inspector",
      "sourceMaps": true,
      "outFiles": ["${workspaceFolder}/**/*.js"],
      "skipFiles": [
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "远程",
        "order": 1
      }
    },
    {
      "name": "🐳 Docker 容器调试",
      "type": "node",
      "request": "attach",
      "address": "localhost",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/app",
      "protocol": "inspector",
      "sourceMaps": true,
      "outFiles": ["/app/**/*.js"],
      "skipFiles": [
        "<node_internals>/**",
        "/app/node_modules/**"
      ],
      "presentation": {
        "hidden": false,
        "group": "远程",
        "order": 2
      }
    }
  ],
  "inputs": [
    {
      "id": "scriptName",
      "description": "选择要调试的脚本文件",
      "type": "pickString",
      "options": [
        "check-wkhtmltoimage.js",
        "test-wkhtmltoimage-simple.js",
        "debug-feishu.js",
        "clean-and-start.js",
        "dev-fast.js"
      ]
    }
  ],
  "compounds": [
    {
      "name": "🔥 完整开发环境",
      "configurations": [
        "🥚 Egg.js 开发模式调试"
      ],
      "presentation": {
        "hidden": false,
        "group": "组合",
        "order": 1
      }
    }
  ]
}

<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <script src="/public/vue/vue.min.js"></script>
  <script src="/public/element/index.js"></script>
  <link rel="stylesheet" href="/public/element/index.css">
  <script src="/public/js/axios.min.js"></script>
  <title>Element Plus demo</title>
</head>

<body>
<div>
</div>
<div id="app">
  <el-alert :title="this.time" type="success" center></el-alert>
  <el-table :data="tableData" style="width: 100%" :row-class-name="tableRowClassName" border
            :default-sort="{prop: 'total', order: 'descending'}">
    <el-table-column label="序号" type="index" :index="indexMethod">

    </el-table-column>

    <el-table-column prop="deptName" label="单位" sortable width="100">
    </el-table-column>

    <el-table-column prop="deptCode" label="岗位代码" sortable width="100%">
    </el-table-column>
    <el-table-column prop="areaName" label="地区" sortable width="100%">
    </el-table-column>
    <el-table-column label="招聘岗位" width="100%">
      <template #default="scope">
        <div>
          <p v-text="scope.row.postName"></p>
          <p v-text="scope.row.sex"></p>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="专业" width="160">
      <template #default="scope">
        <div>
          <p :class="{ 'blue-text': scope.row.specialty.includes('计算机信息管理类') }"
             v-text="scope.row.specialty"></p>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="postNum" label="招聘人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="notExamineNum" label="待审核人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="examineNum" label="	审核通过人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="notApprovedNum" label="审核不通过人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="allNum" label="总人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="holdnum" label="待定人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="stuHasAbandon" label="放弃人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="other" label="备注" sortable width="166">
    </el-table-column>
    <el-table-column prop="update" label="更新时间" :formatter="formatterdate" width="100%">
    </el-table-column>
  </el-table>
</div>

<script>
  new Vue({
    el: "#app",
    data: function() {
      return {
        tableData: null,
        link: "wcnm",
        updatetime: "",
        time: ""
      };
    },
    created() {

    },
    mounted() {   //自动触发写入的函数
      this.getData();
    },
    methods: {
      formatter(row, column) {
        return row.zbmrs - 0;
        //:formatter="formatter"
      },
      formatterdate(row, column) {
        if (row.update) {
          let DateString = row.update;
          let date = new Date(DateString);
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let Hours = date.getHours();
          let Minutes = date.getMinutes();
          let Seconds = date.getSeconds();
          if (month < 10) {
            month = "0" + month;
          }
          if (day < 10) {
            day = "0" + day;
          }
          let s_createtime = year + "-" + month + "-" + day + " " + Hours + ":" + Minutes + ":" + Seconds;
          return s_createtime;
        }
      },
      tableRowClassName({ row, rowIndex }) {
        if (
          (row.deptCode === "1068" && row.postCode === "01") ||
          (row.deptCode === "1104" && row.postCode === "01") ||
          (row.deptCode === "2019" && row.postCode === "01") ||
          (row.deptCode === "1701" && row.postCode === "01")

        ) {
          return "warning-row";
        } else if (
          (row.deptCode === "1058" && row.postCode === "01") ||
          (row.deptCode === "1062" && row.postCode === "01") ||
          (row.deptCode === "1070" && row.postCode === "01") ||
          (row.deptCode === "1083" && row.postCode === "04") ||
          (row.deptCode === "1104" && row.postCode === "01") ||
          (row.deptCode === "1127" && row.postCode === "01") ||
          (row.deptCode === "1343" && row.postCode === "01") ||
          (row.deptCode === "1349" && row.postCode === "01") ||
          (row.deptCode === "1351" && row.postCode === "02") ||
          (row.deptCode === "2204" && row.postCode === "01") ||
          (row.deptCode === "2205" && row.postCode === "01")
        ) {
          return "success-row";
        }
        return "";
      },
      getData() {
        axios.get("/fjzz?all={[all]}&riqi={[riqi]}").then(res => {
          this.tableData = res.data;
          this.time = res.data[0].update;
          console.log(this.time);
        });
      },
      indexMethod(index) {
        return index * 1;
      }

    }
  });


</script>

<style>


    .el-table .warning-row {
        background: oldlace;
    }

    .el-table .success-row {
        background: #f0f9eb;
    }

    .el-table .man-row {
        background: #ffff90;
    }

    .blue-text {
        color: skyblue;
    }
</style>
</body>

</html>
<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Egg.js 实时日志监控</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],
            },
            colors: {
              primary: '#2563eb',
              accent: '#f59e42',
            },
            // 扩展栅格系统支持24列
            gridTemplateColumns: {
              24: 'repeat(24, minmax(0, 1fr))',
            },
            gridColumn: {
              'span-13': 'span 13 / span 13',
              'span-14': 'span 14 / span 14',
              'span-15': 'span 15 / span 15',
              'span-16': 'span 16 / span 16',
              'span-17': 'span 17 / span 17',
              'span-18': 'span 18 / span 18',
              'span-19': 'span 19 / span 19',
              'span-20': 'span 20 / span 20',
              'span-21': 'span 21 / span 21',
              'span-22': 'span 22 / span 22',
              'span-23': 'span 23 / span 23',
              'span-24': 'span 24 / span 24',
            },
          },
        },
      };
    </script>
    <style>
      .log-container {
        height: calc(100vh - 120px);
      }
      .log-section {
        min-height: 220px;
      }
      .log-content {
        font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
        font-size: 13px;
        line-height: 1.5;
        background: linear-gradient(135deg, #fff 0%, #f3f6fa 100%);
        color: #222;
      }
      .log-line {
        border-bottom: 1px solid #f0f0f0;
        padding: 2px 8px;
        word-break: break-all;
      }
      .log-line:hover {
        background-color: #f5f7fa;
      }
      .log-line.error {
        background-color: #fff0f0;
        border-left: 3px solid #ef4444;
      }
      .log-line.warn {
        background-color: #fffbe6;
        border-left: 3px solid #f59e0b;
      }
      .log-line.info {
        background-color: #e6f4ff;
        border-left: 3px solid #3b82f6;
      }
      .auto-scroll {
        scroll-behavior: smooth;
      }
      body {
        background: linear-gradient(135deg, #fff 0%, #e9f1ff 100%);
        color: #222;
      }
    </style>
  </head>
  <body class="min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-lg border-b border-gray-200">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold flex items-center text-primary">
            <svg
              class="w-8 h-8 mr-3 text-primary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            Egg.js 实时日志监控
          </h1>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">状态:</span>
              <span
                id="connection-status"
                class="px-2 py-1 rounded text-xs font-medium bg-green-500 text-white"
                >连接中</span
              >
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">用户:</span>
              <span class="text-sm text-gray-700">已登录</span>
              <a href="/logout" class="text-sm text-red-500 hover:text-red-700 underline">登出</a>
            </div>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="search-input"
                placeholder="搜索日志..."
                class="px-3 py-1 bg-white text-gray-700 rounded text-sm border border-gray-300 focus:border-primary focus:outline-none"
              />
              <button
                id="search-btn"
                class="px-3 py-1 bg-primary hover:bg-blue-700 text-white rounded text-sm font-medium transition-colors"
              >
                搜索
              </button>
            </div>
            <button
              id="clear-all"
              class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg text-sm font-medium transition-colors"
            >
              清空所有
            </button>
            <button
              id="toggle-auto-scroll"
              class="px-4 py-2 bg-primary hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
            >
              自动滚动: 开
            </button>
            <button
              id="download-logs"
              class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-colors"
            >
              下载日志
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-6">
      <!-- 使用24列栅格系统实现 1-6-2-6-2-6-1 布局 -->
      <div class="grid grid-cols-24 gap-4 log-container">
        <!-- 左侧间距 (1格) -->
        <div class="col-span-1"></div>

        <!-- Web日志 (6格) -->
        <div
          class="col-span-6 bg-white rounded-lg shadow-xl log-section border border-gray-200 flex flex-col"
        >
          <div
            class="bg-gradient-to-r from-white to-blue-50 px-4 py-3 rounded-t-lg border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold flex items-center text-primary">
                <div class="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                Web日志 (egg-web.log)
              </h2>
              <div class="flex items-center space-x-2">
                <span id="web-count" class="text-xs text-gray-400">0 行</span>
                <button onclick="clearLog('web')" class="text-xs text-red-500 hover:text-red-400">
                  清空
                </button>
              </div>
            </div>
          </div>
          <div
            id="web-log"
            class="log-content overflow-y-auto p-2 rounded-b-lg auto-scroll flex-1"
            style="min-height: 220px"
          >
            <div class="text-gray-400 text-center py-8">等待日志数据...</div>
          </div>
        </div>

        <!-- 间距 (2格) -->
        <div class="col-span-2"></div>

        <!-- Agent日志 (6格) -->
        <div
          class="col-span-6 bg-white rounded-lg shadow-xl log-section border border-gray-200 flex flex-col"
        >
          <div
            class="bg-gradient-to-r from-white to-yellow-50 px-4 py-3 rounded-t-lg border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold flex items-center text-yellow-500">
                <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2 animate-pulse"></div>
                Agent日志 (egg-agent.log)
              </h2>
              <div class="flex items-center space-x-2">
                <span id="agent-count" class="text-xs text-gray-400">0 行</span>
                <button onclick="clearLog('agent')" class="text-xs text-red-500 hover:text-red-400">
                  清空
                </button>
              </div>
            </div>
          </div>
          <div
            id="agent-log"
            class="log-content overflow-y-auto p-2 rounded-b-lg auto-scroll flex-1"
            style="min-height: 220px"
          >
            <div class="text-gray-400 text-center py-8">等待日志数据...</div>
          </div>
        </div>

        <!-- 间距 (2格) -->
        <div class="col-span-2"></div>

        <!-- Error日志 (6格) -->
        <div
          class="col-span-6 bg-white rounded-lg shadow-xl log-section border border-gray-200 flex flex-col"
        >
          <div
            class="bg-gradient-to-r from-white to-red-50 px-4 py-3 rounded-t-lg border-b border-gray-100"
          >
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold flex items-center text-red-500">
                <div class="w-3 h-3 bg-red-400 rounded-full mr-2 animate-pulse"></div>
                错误日志 (common-error.log)
              </h2>
              <div class="flex items-center space-x-2">
                <span id="error-count" class="text-xs text-gray-400">0 行</span>
                <button onclick="clearLog('error')" class="text-xs text-red-500 hover:text-red-400">
                  清空
                </button>
              </div>
            </div>
          </div>
          <div
            id="error-log"
            class="log-content overflow-y-auto p-2 rounded-b-lg auto-scroll flex-1"
            style="min-height: 220px"
          >
            <div class="text-gray-400 text-center py-8">等待日志数据...</div>
          </div>
        </div>

        <!-- 右侧间距 (1格) -->
        <div class="col-span-1"></div>
      </div>
    </main>

    <script>
      // 全局变量
      let autoScroll = true;
      let logCounts = { web: 0, agent: 0, error: 0 };
      let eventSources = {};

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        initializeLogStreams();
        setupEventListeners();
      });

      // 设置事件监听器
      function setupEventListeners() {
        // 清空所有日志
        document.getElementById('clear-all').addEventListener('click', function () {
          clearLog('web');
          clearLog('agent');
          clearLog('error');
        });

        // 切换自动滚动
        document.getElementById('toggle-auto-scroll').addEventListener('click', function () {
          autoScroll = !autoScroll;
          this.textContent = `自动滚动: ${autoScroll ? '开' : '关'}`;
          this.className = autoScroll
            ? 'px-4 py-2 bg-primary hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors'
            : 'px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm font-medium transition-colors';
        });

        // 搜索功能
        document.getElementById('search-btn').addEventListener('click', performSearch);
        document.getElementById('search-input').addEventListener('keypress', function (e) {
          if (e.key === 'Enter') {
            performSearch();
          }
        });

        // 下载日志
        document.getElementById('download-logs').addEventListener('click', function () {
          const dropdown = document.createElement('div');
          dropdown.className =
            'absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-600 z-50';
          dropdown.innerHTML = `
                    <a href="/api/logs/download?type=web" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 rounded-t-lg">下载 Web 日志</a>
                    <a href="/api/logs/download?type=agent" class="block px-4 py-2 text-sm text-white hover:bg-gray-700">下载 Agent 日志</a>
                    <a href="/api/logs/download?type=error" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 rounded-b-lg">下载 Error 日志</a>
                `;

          // 移除现有下拉菜单
          const existing = document.querySelector('.download-dropdown');
          if (existing) existing.remove();

          dropdown.className += ' download-dropdown';
          this.parentElement.style.position = 'relative';
          this.parentElement.appendChild(dropdown);

          // 点击外部关闭
          setTimeout(() => {
            document.addEventListener('click', function closeDropdown(e) {
              if (
                !dropdown.contains(e.target) &&
                e.target !== document.getElementById('download-logs')
              ) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
              }
            });
          }, 100);
        });
      }

      // 初始化日志流
      function initializeLogStreams() {
        const logTypes = ['web', 'agent', 'error'];

        logTypes.forEach((type) => {
          connectLogStream(type);
        });
      }

      // 连接日志流
      function connectLogStream(logType) {
        if (eventSources[logType]) {
          eventSources[logType].close();
        }

        const eventSource = new EventSource(`/api/logs/${logType}`);
        eventSources[logType] = eventSource;

        eventSource.onopen = function () {
          updateConnectionStatus('connected');
          console.log(`${logType} 日志流已连接`);
        };

        eventSource.onmessage = function (event) {
          try {
            const data = JSON.parse(event.data);

            // 检查是否是身份验证错误
            if (data.success === false && data.message === '请先登录') {
              handleAuthError();
              return;
            }

            // 确保正确处理中文字符
            const decodedLine = decodeURIComponent(escape(data.line));
            appendLogLine(logType, decodedLine, data.timestamp);
          } catch (e) {
            // 如果不是JSON，直接作为文本处理
            try {
              const decodedText = decodeURIComponent(escape(event.data));
              appendLogLine(logType, decodedText);
            } catch (decodeError) {
              // 如果解码失败，使用原始数据
              appendLogLine(logType, event.data);
            }
          }
        };

        eventSource.onerror = function (error) {
          console.error(`${logType} 日志流错误:`, error);
          console.log(`${logType} EventSource readyState:`, eventSource.readyState);
          console.log(`${logType} EventSource url:`, eventSource.url);

          // 检查是否是身份验证错误（通常返回401或403状态码）
          if (eventSource.readyState === EventSource.CLOSED) {
            // 尝试发送一个测试请求来检查身份验证状态
            fetch('/api/logs/web', { method: 'HEAD' })
              .then((response) => {
                if (response.status === 401 || response.status === 403) {
                  handleAuthError();
                  return;
                }
                // 如果不是身份验证错误，继续正常的错误处理
                handleConnectionError(logType, eventSource);
              })
              .catch(() => {
                // 网络错误，按正常连接错误处理
                handleConnectionError(logType, eventSource);
              });
          } else {
            handleConnectionError(logType, eventSource);
          }
        };
      }

      // 处理连接错误（非身份验证错误）
      function handleConnectionError(logType, eventSource) {
        updateConnectionStatus('error');

        // 显示错误信息到日志容器
        const logContainer = document.getElementById(`${logType}-log`);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-red-400 text-center py-4';
        errorDiv.textContent = `连接错误，5秒后重试... (${new Date().toLocaleTimeString()})`;
        logContainer.appendChild(errorDiv);

        // 5秒后重连
        setTimeout(() => {
          if (eventSource.readyState === EventSource.CLOSED) {
            console.log(`重新连接 ${logType} 日志流...`);
            connectLogStream(logType);
          }
        }, 5000);
      }

      // 添加日志行
      function appendLogLine(logType, line, timestamp) {
        const logContainer = document.getElementById(`${logType}-log`);

        // 清除初始提示
        if (logContainer.querySelector('.text-gray-500')) {
          logContainer.innerHTML = '';
        }

        // 创建日志行元素
        const logLine = document.createElement('div');
        logLine.className = 'log-line';

        // 根据日志内容添加样式
        if (line.toLowerCase().includes('error')) {
          logLine.classList.add('error');
        } else if (line.toLowerCase().includes('warn')) {
          logLine.classList.add('warn');
        } else if (line.toLowerCase().includes('info')) {
          logLine.classList.add('info');
        }

        // 格式化时间戳
        const time = timestamp
          ? new Date(timestamp).toLocaleTimeString()
          : new Date().toLocaleTimeString();

        // 设置内容
        logLine.innerHTML = `
                <span class="text-gray-400 text-xs">[${time}]</span>
                <span class="ml-2">${escapeHtml(line)}</span>
            `;

        // 添加到容器
        logContainer.appendChild(logLine);

        // 更新计数
        logCounts[logType]++;
        document.getElementById(`${logType}-count`).textContent = `${logCounts[logType]} 行`;

        // 限制最大行数（防止内存溢出）
        const maxLines = 1000;
        if (logContainer.children.length > maxLines) {
          logContainer.removeChild(logContainer.firstChild);
          logCounts[logType]--;
        }

        // 自动滚动到底部
        if (autoScroll) {
          logContainer.scrollTop = logContainer.scrollHeight;
        }
      }

      // 清空日志
      function clearLog(logType) {
        // 发送清空请求到服务器
        fetch(`/api/logs/clear`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ type: logType }),
        })
          .then((response) => {
            if (response.status === 401 || response.status === 403) {
              handleAuthError();
              return;
            }
            return response.json();
          })
          .then((data) => {
            if (data && data.success === false && data.message === '请先登录') {
              handleAuthError();
              return;
            }

            // 清空前端显示
            const logContainer = document.getElementById(`${logType}-log`);
            logContainer.innerHTML = '<div class="text-gray-500 text-center py-8">日志已清空</div>';
            logCounts[logType] = 0;
            document.getElementById(`${logType}-count`).textContent = '0 行';
          })
          .catch((error) => {
            console.error('清空日志失败:', error);
            // 即使服务器清空失败，也清空前端显示
            const logContainer = document.getElementById(`${logType}-log`);
            logContainer.innerHTML = '<div class="text-gray-500 text-center py-8">日志已清空</div>';
            logCounts[logType] = 0;
            document.getElementById(`${logType}-count`).textContent = '0 行';
          });
      }

      // 更新连接状态
      function updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');

        switch (status) {
          case 'connected':
            statusElement.textContent = '已连接';
            statusElement.className =
              'px-2 py-1 rounded text-xs font-medium bg-green-500 text-white';
            break;
          case 'error':
            statusElement.textContent = '连接错误';
            statusElement.className = 'px-2 py-1 rounded text-xs font-medium bg-red-500 text-white';
            break;
          default:
            statusElement.textContent = '连接中';
            statusElement.className =
              'px-2 py-1 rounded text-xs font-medium bg-yellow-500 text-white';
        }
      }

      // 搜索功能
      function performSearch() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        if (!searchTerm) return;

        const logTypes = ['web', 'agent', 'error'];
        let totalMatches = 0;

        logTypes.forEach((type) => {
          const container = document.getElementById(`${type}-log`);
          const lines = container.querySelectorAll('.log-line');
          let matches = 0;

          lines.forEach((line) => {
            const text = line.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
              line.style.backgroundColor = 'rgba(59, 130, 246, 0.3)';
              line.style.border = '1px solid #3b82f6';
              matches++;
              totalMatches++;
            } else {
              line.style.backgroundColor = '';
              line.style.border = '';
            }
          });

          // 滚动到第一个匹配项
          if (matches > 0) {
            const firstMatch = container.querySelector(
              '.log-line[style*="rgba(59, 130, 246, 0.3)"]',
            );
            if (firstMatch) {
              firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }
        });

        // 显示搜索结果
        updateConnectionStatus(`找到 ${totalMatches} 个匹配项`);
        setTimeout(() => {
          updateConnectionStatus('connected');
        }, 3000);
      }

      // 清除搜索高亮
      function clearSearchHighlight() {
        const logTypes = ['web', 'agent', 'error'];
        logTypes.forEach((type) => {
          const container = document.getElementById(`${type}-log`);
          const lines = container.querySelectorAll('.log-line');
          lines.forEach((line) => {
            line.style.backgroundColor = '';
            line.style.border = '';
          });
        });
      }

      // 处理身份验证错误
      function handleAuthError() {
        updateConnectionStatus('需要登录');

        // 显示登录提示
        const logTypes = ['web', 'agent', 'error'];
        logTypes.forEach((type) => {
          const logContainer = document.getElementById(`${type}-log`);
          logContainer.innerHTML = `
            <div class="text-red-500 text-center py-8">
              <div class="mb-4">⚠️ 需要登录才能查看日志</div>
              <a href="/login" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors">
                前往登录
              </a>
            </div>
          `;
        });

        // 关闭所有连接
        Object.values(eventSources).forEach((source) => {
          if (source) source.close();
        });
      }

      // HTML转义
      function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
      }

      // 页面卸载时关闭连接
      window.addEventListener('beforeunload', function () {
        Object.values(eventSources).forEach((source) => {
          if (source) source.close();
        });
      });
    </script>
  </body>
</html>

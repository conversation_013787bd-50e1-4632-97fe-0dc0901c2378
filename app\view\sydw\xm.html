<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <script src="/public/vue/vue.min.js"></script>
  <script src="/public/element/index.js"></script>
  <link rel="stylesheet" href="/public/element/index.css">
  <script src="/public/js/axios.min.js"></script>
  <title>Element Plus demo</title>
</head>
<body>

<div id="app">
  <el-alert
    title="{[time]}"
    type="success"
    center>
  </el-alert>
  <el-table
    :data="tableData"
    style="width: 100%"
    :row-class-name="tableRowClassName"
    border
    :default-sort="{prop: 'total', order: 'descending'}"
  >
    <el-table-column
      prop="companyname"
      label="单位"
      width="200">
    </el-table-column>
    <el-table-column
      prop="postinfono"
      label="岗位代码"
      sortable
      width="100">
    </el-table-column>
    <el-table-column
      label="招聘岗位"
      width="100">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <el-link v-text="scope.row.takejob" :href="scope.row.link" target="_blank" type="primary"></el-link>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      prop="sjzbmrs"
      label="实际总人数"
      sortable
      width="100">
    </el-table-column>
    <el-table-column
      prop="shtgyjf"
      label="	审核通过人数"
      sortable
      width="100"
    >
    </el-table-column>

    <el-table-column
      prop="dshrs"
      label="待审核人数"
      sortable
      width="100">
    </el-table-column>

    <el-table-column
      prop="shbtg"
      label="审核不通过人数"
      sortable
      width="100">
    </el-table-column>

    <el-table-column
      prop="zbmrs"
      label="总人数"
      sortable
      width="100">
    </el-table-column>
    <el-table-column
      prop="takenum"
      label="招聘人数	"
      sortable
      width="100">
    </el-table-column>
    <el-table-column
      prop="tjsj"
      label="更新时间"
      width="180">
    </el-table-column>
  </el-table>
</div>

<script>
  new Vue({
    el: "#app",
    data: function() {
      return {
        tableData: null,
        link: "wcnm"
      };
    },
    created() {

    },
    mounted() {   //自动触发写入的函数
      this.getData();
    },
    methods: {
      formatter(row, column) {
        return (row.dshrs + row.shtgyjf) / 1;
        //:formatter="formatter"
      },
      formatterdate(row, column) {
        if (row.uptime) {
          let DateString = row.uptime;
          let date = new Date(DateString);
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let Hours = date.getHours();
          let Minutes = date.getMinutes();
          let Seconds = date.getSeconds();
          if (month < 10) {
            month = "0" + month;
          }
          if (day < 10) {
            day = "0" + day;
          }
          let s_createtime = year + "-" + month + "-" + day + " " + Hours + ":" + Minutes + ":" + Seconds;
          return s_createtime;
        }
      },
      tableRowClassName({ row, rowIndex }) {
        if (
          row.postinfono === "008" ||
          row.postinfono === "029" ||
          row.postinfono === "075"
        ) {
          return "success-row";
        } else if (
          row.postinfono === "012" ||
          row.postinfono === "065"
        ) {
          return "warning-row";
        } else if (
          row.postinfono === "040"
        ) {
          return "error-row";
        } else if (
          row.postinfono === "173" ||
          row.postinfono === "179" ||
          row.postinfono === "190"
        ) {
          return "niubi-row";
        }
        return "";
      },
      getData() {
        axios.get("/xm?all={[all]}").then(res => {
          this.tableData = res.data;
        });
      }
    }
  });


</script>

<style>
    body {
        display: inline-block;
    }

    .el-table .warning-row {
        background: oldlace;
    }

    .el-table .success-row {
        background: #f0f9eb;
    }

    .el-table .error-row {
        background: #ffc3c3;
    }

    .el-table .niubi-row {
        background: #e9f8ff;
    }
</style>
</body>
</html>

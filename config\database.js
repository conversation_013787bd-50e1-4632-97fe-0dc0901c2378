'use strict';

/**
 * 统一数据库配置模块
 * 提供MySQL数据库的基础配置和环境特定的优化配置
 */

// 基础数据库连接配置
const baseConfig = {
  host: '************',
  port: '11436',
  user: 'root',
  password: 'wangcong',
  database: 'zz',
  charset: 'utf8mb4',
};

// 默认连接池配置
const defaultPoolConfig = {
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  connectionLimit: 10, // 默认连接数
};

// 开发环境优化配置
const developmentConfig = {
  ...defaultPoolConfig,
  connectionLimit: 5, // 开发环境减少连接数
};

// 生产环境优化配置
const productionConfig = {
  ...defaultPoolConfig,
  connectionLimit: 20, // 生产环境增加连接数
  acquireTimeout: 30000, // 生产环境缩短获取连接超时时间
};

/**
 * 根据环境获取MySQL配置
 * @param {string} env - 环境名称 (local, dev, prod)
 * @param {object} customConfig - 自定义配置覆盖
 * @returns {object} MySQL配置对象
 */
function getMySQLConfig(env = 'local', customConfig = {}) {
  let poolConfig;
  
  switch (env) {
    case 'prod':
    case 'production':
      poolConfig = productionConfig;
      break;
    case 'dev':
    case 'development':
      poolConfig = developmentConfig;
      break;
    default:
      poolConfig = defaultPoolConfig;
  }

  return {
    client: {
      ...baseConfig,
      ...poolConfig,
      ...customConfig.client,
    },
    app: customConfig.app !== undefined ? customConfig.app : true,
    agent: customConfig.agent !== undefined ? customConfig.agent : false,
  };
}

/**
 * 获取本地环境MySQL配置
 */
function getLocalMySQLConfig(customConfig = {}) {
  return getMySQLConfig('local', customConfig);
}

/**
 * 获取开发环境MySQL配置
 */
function getDevelopmentMySQLConfig(customConfig = {}) {
  return getMySQLConfig('dev', customConfig);
}

/**
 * 获取生产环境MySQL配置
 */
function getProductionMySQLConfig(customConfig = {}) {
  return getMySQLConfig('prod', customConfig);
}

module.exports = {
  baseConfig,
  defaultPoolConfig,
  developmentConfig,
  productionConfig,
  getMySQLConfig,
  getLocalMySQLConfig,
  getDevelopmentMySQLConfig,
  getProductionMySQLConfig,
};

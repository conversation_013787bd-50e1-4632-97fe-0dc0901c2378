'use strict';

/**
 * Agent扩展 - 自定义文件监听逻辑
 * 使用chokidar实现更高效的文件变更检测
 */

const chokidar = require('chokidar');
const path = require('path');
const fs = require('fs');

module.exports = {
  /**
   * 初始化高效文件监听器
   */
  initOptimizedWatcher() {
    if (this.config.env === 'prod') {
      return; // 生产环境不启用文件监听
    }

    const config = this.config.watcher || {};
    const watchConfig = {
      // chokidar配置选项
      ignored: config.ignore || [
        '**/node_modules/**',
        '**/*.log',
        '**/logs/**',
        '**/run/**',
        '**/typings/**',
        'app/public/**',
        'app/view/**',
        '**/.git/**',
        '**/coverage/**',
        '**/test/**',
      ],
      persistent: true,
      ignoreInitial: true,
      followSymlinks: false,
      cwd: this.config.baseDir,
      disableGlobbing: false,
      usePolling: false, // 使用原生事件，性能更好
      interval: 100, // 轮询间隔（仅在usePolling为true时有效）
      binaryInterval: 300,
      alwaysStat: false,
      depth: 99,
      awaitWriteFinish: {
        stabilityThreshold: 50, // 文件稳定时间，减少到50ms
        pollInterval: 25, // 轮询间隔，减少到25ms
      },
    };

    // 监听的目录
    const watchPaths = config.directories || [
      'app/controller',
      'app/service',
      'app/middleware',
      'app/extend',
      'app/router.js',
      'config',
    ];

    console.log('🔥 [文件监听] 启动优化的文件监听器...');
    console.log(`📁 监听目录: ${watchPaths.join(', ')}`);

    // 创建chokidar监听器
    const watcher = chokidar.watch(watchPaths, watchConfig);

    // 性能统计
    const stats = {
      totalChanges: 0,
      reloadCount: 0,
      lastReloadTime: Date.now(),
      changeHistory: [],
    };

    // 防抖处理 - 避免频繁重启
    let reloadTimer = null;
    const RELOAD_DELAY = 50; // 50ms防抖延迟，提升响应速度

    const triggerReload = (filePath, eventType) => {
      if (reloadTimer) {
        clearTimeout(reloadTimer);
      }

      reloadTimer = setTimeout(() => {
        const now = Date.now();
        const reloadTime = now - stats.lastReloadTime;
        stats.lastReloadTime = now;
        stats.reloadCount++;

        console.log(`🔄 [文件监听] 触发热重载 #${stats.reloadCount} - ${reloadTime}ms`);
        console.log(`📝 变更文件: ${filePath} (${eventType})`);

        // 记录热重载日志
        this.recordHotReload(filePath, eventType, reloadTime);

        // 发送重载消息给master进程
        this.messenger.sendToApp('egg-watcher', {
          path: filePath,
          type: eventType,
          reloadTime,
          timestamp: new Date().toISOString(),
        });

        // 发送给所有worker进程
        this.messenger.sendToAppWorker('egg-watcher', {
          path: filePath,
          type: eventType,
          reloadTime,
          timestamp: new Date().toISOString(),
        });
      }, RELOAD_DELAY);
    };

    // 监听文件变更事件
    watcher
      .on('change', (filePath) => {
        stats.totalChanges++;
        stats.changeHistory.push({
          path: filePath,
          type: 'change',
          timestamp: Date.now(),
        });

        // 只保留最近50个变更记录
        if (stats.changeHistory.length > 50) {
          stats.changeHistory = stats.changeHistory.slice(-50);
        }

        console.log(`📝 [文件监听] 文件变更: ${filePath}`);
        triggerReload(filePath, 'change');
      })
      .on('add', (filePath) => {
        stats.totalChanges++;
        console.log(`➕ [文件监听] 文件新增: ${filePath}`);
        triggerReload(filePath, 'add');
      })
      .on('unlink', (filePath) => {
        stats.totalChanges++;
        console.log(`🗑️ [文件监听] 文件删除: ${filePath}`);
        triggerReload(filePath, 'unlink');
      })
      .on('addDir', (dirPath) => {
        console.log(`📁 [文件监听] 目录新增: ${dirPath}`);
      })
      .on('unlinkDir', (dirPath) => {
        console.log(`📁 [文件监听] 目录删除: ${dirPath}`);
      })
      .on('error', (error) => {
        console.error('❌ [文件监听] 监听器错误:', error);
      })
      .on('ready', () => {
        console.log('✅ [文件监听] 监听器初始化完成');
        console.log(
          `📊 监听配置: 防抖${RELOAD_DELAY}ms, 稳定性阈值${watchConfig.awaitWriteFinish.stabilityThreshold}ms`,
        );
      });

    // 保存监听器实例和统计信息
    this.optimizedWatcher = watcher;
    this.watcherStats = stats;

    // 优雅关闭
    this.beforeClose(() => {
      if (this.optimizedWatcher) {
        console.log('🔥 [文件监听] 关闭监听器...');
        this.optimizedWatcher.close();
      }
    });
  },

  /**
   * 记录热重载日志
   */
  recordHotReload(filePath, eventType, reloadTime) {
    const logPath = path.join(this.config.baseDir, 'run', 'hot-reload-log.json');
    const logDir = path.dirname(logPath);

    // 确保目录存在
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const logEntry = {
      reloadTime,
      timestamp: new Date().toISOString(),
      changedFiles: [filePath],
      type: eventType,
      agent: 'chokidar-optimized',
    };

    let logs = [];
    if (fs.existsSync(logPath)) {
      try {
        logs = JSON.parse(fs.readFileSync(logPath, 'utf8'));
      } catch (e) {
        logs = [];
      }
    }

    logs.push(logEntry);

    // 只保留最近100次热重载记录
    if (logs.length > 100) {
      logs = logs.slice(-100);
    }

    fs.writeFileSync(logPath, JSON.stringify(logs, null, 2));
  },

  /**
   * 获取文件监听统计信息
   */
  getWatcherStats() {
    if (!this.watcherStats) {
      return null;
    }

    return {
      totalChanges: this.watcherStats.totalChanges,
      reloadCount: this.watcherStats.reloadCount,
      lastReloadTime: this.watcherStats.lastReloadTime,
      recentChanges: this.watcherStats.changeHistory.slice(-10),
      isActive: this.optimizedWatcher ? !this.optimizedWatcher.closed : false,
    };
  },
};

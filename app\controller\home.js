'use strict';

const axios = require('axios');
const { dateNow } = require('../extend/helper');
const fs = require('fs');
const Controller = require('egg').Controller;
const yaml = require('yaml');

class HomeController extends Controller {
  async index() {
    const { ctx } = this;
    // console.log('home');
    ctx.body = 'hi, egg - 性能监控测试!';
  }

  async gitpull() {
    const ctx = this.ctx;
    ctx.body = 'smys';
  }

  async showip() {
    const { ctx } = this;
    ctx.body = ctx.request.ip;
  }

  // 测试模块懒加载统计
  async moduleStats() {
    const { ctx, app } = this;
    try {
      const stats = app.getModuleStats();
      ctx.body = {
        success: true,
        data: stats,
        message: '模块加载统计信息',
      };
    } catch (error) {
      ctx.body = {
        success: false,
        error: error.message,
        message: '获取模块统计失败',
      };
    }
  }

  // 测试懒加载模块
  async testLazyLoad() {
    const { ctx, app } = this;
    try {
      console.log('🧪 开始测试模块懒加载...');

      // 测试加载OpenAI模块
      const openai = await app.loadModule('openai');
      console.log('✅ OpenAI模块加载成功');

      // 测试加载Sharp模块
      const sharp = await app.loadModule('sharp');
      console.log('✅ Sharp模块加载成功');

      const stats = app.getModuleStats();

      ctx.body = {
        success: true,
        data: {
          loadedModules: stats.loadedModules,
          totalLoads: stats.totalLoads,
          loadTimes: stats.loadTimes,
          totalMemoryUsage: stats.totalMemoryUsage,
          averageLoadTime: stats.averageLoadTime,
        },
        message: '懒加载测试完成',
      };
    } catch (error) {
      ctx.body = {
        success: false,
        error: error.message,
        message: '懒加载测试失败',
      };
    }
  }

  // 获取集群状态信息
  async clusterStats() {
    const { ctx, app } = this;
    try {
      const clusterStats = app.getClusterStats();
      const ipcStats = app.ipcOptimizer.getStats();

      ctx.body = {
        success: true,
        data: {
          cluster: clusterStats,
          ipc: ipcStats,
          environment: app.config.env,
          eggEnvironment: app.config.eggEnv,
        },
        message: '集群状态信息',
      };
    } catch (error) {
      ctx.body = {
        success: false,
        error: error.message,
        message: '获取集群状态失败',
      };
    }
  }

  // 测试进程间通信
  async testIPC() {
    const { ctx, app } = this;
    try {
      console.log('🧪 开始测试进程间通信...');

      // 发送测试消息
      app.ipcOptimizer.queueMessage('test-message', {
        from: process.pid,
        timestamp: Date.now(),
        data: 'IPC测试消息',
      });

      // 广播消息
      app.broadcastToWorkers('test-broadcast', {
        message: '广播测试',
        timestamp: Date.now(),
      });

      const ipcStats = app.ipcOptimizer.getStats();

      ctx.body = {
        success: true,
        data: ipcStats,
        message: 'IPC测试完成',
      };
    } catch (error) {
      ctx.body = {
        success: false,
        error: error.message,
        message: 'IPC测试失败',
      };
    }
  }

  // 获取生产环境监控信息
  async productionMonitor() {
    const { ctx, app } = this;
    try {
      // 获取生产监控中间件的统计信息
      const { getMonitoringStats } = require('../middleware/production-monitor');
      const monitoringStats = getMonitoringStats();

      // 获取系统信息
      const systemInfo = {
        pid: process.pid,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      };

      ctx.body = {
        success: true,
        data: {
          monitoring: monitoringStats,
          system: systemInfo,
          environment: app.config.env,
          timestamp: new Date().toISOString(),
        },
        message: '生产环境监控信息',
      };
    } catch (error) {
      ctx.body = {
        success: false,
        error: error.message,
        message: '获取生产监控信息失败',
      };
    }
  }

  // 健康检查端点
  async healthCheck() {
    const { ctx, app } = this;
    try {
      const memUsage = process.memoryUsage();
      const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
      const uptime = process.uptime();

      // 简单的健康检查逻辑
      const isHealthy = heapUsageRatio < 0.9 && uptime > 10; // 堆内存使用率<90% 且运行时间>10秒

      ctx.status = isHealthy ? 200 : 503;
      ctx.body = {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: Math.round(uptime),
        memoryUsage: {
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
          heapUsageRatio: Math.round(heapUsageRatio * 100), // %
        },
        pid: process.pid,
      };
    } catch (error) {
      ctx.status = 503;
      ctx.body = {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  async clashsw() {
    const { ctx } = this;
    let sw = ctx.query.sw;
    const res = await ctx.service.xr.update(
      'sw',
      {
        id: 3,
        sw: +sw,
      },
      {
        where: {
          name: 'clash',
        },
      },
    );
    // console.log(res);
    ctx.body = await ctx.service.xr.find('sw', { name: 'clash' });
  }

  async clashsw1() {
    const { ctx, app } = this;
    let clientIp = ctx.ip;
    const ua = ctx.get('user-agent');
    console.log(ua);
    let sw1 = await app.redis.get('clashsw');
    sw1 = +sw1;
    await app.redis.set('clashsw', sw1 === 0 ? 1 : 0);
    let sw2 = await app.redis.get('clashsw');
    let text = `${clientIp}访问了clashsw1:\n当前状态${+sw2 === 0 ? '关' : '开'}`;
    await ctx.service.feishu.fs(text);
    ctx.body = { message: +sw2 };
  }

  async showclashconf() {
    const { ctx, app } = this;
    let type = ctx.query.type || 0;
    let o = ctx.query.o || 'android';
    let local = ctx.query.local || false;
    let ua = ctx.get('user-agent');
    let ua1 = ua === 'xctcc' || ua.match('ClashMetaForAndroid');
    // console.log(ua1);

    let sw1 = await app.redis.get('clashsw');
    sw1 = +sw1;
    let clientIp = ctx.ip;
    let text = `${clientIp}访问了clash:\n当前状态${sw1 === 0 ? '关' : '开'}\nua是${ua}`;
    await ctx.service.feishu.fs(text);

    if (sw1 === 0 && ua1 === false) {
      ctx.body = { message: '什么意思' };
      return;
    }

    if (type === `v`) {
      let content = fs.readFileSync(`/home/<USER>/xray.txt`, 'utf-8');
      ctx.body = `${content}`;
      return;
    }
    let filepath = '/home/<USER>/1.yaml';
    let filepath1 = '/home/<USER>/dns.yaml';
    if (type === 'fakeip') {
      filepath1 = '/home/<USER>/fakeip.yaml';
    } else if (type === 'lt') {
      filepath1 = '/home/<USER>/lt.yaml';
    } else if (type === 'yd') {
      filepath1 = '/home/<USER>/yd.yaml';
    }
    let filepath2 = '/home/<USER>/rules.yaml';
    const raw = fs.readFileSync(filepath2, 'utf8');
    const doc = yaml.parse(raw);
    if (+local === 1) {
      doc.rules.unshift('RULE-SET,unicom,DIRECT', 'RULE-SET,cmcc,yd');
    } else if (+local === 2) {
      doc.rules.unshift('RULE-SET,unicom,lt', 'RULE-SET,cmcc,DIRECT');
    } else {
      doc.rules.unshift('RULE-SET,unicom,DIRECT', 'RULE-SET,cmcc,DIRECT');
    }
    // 4. 转回 YAML 文本
    //    yaml.stringify 默认会自动处理缩进和换行
    const newYaml = yaml.stringify(doc);
    let sql = `select *
               from sw
               where name = 'clash'`;
    let res = await ctx.service.xr.query(sql);
    let sw = res[0].sw;
    if (sw !== 0) {
      // 读取path
      let fs = require('fs');
      // console.log(data);
      let content1 = fs.readFileSync(filepath, 'utf-8');
      let content2 = fs.readFileSync(filepath1, 'utf-8');
      let content3 = newYaml;
      ctx.body = `${content1}\n${content2}\n${content3}`;
    }
  }

  async caiyun() {
    const { ctx, app } = this;
    const params = {
      latitude: '24.509954',
      longitude: '117.65646',
      isLocated: true,
      locationKey: 'weathercn:*********',
      days: 15,
      appKey: 'weather20151024',
      sign: 'zUFJoAR2ZVrDy1vF3D07',
      romVersion: 'V10.0.1.0.OAACNFH',
      appVersion: '10010002',
      alpha: false,
      isGlobal: false,
      device: 'gemini',
      modDevice: '',
      locale: 'zh_cn',
    };

    // 请求头
    const headers = {
      'User-Agent': 'okhttp/3.12.12',
      'Accept-Encoding': 'gzip',
    };
    await axios.get('https://weatherapi.market.xiaomi.com/wtr-v3/weather/all', {
      params,
      headers,
      http2: true,
    });

    const params1 = {
      latitude: 24.509954,
      longitude: 117.65646,
      locale: 'zh_cn',
      isGlobal: false,
      appKey: 'weather20151024',
      locationKey: 'weathercn:*********',
      sign: 'zUFJoAR2ZVrDy1vF3D07',
    };

    await axios.get('https://weatherapi.market.xiaomi.com/wtr-v3/weather/xm/forecast/minutely', {
      params1,
      headers,
      http2: true,
    });
  }

  async button() {
    const { ctx, app } = this;
  }

  async testSocket() {
    const { ctx } = this;
    await ctx.render('test-socket.html');
  }

  async buttonindex() {
    console.log('namespace /buttonindex: client connected');
    // 可发送欢迎消息：this.ctx.socket.emit('reply', 'hello from /buttonindex');
  }

  async sendMsg() {
    const key = this.ctx.args[0].key;
    console.log('received key:', key);
    // 发回给客户端
    this.ctx.socket.emit('key', key);
  }
  /**
   * GitHub Webhook 自动部署 - 简化版本
   */
  async githubWebhook() {
    const { ctx } = this;

    try {
      ctx.logger.info('收到部署请求，开始自动部署...');

      // 发送开始部署通知
      await ctx.service.feishu.fsText(
        `🚀 开始自动部署\n\n` +
          `时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n` +
          `触发方式: Webhook`,
        '🔄 自动部署开始',
      );

      // 异步执行部署，避免超时
      setImmediate(async () => {
        try {
          const { spawn } = require('child_process');
          const deployProcess = spawn('bash', ['/home/<USER>/update.sh'], {
            cwd: '/home/<USER>',
            stdio: 'pipe',
          });

          let output = '';

          deployProcess.stdout.on('data', (data) => {
            output += data.toString();
            ctx.logger.info('部署输出:', data.toString().trim());
          });

          deployProcess.stderr.on('data', (data) => {
            output += data.toString();
            ctx.logger.warn('部署警告:', data.toString().trim());
          });

          deployProcess.on('close', async (code) => {
            try {
              if (code === 0) {
                await ctx.service.feishu.fsText(
                  `✅ 自动部署成功\n\n` +
                    `完成时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n` +
                    `� 应用已重启并正常运行`,
                  '✅ 部署成功',
                );
              } else {
                await ctx.service.feishu.fsText(
                  `❌ 自动部署失败\n\n` +
                    `失败时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n` +
                    `退出码: ${code}\n` +
                    `错误信息: ${output.slice(-500)}`,
                  '❌ 部署失败',
                );
              }
            } catch (notifyError) {
              ctx.logger.error('发送通知失败:', notifyError);
            }
          });
        } catch (error) {
          ctx.logger.error('部署执行失败:', error);
          try {
            await ctx.service.feishu.fsText(
              `❌ 部署执行失败\n\n` +
                `错误: ${error.message}\n` +
                `时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`,
              '🚨 部署错误',
            );
          } catch (notifyError) {
            ctx.logger.error('发送错误通知失败:', notifyError);
          }
        }
      });

      // 立即返回成功响应
      ctx.status = 200;
      ctx.body = {
        success: true,
        message: '部署已开始',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      ctx.logger.error('Webhook处理失败:', error);

      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '处理失败',
        error: error.message,
      };
    }
  }

  /**
   * Webhook测试路由
   */
  async webhookTest() {
    const { ctx } = this;
    ctx.body = {
      success: true,
      message: 'Webhook路由工作正常',
      timestamp: new Date().toISOString(),
      method: 'GET',
    };
  }
  /**
   * 查询部署状态
   */
  async deploymentStatus() {
    const { ctx } = this;

    try {
      const status = await ctx.service.deployment.getDeploymentStatus();
      ctx.body = {
        success: true,
        data: status,
      };
    } catch (error) {
      ctx.logger.error('查询部署状态失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '查询失败',
        error: error.message,
      };
    }
  }
}

module.exports = HomeController;

# generate-commit.js 调试模式说明

## 🔍 调试模式功能

`generate-commit.js` 脚本内置了调试模式，可以输出大模型的思考过程和详细的调试信息。

## 🚀 启用调试模式

### Windows (PowerShell)
```powershell
# 设置环境变量并运行
$env:DEBUG = "1"; node generate-commit.js

# 或者一行命令
$env:DEBUG="1" && node generate-commit.js
```

### Windows (CMD)
```cmd
# 设置环境变量并运行
set DEBUG=1 && node generate-commit.js
```

### Linux/macOS
```bash
# 设置环境变量并运行
DEBUG=1 node generate-commit.js
```

## 📊 调试模式输出内容

### 1. 大模型响应结构
```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion.chunk",
  "created": 1234567890,
  "model": "Qwen/Qwen3-8B",
  "choices": [
    {
      "index": 0,
      "delta": {
        "content": "生成的内容",
        "reasoning_content": "思考过程"
      }
    }
  ]
}
```

### 2. 思考过程输出
调试模式下会实时显示大模型的思考过程：
- `reasoning_content` - 主要的思考内容
- `explanation` - 解释性内容（备选字段）

### 3. 调试信息保存
调试模式下会保存详细信息到 `commit_debug.json`：
```json
{
  "timestamp": "2025-07-26T07:30:00.000Z",
  "projectType": "egg",
  "workDir": "/path/to/project",
  "rawContent": "大模型生成的原始内容",
  "extractedMessage": "提取的提交消息",
  "safeMessage": "安全处理后的消息",
  "reasoning": "完整的思考过程"
}
```

## 🔧 调试模式特性

### 启用时
- ✅ 显示第一个响应chunk的完整结构
- ✅ 实时输出大模型思考过程
- ✅ 保留 `commit_debug.json` 文件
- ✅ 显示详细的处理步骤

### 关闭时（默认）
- 🔒 只显示最终生成的commit消息
- 🧹 自动清理调试文件
- ⚡ 更简洁的输出

## 📝 使用示例

### 正常模式
```bash
node generate-commit.js
```
输出：
```
🤖 大模型正在生成commit消息...

[功能] 添加用户管理模块

- 新增用户注册和登录接口
- 实现用户权限验证中间件
- 添加用户数据模型和数据库迁移
```

### 调试模式
```bash
DEBUG=1 node generate-commit.js
```
输出：
```
🤖 大模型正在生成commit消息...

第一个响应chunk结构: {
  "id": "chatcmpl-xxx",
  "object": "chat.completion.chunk",
  ...
}

[思考过程实时输出]
根据提供的Git变更，我需要分析...
这是一个EggJS项目的变更...
主要涉及以下几个方面...

[功能] 添加用户管理模块

- 新增用户注册和登录接口
- 实现用户权限验证中间件
- 添加用户数据模型和数据库迁移

📝 大模型生成的原始内容:
---
[完整的原始内容]
---

🔍 提取的原始提交标题: [功能] 添加用户管理模块...
🛡️  安全处理后的提交标题: [功能] 添加用户管理模块...
```

## 🛠️ 调试文件说明

调试模式下会生成以下文件：

| 文件 | 说明 | 保留条件 |
|------|------|----------|
| `changes.diff` | Git变更差异 | 总是清理 |
| `status.txt` | Git状态信息 | 总是清理 |
| `commit_debug.json` | 调试信息 | 调试模式下保留 |
| `commit_error.json` | 错误信息 | 发生错误时保留 |
| `commit_msg.txt` | 提交消息 | 总是清理 |

## 💡 使用建议

### 何时使用调试模式
- 🐛 **排查问题** - 当commit消息生成异常时
- 🔍 **了解过程** - 想了解大模型的思考过程
- ⚙️ **优化提示词** - 调整prompt时验证效果
- 📊 **性能分析** - 分析响应时间和内容质量

### 注意事项
- 📁 调试文件会占用额外空间
- 🔒 调试信息可能包含敏感的项目信息
- ⏱️ 调试模式输出更多，执行时间稍长

## 🔗 相关文件

- `generate-commit.js` - 主脚本
- `commit_debug.json` - 调试信息（调试模式）
- `.gitignore` - 已配置忽略调试文件

---

💡 **提示**: 调试模式对于理解和优化commit消息生成过程非常有用，特别是在需要调整大模型行为时。

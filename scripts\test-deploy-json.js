#!/usr/bin/env node

// 测试部署通知JSON格式

const axios = require('axios');

const BASE_URL = 'http://localhost:7001';
// const BASE_URL = 'https://egg.wcy9.com';

// 测试不同的JSON格式
const testCases = [
  {
    name: '正确的JSON格式',
    data: {
      projectName: "Vue3前端项目",
      environment: "production",
      version: "main",
      branch: "main",
      commitHash: "347b31b163730cad58efcb14a6cf393fef7273c8",
      commitMessage: "update",
      deployTime: "2025-07-24 10:16:35",
      deployDuration: "2分钟30秒",
      deployedBy: "github-actions",
      serverUrl: "https://vue.wcy9.com",
      buildSize: "2.5MB",
      features: [
        "🎨 Vue3 + Vite 构建",
        "📦 pnpm 包管理",
        "⚡ 缓存优化"
      ],
      notes: "GitHub Actions自动部署"
    }
  },
  {
    name: '包含特殊字符的JSON',
    data: {
      projectName: "Vue3前端项目",
      commitMessage: 'feat: 添加"新功能"和\'优化\'性能',
      deployTime: "2025-07-24 10:16:35",
      notes: "包含引号和换行符\n的测试"
    }
  },
  {
    name: '最小JSON格式',
    data: {
      projectName: "测试项目"
    }
  }
];

// 模拟GitHub Actions可能发送的错误JSON
const errorCases = [
  {
    name: '截断的JSON',
    rawData: '{"projectName": "Vue3前端项目", "deployTime": "2025-07-24'
  },
  {
    name: '未转义的引号',
    rawData: '{"projectName": "Vue3前端项目", "commitMessage": "feat: 添加"新功能""}'
  },
  {
    name: '多余的逗号',
    rawData: '{"projectName": "Vue3前端项目", "environment": "production",}'
  }
];

async function testValidJSON(testCase) {
  console.log(`\n🧪 测试: ${testCase.name}`);
  
  try {
    const response = await axios.post(`${BASE_URL}/api/deploy/success`, testCase.data, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'curl/7.68.0'
      },
      timeout: 5000
    });
    
    console.log(`✅ 请求成功 - 状态码: ${response.status}`);
    console.log(`响应: ${JSON.stringify(response.data, null, 2)}`);
    
  } catch (error) {
    console.error(`❌ 请求失败:`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error(`错误: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.error(`错误: ${error.message}`);
    }
  }
}

async function testInvalidJSON(errorCase) {
  console.log(`\n🚨 测试错误格式: ${errorCase.name}`);
  console.log(`原始数据: ${errorCase.rawData}`);
  
  try {
    const response = await axios.post(`${BASE_URL}/api/deploy/success`, errorCase.rawData, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'curl/7.68.0'
      },
      timeout: 5000
    });
    
    console.log(`⚠️ 意外成功 - 状态码: ${response.status}`);
    console.log(`响应: ${JSON.stringify(response.data, null, 2)}`);
    
  } catch (error) {
    console.log(`✅ 正确拒绝了错误格式:`);
    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.log(`网络错误: ${error.message}`);
    }
  }
}

async function testGitHubActionsFormat() {
  console.log(`\n🔧 测试GitHub Actions格式:`);
  
  // 模拟GitHub Actions的curl命令
  const githubData = {
    projectName: "Vue3前端项目",
    environment: "production",
    version: "main",
    branch: "main", 
    commitHash: "347b31b163730cad58efcb14a6cf393fef7273c8",
    commitMessage: "update",
    deployTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
    deployDuration: "2分钟30秒",
    deployedBy: "github-actions",
    serverUrl: "https://vue.wcy9.com",
    buildSize: "2.5MB",
    features: [
      "🎨 Vue3 + Vite 构建",
      "📦 pnpm 包管理",
      "⚡ 缓存优化",
      "🔧 自动权限设置"
    ],
    notes: "通过GitHub Actions自动化部署到生产环境"
  };
  
  console.log('发送数据:', JSON.stringify(githubData, null, 2));
  
  try {
    const response = await axios.post(`${BASE_URL}/api/deploy/success`, githubData, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'curl/7.68.0'
      },
      timeout: 5000
    });
    
    console.log(`✅ GitHub Actions格式测试成功`);
    console.log(`状态码: ${response.status}`);
    console.log(`响应: ${JSON.stringify(response.data, null, 2)}`);
    
  } catch (error) {
    console.error(`❌ GitHub Actions格式测试失败:`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error(`错误: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.error(`错误: ${error.message}`);
    }
  }
}

function generateCurlCommand() {
  console.log(`\n📋 正确的curl命令示例:`);
  
  const data = {
    projectName: "Vue3前端项目",
    environment: "production",
    version: "main",
    branch: "main",
    commitHash: "347b31b163730cad58efcb14a6cf393fef7273c8",
    commitMessage: "update",
    deployTime: "2025-07-24 10:16:35",
    deployDuration: "2分钟30秒",
    deployedBy: "github-actions",
    serverUrl: "https://vue.wcy9.com",
    buildSize: "2.5MB",
    features: [
      "🎨 Vue3 + Vite 构建",
      "📦 pnpm 包管理"
    ],
    notes: "GitHub Actions自动部署"
  };
  
  console.log(`curl -X POST "${BASE_URL}/api/deploy/success" \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -d '${JSON.stringify(data)}'`);
  
  console.log(`\n🔧 GitHub Actions中的正确格式:`);
  console.log(`- 使用双引号包围JSON`);
  console.log(`- 转义内部的双引号`);
  console.log(`- 避免在JSON中使用单引号`);
  console.log(`- 确保所有字符串都正确闭合`);
}

async function main() {
  console.log('🔍 部署通知JSON格式测试工具');
  console.log('服务器地址:', BASE_URL);
  console.log('测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('═'.repeat(60));
  
  // 测试正确的JSON格式
  for (const testCase of testCases) {
    await testValidJSON(testCase);
  }
  
  // 测试错误的JSON格式
  for (const errorCase of errorCases) {
    await testInvalidJSON(errorCase);
  }
  
  // 测试GitHub Actions格式
  await testGitHubActionsFormat();
  
  // 生成正确的curl命令
  generateCurlCommand();
  
  console.log('\n📊 测试总结:');
  console.log('1. 确保JSON格式正确，所有字符串都有闭合引号');
  console.log('2. 在GitHub Actions中正确转义特殊字符');
  console.log('3. 使用文件方式可以避免shell转义问题');
  console.log('4. 服务器端已增强错误处理和调试信息');
  
  console.log('\n🎉 测试完成!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testValidJSON, testInvalidJSON, testGitHubActionsFormat };

const Controller = require('egg').Controller;
const axios = require('axios');
const xjti = 'eGlwdl9pTDhWQVZLUXVNdXpQdjJKZ0pxeUEw';

class Min<PERSON>ontroller extends Controller {
  async index() {
    const ctx = this.ctx;
    let timu1 = [];
    //言语理解与表达moduleId887708524941938688
    //判断推理moduleId887708629606600704
    //数量关系moduleId887708686896599040
    //资料分析moduleId887708760070426624
    //公共基础知识moduleId887281791193321472
    let type = ctx.query.type ? ctx.query.type : 1;
    // console.log(type);
    type = +type;
    let t = {};
    if (type === 1) {
      t.moduleId = '887281791193321472';
      t.exerciseName = '公共基础知识';
    } else if (type === 2) {
      t.moduleId = '887708524941938688';
      t.exerciseName = '言语理解与表达';
    } else if (type === 3) {
      t.moduleId = '887708629606600704';
      t.exerciseName = '判断推理';
    } else if (type === 4) {
      t.moduleId = '887708686896599040';
      t.exerciseName = '数量关系';
    } else if (type === 5) {
      t.moduleId = '887708760070426624';
      t.exerciseName = '资料分析';
    }
    // console.log(t);
    console.time('api_request');
    let zxurl =
      'http://api01.dfwl100.com/api/v1/qwm/qwmStudentExercise/knowledgeTopics2';
    let res = await axios
      .get(zxurl, {
        auth: {
          username: 'Basic d2ViU2l0ZToxMjM0NTY=',
          password: 'app4android:123456',
        },
        headers: {
          'X-JTI': xjti,
        },
        params: {
          accountId: 3,
          userId: 978310856272003072,
          projectId: 45,
          subjectId: '886301986897530880',
          difficultLevel: 0,
          moduleId: t.moduleId,
          stuPara: 40,
          isForcedEnd: 0,
          timeLength: 15,
          exerciseName: t.exerciseName,
        },
      })
      .then((result) => {
        // console.log(result.data.qwmQuestionList.length);
        timu1 = result.data.qwmQuestionList;
        return result.data.qwmQuestionList;
      });
    // let check = await ctx.service.xr.find('min', {questionId: "954035267306524672"})
    // ctx.body = check;
    //
    // console.log(check);
    // ctx.body=res;
    console.timeEnd('api_request');
    // return;
    let i = 0;
    for (let item of res) {
      let x = {};
      x.questionId = item.questionId;
      x.content = item.content;
      x.referenceAnswer = item.referenceAnswer;
      x.referenceAnalysis = item.referenceAnalysis;
      x.subjectName = item.subjectName;
      x.moduleName = item.moduleName;
      x.firstKnowledgeName = item.firstKnowledgeName;
      x.secondKnowledgeName = item.secondKnowledgeName;
      x.thirdKnowledgeName = item.thirdKnowledgeName;
      x.correctRate = item.correctRate;
      if (type === 5) {
        x.materialContent = item.materialContent;
        x.parentQuestionId = item.parentQuestionId;
      }
      x.A =
        item.qwmQuestionOptions[0].optionsName +
        item.qwmQuestionOptions[0].optionsValue;
      x.B =
        item.qwmQuestionOptions[1].optionsName +
        item.qwmQuestionOptions[1].optionsValue;
      x.C =
        item.qwmQuestionOptions[2].optionsName +
        item.qwmQuestionOptions[2].optionsValue;
      x.D =
        item.qwmQuestionOptions[3].optionsName +
        item.qwmQuestionOptions[3].optionsValue;

      let check = await ctx.service.xr.find('min', {
        questionId: x.questionId,
      });
      if (check.data) {
      } else {
        await ctx.service.xr.create('min', x);
        i++;
      }

      // console.log(x)
      // ctx.body = x;
    }
    let q =
      'select count(questionId) as total from min  where moduleName = "' +
      t.exerciseName +
      '"';
    let total = await ctx.service.xr.query(q);
    // ctx.body = {length: i, total: total};
    console.log('题目数：' + total[0].total);
    ctx.body = { total: total[0].total, data: timu1 };
  }

  async indexgwy() {
    const ctx = this.ctx;

    //言语理解与表达moduleId887708524941938688
    //判断推理moduleId887708629606600704
    //数量关系moduleId887708686896599040
    //资料分析moduleId887708760070426624
    //公共基础知识moduleId887281791193321472
    let type = ctx.query.type ? ctx.query.type : 1;
    // console.log(type);
    type = +type;
    let t = {};
    if (type === 1) {
      t.moduleId = '15';
      t.exerciseName = '常识判断';
    } else if (type === 2) {
      t.moduleId = '16';
      t.exerciseName = '言语理解与表达';
    } else if (type === 3) {
      t.moduleId = '17';
      t.exerciseName = '判断推理';
    } else if (type === 4) {
      t.moduleId = '18';
      t.exerciseName = '数量关系';
    } else if (type === 5) {
      t.moduleId = '19';
      t.exerciseName = '资料分析';
    }
    // console.log(t);
    let zxurl =
      'https://api01.dfwl100.com/api/v1/qwm/qwmStudentExercise/moduleTopics';
    let res = await axios
      .get(zxurl, {
        auth: {
          username: 'Basic YXBwNGFuZHJvaWQ6MTIzNDU2',
          password: 'app4android:123456',
        },
        headers: {
          'X-JTI': xjti,
        },
        params: {
          accountId: 3,
          userId: 978310856272003072,
          projectId: 42,
          subjectId: '9',
          difficultLevel: 0,
          moduleId: t.moduleId,
          stuPara: 5,
          isForcedEnd: 0,
          timeLength: 15,
          exerciseName: t.exerciseName,
        },
      })
      .then((result) => {
        // console.log(result.data.qwmQuestionList.length);
        return result.data.qwmQuestionList;
      });
    // let check = await ctx.service.xr.find('min', {questionId: "954035267306524672"})
    // ctx.body = check;
    //
    // console.log(check);
    // return;
    let i = 0;
    for (let item of res) {
      let x = {};
      x.questionId = item.questionId;
      x.content = item.content;
      x.referenceAnswer = item.referenceAnswer;
      x.referenceAnalysis = item.referenceAnalysis;
      x.subjectName = item.subjectName;
      x.moduleName = item.moduleName;
      x.firstKnowledgeName = item.firstKnowledgeName;
      x.secondKnowledgeName = item.secondKnowledgeName;
      x.thirdKnowledgeName = item.thirdKnowledgeName;
      if (type === 5) {
        x.materialContent = item.materialContent;
        x.parentQuestionId = item.parentQuestionId;
      }
      x.A =
        item.qwmQuestionOptions[0].optionsName +
        item.qwmQuestionOptions[0].optionsValue;
      x.B =
        item.qwmQuestionOptions[1].optionsName +
        item.qwmQuestionOptions[1].optionsValue;
      x.C =
        item.qwmQuestionOptions[2].optionsName +
        item.qwmQuestionOptions[2].optionsValue;
      x.D =
        item.qwmQuestionOptions[3].optionsName +
        item.qwmQuestionOptions[3].optionsValue;

      let check = await ctx.service.xr.find('mingwy', {
        questionId: x.questionId,
      });
      if (check.data) {
      } else {
        await ctx.service.xr.create('mingwy', x);
        i++;
      }

      // ctx.body = x;
    }
    let q =
      'select count(questionId) as total from mingwy  where moduleName = "' +
      t.exerciseName +
      '"';
    let total = await ctx.service.xr.query(q);
    // ctx.body = {length: i, total: total};
    ctx.body = total[0].total;
  }

  async tree() {
    const ctx = this.ctx;
    const all = ctx.query.all;
    let tree = [];
    let zxurl =
      'http://api01.dfwl100.com/api/v1/basic/knowledges/getKnowledgeTreeApp?projectId=45&subjectId=886301986897530880&accountId=3&userId=965919884246532096&difficultLevel=0';

    let res = await axios
      .get(zxurl, {
        auth: {
          username: 'Basic YXBwNGFuZHJvaWQ6MTIzNDU2',
          password: 'app4android:123456',
        },
        headers: {
          'X-JTI': xjti,
        },
        params: {
          projectId: 45,
          subjectId: 886301986897530880,
          accountId: 3,
          userId: 965919884246532096,
          difficultLevel: 0,
        },
      })
      .then((result) => {
        // console.log(result.data.qwmQuestionList.length);
        return result.data;
      });

    let i = 0;
    for (let item of res) {
      tree[i] = {};
      tree[i].objectId = item.objectId;
      tree[i].objectName = item.objectName;
      tree[i].questionCount = item.questionCount;
      // let j = 0;
      // for (let item1 of item.children) {
      //     console.log(item1)
      //     tree[i].children = [];
      //     tree[i].children[j].knowledgeId = item1.knowledgeId;
      //     tree[i].children[j].knowledgeName = item1.knowledgeName;
      //     j++;
      // }
      i++;
    }

    if (all) {
      ctx.body = res;
      return;
    }
    ctx.body = tree;
  }

  async timu() {
    const ctx = this.ctx;
    // let num = ctx.query.num / 1 ? ctx.query.num / 1 : 1;
    let page = ctx.query.page / 1 ? ctx.query.page / 1 : 1;
    let per = ctx.query.per / 1 ? ctx.query.per / 1 : 10;
    let moduleName = ctx.query.m ? ctx.query.m : '';
    let firstKnowledgeName = ctx.query.f ? ctx.query.f : '';
    let secondKnowledgeName = ctx.query.s ? ctx.query.s : '';
    let tk = ctx.query.tk ? ctx.query.tk : 'min';
    let showanswer = ctx.query.a ? ctx.query.a : '';
    let showbutton = ctx.query.b ? ctx.query.b : '';
    let showjson = ctx.query.j ? ctx.query.j : '';
    let onlyan = ctx.query.oa ? ctx.query.oa : '';
    let z = ctx.query.z ? ctx.query.z : '';
    let off = +page === 1 ? 0 : (page - 1) * per;
    let ids = [];
    let w = {};
    let finish = '';
    let pagetotal;
    // console.log(ctx.query)
    if (moduleName) {
      w.moduleName = moduleName;
    }
    if (firstKnowledgeName) {
      w.firstKnowledgeName = firstKnowledgeName;
      let total = await ctx.service.xr.query(
        'SELECT count(questionId) as total FROM `min` where `firstKnowledgeName` = "' +
          firstKnowledgeName +
          '" ',
      );
      finish = await this.refinishf(firstKnowledgeName);
      for (let item of total) {
        pagetotal = item.total;
      }
    }
    if (secondKnowledgeName) {
      w.secondKnowledgeName = secondKnowledgeName;
      let total = await ctx.service.xr.query(
        'SELECT count(questionId) as total FROM `min` where `secondKnowledgeName` = "' +
          secondKnowledgeName +
          '" ',
      );
      finish = await this.refinishs(secondKnowledgeName);
      for (let item of total) {
        pagetotal = item.total;
      }
    }

    let data = await ctx.service.xr.select(tk, {
      where: w,
      orders: [['id', 'asc']],
      limit: per, // 返回数据量
      offset: off, // 数据偏移量
    });
    // console.log(data)

    let i = per * (page - 1) + 1;
    for (let item of data.data) {
      // console.log(item)
      item.content = item.content.replace('>', '>' + i + '.');
      item.A = item.A.replace('<p>', '').replace('</p>', '').replace('A', 'A.');
      item.B = item.B.replace('<p>', '').replace('</p>', '').replace('B', 'B.');
      item.C = item.C.replace('<p>', '').replace('</p>', '').replace('C', 'C.');
      item.D = item.D.replace('<p>', '').replace('</p>', '').replace('D', 'D.');
      item.referenceAnswer = item.referenceAnswer
        .replace('<p>', '')
        .replace('</p>', '')
        .replace('<br>', '');
      item.referenceAnalysis = item.referenceAnalysis
        .replace(/<p>/g, '')
        .replace(/<\/p>/g, '')
        .replace(/<br>/g, '');
      item.zhengque = item[item.referenceAnswer];
      if (item.referenceAnalysis.match('选项A')) {
        item.referenceAnalysis = item.referenceAnalysis
          .replace('选项A', '<br>选项A')
          .replace('选项B', '<br>选项B')
          .replace('选项C', '<br>选项C')
          .replace('选项D', '<br>选项D');
      } else {
        item.referenceAnalysis = item.referenceAnalysis
          .replace('A', '<br>选项A')
          .replace('B', '<br>选项B')
          .replace('C', '<br>选项C')
          .replace('D', '<br>选项D');
      }

      item.pagetotal = pagetotal;
      item.finish = finish;
      i++;
      if (
        item.referenceAnswer === 'A' ||
        item.referenceAnswer === 'B' ||
        item.referenceAnswer === 'C' ||
        item.referenceAnswer === 'D' ||
        item.referenceAnswer
      ) {
        ids.push(item);
      }
    }
    // console.log(ids)
    if (showjson) {
      ctx.body = ids;
      return;
    }
    if (z) {
      await ctx.render('min/timu1', {
        data: ids,
        showanswer: showanswer,
        per: parseInt(per),
        page: page,
        moduleName: moduleName,
        firstKnowledgeName: firstKnowledgeName,
        secondKnowledgeName: secondKnowledgeName,
        showbutton: showbutton,
        onlyan: onlyan,
      });
      return;
    }
    await ctx.render('min/timu', {
      data: ids,
      showanswer: showanswer,
      per: parseInt(per),
      page: page,
      moduleName: moduleName,
      firstKnowledgeName: firstKnowledgeName,
      secondKnowledgeName: secondKnowledgeName,
      showbutton: showbutton,
      onlyan: onlyan,
    });
  }

  async mincha() {
    const { ctx } = this;
    // let d = dateNow();
    await ctx.render('min/cha');
  }

  async cha() {
    const ctx = this.ctx;

    let timu = ctx.query.timu;

    let data = await ctx.service.xr.query(
      "SELECT * FROM min WHERE min.content LIKE '%" + timu + "%'\n" + 'limit 1',
    );
    let i = 1;
    for (let item of data) {
      item.content = item.content.replace('>', '>' + i + '.');
      item.A = item.A.replace('<p>', '').replace('</p>', '').replace('A', 'A.');
      item.B = item.B.replace('<p>', '').replace('</p>', '').replace('B', 'B.');
      item.C = item.C.replace('<p>', '').replace('</p>', '').replace('C', 'C.');
      item.D = item.D.replace('<p>', '').replace('</p>', '').replace('D', 'D.');
      i++;
      // ids.push(item)
    }
    ctx.body = data;
    // await ctx.render('min/timu', {
    //     data: data,
    // })
  }

  async tiku() {
    const ctx = this.ctx;
    await ctx.render('min/tiku', {});
  }

  async get() {
    const ctx = this.ctx;
    // let num = ctx.query.num / 1 ? ctx.query.num / 1 : 1;
    let moduleName = ctx.query.m ? ctx.query.m : '';
    let firstKnowledgeName = ctx.query.f ? ctx.query.f : '';
    let secondKnowledgeName = ctx.query.s ? ctx.query.s : '';
    let tk = ctx.query.tk ? ctx.query.tk : 'min';
    let ids = [];
    let w = {};
    if (moduleName) {
      w.moduleName = moduleName;
    }
    if (firstKnowledgeName) {
      w.firstKnowledgeName = firstKnowledgeName;
    }
    if (secondKnowledgeName) {
      w.secondKnowledgeName = secondKnowledgeName;
    }

    // let data = await ctx.service.xr.select(tk, {
    //     where: w,
    //     orders: [[
    //         'questionId', 'desc'
    //     ]],
    //     limit: num, // 返回数据量
    //     offset: 0, // 数据偏移量
    // });

    let data = await ctx.service.xr.query(
      'SELECT * FROM `min` where `moduleName` = "言语理解与表达" and `display` is null order by rand() limit 1',
    );
    // console.log(data)
    let i = 1;
    for (let item of data) {
      item.content = item.content.replace('>', '>' + i + '.');
      item.A = item.A.replace('<p>', '').replace('</p>', '').replace('A', 'A.');
      item.B = item.B.replace('<p>', '').replace('</p>', '').replace('B', 'B.');
      item.C = item.C.replace('<p>', '').replace('</p>', '').replace('C', 'C.');
      item.D = item.D.replace('<p>', '').replace('</p>', '').replace('D', 'D.');
      i++;
      ids.push(item);
    }
    ctx.body = ids;
  }

  async getgj() {
    const ctx = this.ctx;
    let num = ctx.query.num / 1 ? ctx.query.num / 1 : 1;
    let moduleName = ctx.query.m ? ctx.query.m : '';
    let firstKnowledgeName = ctx.query.f ? ctx.query.f : '';
    let secondKnowledgeName = ctx.query.s ? ctx.query.s : '';
    let tk = ctx.query.tk ? ctx.query.tk : 'min';
    let ids = [];
    let w = {};
    if (moduleName) {
      w.moduleName = moduleName;
    }
    if (firstKnowledgeName) {
      w.firstKnowledgeName = firstKnowledgeName;
    }
    if (secondKnowledgeName) {
      w.secondKnowledgeName = secondKnowledgeName;
    }

    // let data = await ctx.service.xr.select(tk, {
    //     where: w,
    //     orders: [[
    //         'questionId', 'desc'
    //     ]],
    //     limit: num, // 返回数据量
    //     offset: 0, // 数据偏移量
    // });

    let data = await ctx.service.xr.query(
      'SELECT * FROM `min` where `moduleName` = "公共基础知识" and `display` is null order by rand() limit 1',
    );
    // console.log(data)
    let i = 1;
    for (let item of data) {
      item.content = item.content.replace('>', '>' + i + '.');
      item.A = item.A.replace('<p>', '').replace('</p>', '').replace('A', 'A.');
      item.B = item.B.replace('<p>', '').replace('</p>', '').replace('B', 'B.');
      item.C = item.C.replace('<p>', '').replace('</p>', '').replace('C', 'C.');
      item.D = item.D.replace('<p>', '').replace('</p>', '').replace('D', 'D.');
      i++;
      ids.push(item);
    }
    ctx.body = ids;
  }

  async navjson() {
    const ctx = this.ctx;
    const rows = await this.app.mysql.query(`
            SELECT moduleName,
                   GROUP_CONCAT(DISTINCT firstKnowledgeName 
                 SEPARATOR ',') AS firstKnowledgeNames
            FROM min
            GROUP BY moduleName
        `);

    const data = {};
    for (const row of rows) {
      const { moduleName, firstKnowledgeNames } = row;
      const firstKnowledgeList = firstKnowledgeNames.split(',');

      data[moduleName] = {};
      for (const firstKnowledge of firstKnowledgeList) {
        const secondKnowledgeList = Array.from(
          await this.app.mysql.query(
            'SELECT DISTINCT secondKnowledgeName FROM min WHERE firstKnowledgeName = ?',
            [firstKnowledge],
          ),
        ).map((row) => row.secondKnowledgeName);

        data[moduleName][firstKnowledge] = secondKnowledgeList;
      }
    }

    ctx.body = data;

    // await ctx.render('min/nav', {})
  }

  async nav() {
    const ctx = this.ctx;

    const sql =
      'SELECT distinct moduleName, firstKnowledgeName FROM min order by moduleName asc';
    const result = await this.app.mysql.query(sql);

    const jsonResult = {};

    result.forEach((row) => {
      const moduleName = row.moduleName;
      const firstKnowledgeName = row.firstKnowledgeName;

      if (!jsonResult[moduleName]) {
        jsonResult[moduleName] = [];
      }

      jsonResult[moduleName].push(firstKnowledgeName);
    });

    ctx.body = jsonResult;
  }

  async refinish() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    let st = '';
    if (ctx.query.m === 'm') {
      st = await this.refinishm(text);
    }
    if (ctx.query.m === 'f') {
      st = await this.refinishf(text);
    }
    if (ctx.query.m === 's') {
      st = await this.refinishs(text);
    }
    ctx.body = { st: st };
  }

  async refinishm(moduleName) {
    const ctx = this.ctx;
    const sql =
      "select count(choice)  as count from min where moduleName='" +
      moduleName +
      "';";
    const sql1 =
      "select count(content)  as count from min where moduleName='" +
      moduleName +
      "';";
    const result = await this.app.mysql.query(sql);
    const result1 = await this.app.mysql.query(sql1);
    return '(' + result[0].count + '/' + result1[0].count + ')';
  }

  async refinishf(firstKnowledgeName) {
    const ctx = this.ctx;
    const sql =
      "select count(choice)  as count from min where firstKnowledgeName='" +
      firstKnowledgeName +
      "';";
    const sql1 =
      "select count(content)  as count from min where firstKnowledgeName='" +
      firstKnowledgeName +
      "';";
    const result = await this.app.mysql.query(sql);
    const result1 = await this.app.mysql.query(sql1);
    return '(' + result[0].count + '/' + result1[0].count + ')';
  }

  async refinishs(secondKnowledgeName) {
    const ctx = this.ctx;
    const sql =
      "select count(choice)  as count from min where secondKnowledgeName='" +
      secondKnowledgeName +
      "';";
    const sql1 =
      "select count(content)  as count from min where secondKnowledgeName='" +
      secondKnowledgeName +
      "';";
    const result = await this.app.mysql.query(sql);
    const result1 = await this.app.mysql.query(sql1);
    return '(' + result[0].count + '/' + result1[0].count + ')';
  }

  async nav1() {
    const ctx = this.ctx;

    const sql =
      'SELECT distinct moduleName, firstKnowledgeName, secondKnowledgeName FROM min order by moduleName asc';
    const result = await this.app.mysql.query(sql);

    // console.log(result);
    const jsonResult = {};

    for (const row of result) {
      let moduleName = row.moduleName;
      let firstKnowledgeName = row.firstKnowledgeName;
      let secondKnowledgeName = row.secondKnowledgeName;
      //
      // const m = await this.refinishm(moduleName);
      // moduleName = moduleName + m;
      //
      // const f = await this.refinishf(firstKnowledgeName);
      // firstKnowledgeName = firstKnowledgeName + f;
      //
      // const s = await this.refinishs(secondKnowledgeName);
      // secondKnowledgeName = secondKnowledgeName + s;

      if (!jsonResult[moduleName]) {
        jsonResult[moduleName] = [];
      }

      // 检查是否已存在相同的firstKnowledgeName，如果存在则将secondKnowledgeName添加到对应的数组中
      const existingItem = jsonResult[moduleName].find(
        (item) => item.firstKnowledgeName === firstKnowledgeName,
      );

      if (existingItem) {
        existingItem.secondKnowledgeName.push(secondKnowledgeName);
      } else {
        // 创建新的对象来保存firstKnowledgeName和secondKnowledgeName数组
        jsonResult[moduleName].push({
          firstKnowledgeName,
          secondKnowledgeName: [secondKnowledgeName],
        });
      }
    }

    ctx.body = jsonResult;
  }

  async nav12() {
    const ctx = this.ctx;

    const sql =
      'SELECT distinct moduleName, firstKnowledgeName, secondKnowledgeName FROM min';
    const result = await this.app.mysql.query(sql);

    const jsonResult = {};

    for (const row of result) {
      let moduleName = row.moduleName;
      let firstKnowledgeName = row.firstKnowledgeName;
      let secondKnowledgeName = row.secondKnowledgeName;

      if (!jsonResult[moduleName]) {
        jsonResult[moduleName] = [];
      }

      const existingItem = jsonResult[moduleName].find(
        (item) => item.name === firstKnowledgeName,
      );

      if (existingItem) {
        existingItem.child.push({ name: secondKnowledgeName, name1: '' });
      } else {
        jsonResult[moduleName].push({
          name: firstKnowledgeName,
          name1: '',
          child: [{ name: secondKnowledgeName, name1: '' }],
        });
      }
    }

    ctx.body = jsonResult;
  }

  async update() {
    const ctx = this.ctx;
    let obj = this.ctx.request.body;
    // console.log(obj);
    if (obj.id) {
      // x.show = 1;
      // // res.push(x);
      // let options = {
      //     where: {
      //         questionId: obj.id / 1
      //     }
      // };
      //
      // console.log(x);
      // console.log(options);
      // ctx.body = {id: obj.id / 1};
      let cr = await ctx.service.xr.query(
        'update min set `display`=0 where sid=' + obj.id,
      );
      ctx.body = cr;
    }
  }

  async minupdatezhenti() {
    const ctx = this.ctx;
    let obj = this.ctx.request.body;
    if (obj.id) {
      let cr = await ctx.service.xr.query(
        'update min set `zhenti`=1   where sid=' + obj.id,
      );
      ctx.body = cr;
    }
  }

  async cuoti() {
    const ctx = this.ctx;
    let obj = this.ctx.request.body;
    if (obj.id) {
      if (obj.duile === 1) {
        let cr = await ctx.service.xr.query(
          'update min set `error`=0   where sid=' + obj.id,
        );
        ctx.body = cr;
      } else {
        let cr = await ctx.service.xr.query(
          'update min set `error`=1   where sid=' + obj.id,
        );
        ctx.body = cr;
      }
    }
  }

  async pageinfo() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    let data = await ctx.service.xrplus.page(url);
    // data[0] = url;
    ctx.body = data;
  }

  async pages() {
    const ctx = this.ctx;
    let url = 'https://www.xr05.xyz/XiuRen/10671.html';
    let data = await ctx.service.xrplus.page(url);
    // data[0] = url;
    let res = data;
    ctx.body = res;
  }

  async info() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    // let url = "https://www.xr05.xyz/XiuRen/10671.html"
    let data = await ctx.service.xrplus.imgs(url);
    let res = data;
    ctx.body = res;
  }

  async imgs() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    let pd = ctx.query.pd;
    // let url = "https://www.1y.is/chinese/no-609-well.html"
    // let url = "https://www.xr05.xyz/XiuRen/10679.html"
    // let data = await ctx.service.xrplus.img(url,"202205");
    let data = await ctx.service.xrplus.img(url, pd);
    let res = data;
    ctx.body = res;
  }

  async updatesid() {
    const ctx = this.ctx;
    let total = await ctx.service.xr.query(
      'UPDATE min SET sid = CAST(questionId AS CHAR(255)) where sid is null',
    );
    ctx.body = total;
  }

  async card() {
    //UPDATE min SET sid = CAST(questionId AS CHAR(255)) where sid is null;
    const ctx = this.ctx;
    let total = await ctx.service.xr.query(
      'SELECT count(questionId) as total FROM `min` where `moduleName` = "言语理解与表达" and `display` is null',
    );
    for (let item of total) {
      total = item.total;
    }
    // console.log(total);

    await ctx.render('min/card', { total: total });
  }

  async cardgj() {
    const ctx = this.ctx;
    let total = await ctx.service.xr.query(
      'SELECT count(sid) as total FROM `min` where `moduleName` = "公共基础知识" and `display` is null',
    );
    for (let item of total) {
      total = item.total;
    }
    // console.log(total);

    await ctx.render('min/gj', { total: total });
  }

  async cardall() {
    const { ctx } = this;
    const { s, o, e } = ctx.query;
    if (s) {
      const [total] = await ctx.service.xr.query(`SELECT COUNT(sid) AS total
                                                        FROM min
                                                        WHERE firstKnowledgeName = '${s}'
                                                          AND display IS NULL`);
      await ctx.render('min/alltimu', {
        s,
        total: total.total,
        o,
        e,
      });
    } else {
      await ctx.render('min/all', { o });
    }
  }

  async cardalljson() {
    const { ctx } = this;
    const { nav, s, o, e } = ctx.query;
    if (nav) {
      const glist = await ctx.service.xr.query(
        "SELECT DISTINCT firstKnowledgeName FROM min where moduleName = '公共基础知识'",
      );
      const ylist = await ctx.service.xr.query(
        "SELECT DISTINCT firstKnowledgeName FROM min where moduleName = '言语理解与表达'",
      );
      const data = [...glist, ...ylist].map(({ firstKnowledgeName }) => ({
        value: firstKnowledgeName,
        label: firstKnowledgeName,
      }));
      ctx.body = data;
      return;
    }
    if (s) {
      let q =
        "SELECT * FROM `min` where `firstKnowledgeName` = '" +
        s +
        "' and `display` is null order by rand() limit 1";
      if (o) {
        q =
          "SELECT * FROM `min` where `firstKnowledgeName` = '" +
          s +
          "' and `display` is null limit 1";
      }
      if (e) {
        q =
          "SELECT * FROM `min` where `firstKnowledgeName` = '" +
          s +
          "' and error=1 limit 1";
      }

      let data = await ctx.service.xr.query(q);
      ctx.body = data.map((item, index) => ({
        ...item,
        content: item.content.replace('>', `>${index + 1}.`),
        A: item.A.replace(/<\/?p>/g, '').replace('A', 'A.'),
        B: item.B.replace(/<\/?p>/g, '').replace('B', 'B.'),
        C: item.C.replace(/<\/?p>/g, '').replace('C', 'C.'),
        D: item.D.replace(/<\/?p>/g, '').replace('D', 'D.'),
      }));
    }
    if (o) {
      const allist = ['政治', '经济', '法律', '管理', '片段阅读'];
      const alist = allist.map((item) => ({ value: item, label: item }));
      ctx.body = alist;
      return;
    }
  }

  async intimu() {
    const ctx = this.ctx;
    let obj = ctx.request.body;
    let lastid = ctx.query.id ? ctx.query.id : 0;
    if (!(lastid === 0)) {
      let check = await ctx.service.xr.query(
        "SELECT questionId FROM min2022 WHERE thirdKnowledgeName = '5000' AND moduleName = '言语理解' ORDER BY questionId DESC LIMIT 1;\n",
      );
      console.log(check);
      ctx.body = {
        lastid: check[0].questionId,
      };
      return;
    }
    let x = {};
    x.questionId = obj.ID;
    x.content = obj.timu;
    x.referenceAnswer = obj.zhengque;
    x.referenceAnalysis = obj.jiexi;
    x.moduleName = obj.moduleName;
    x.thirdKnowledgeName = '5000';
    x.A = obj.A;
    x.B = obj.B;
    x.C = obj.C;
    x.D = obj.D;
    let check = await ctx.service.xr.find('min2022', {
      questionId: x.questionId,
    });
    if (check.data) {
      ctx.status = 500;
      ctx.body = { data: '已经存在' };
    } else {
      let cr = await ctx.service.xr.create('min2022', x);
      console.log(cr);
      ctx.body = cr;
    }
  }

  async an() {
    const ctx = this.ctx;
    let s = ctx.query.s;
    let c = ctx.query.c;
    let f = ctx.query.f;
    let m = ctx.query.m;
    let where = {};
    if (m) {
      where = {
        moduleName: m,
      };
    }
    if (f) {
      where = {
        firstKnowledgeName: f,
      };
    }
    if (s) {
      where = {
        // secondKnowledgeName:[ "哲学","哲学基础理论","哲学基本理论"]
        secondKnowledgeName: s,
      };
    }

    let data = await ctx.service.xr.select('min', {
      where: where,
      orders: [['questionId', 'desc']],
      // limit: 20, // 返回数据量
      // offset: 1, // 数据偏移量
    });
    data = data.data;
    let ans = [];
    let an = '';
    let characterCount = 0;

    function re(ans) {
      ans = ans
        .substring(1)
        .replace(/<p>/g, '')
        .replace(/<\/p>/g, '')
        .replace(/&ldquo;/g, '')
        .replace(/&rdquo/g, '')
        .replace(/&ldquo;/g, '')
        .replace(/&rdquo/g, '');
      return ans;
    }

    if (c) {
      for (let item of data) {
        if (item.referenceAnswer === 'A') {
          let obj = [re(item.B), re(item.C), re(item.D)];
          ans.push(obj);
        }
        if (item.referenceAnswer === 'B') {
          let obj = [re(item.A), re(item.C), re(item.D)];
          ans.push(obj);
        }
        if (item.referenceAnswer === 'C') {
          let obj = [re(item.A), re(item.B), re(item.D)];
          ans.push(obj);
        }
        if (item.referenceAnswer === 'D') {
          let obj = [re(item.A), re(item.B), re(item.C)];
          ans.push(obj);
        }
      }
      ans = ans.filter(Boolean); // 删除空数组元素
      ctx.body = ans;
      return;
    }
    let index = 1;
    for (let item of data) {
      if (
        item.referenceAnswer === 'A' ||
        item.referenceAnswer === 'B' ||
        item.referenceAnswer === 'C' ||
        item.referenceAnswer === 'D'
      ) {
        let a = item[item.referenceAnswer];
        a = a
          .substring(1)
          .replace('<p>', '')
          .replace('</p>', '')
          .replace('&ldquo;', '')
          .replace('&rdquo', '')
          .replace('&ldquo;', '')
          .replace('&rdquo', '');
        if (characterCount + a.length > 2000) {
          an = an.trim() + '\n';
          characterCount = 0;
        }
        an += a + '。';
        let o = {
          content:
            index +
            '.' +
            item.content
              .replace(/<p>/g, '')
              .replace(/<\/p>/g, '')
              .replace(/<br>/g, ''),
          answer: item.referenceAnswer + '.' + a.trim(),
        };
        index++;
        ans.push(o);
        characterCount += a.length;
      }
    }
    ans = ans.filter(Boolean); // 删除空数组元素
    // if (s) {
    //     ctx.body = an;
    //     return;
    // }
    await ctx.render('min/an1', { data: ans });
    return;
    // ctx.body = ans;
  }

  async addid() {
    const ctx = this.ctx;
    let x = [];
    let data = await ctx.service.xr.select('min', {
      columns: ['questionId', 'sid'], // 要查询的表字段
      orders: [['questionId', 'desc']],
    });
    data = data.data;
    for (let item in data) {
      console.log(item);
      console.log(data[item].sid);
      let options = {
        where: {
          sid: data[item].sid,
        },
      };
      let up = await ctx.service.xr.query(
        'update min set id = ' + item + ' where sid =' + data[item].sid,
      );
      console.log(up);
    }
  }

  async choice() {
    const ctx = this.ctx;
    let x = ctx.request.body;
    let up = await ctx.service.xr.query(
      'update min set choice = ' + x.choice + ' where id =' + x.id,
    );
    ctx.body = up;
  }
}

module.exports = MinController;

const axios = require('axios');
const { dateNow } = require('../extend/helper');
/**
 * @param {Egg.Application} app - egg application
 */
module.exports = {
  schedule: {
    interval: '62s', // 1 分钟间隔
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['local'],
    immediate: true,
    enabled: false,
  },
  async task(ctx) {
    // let ip = await axios.get("http://ip.wcy9.com/cnip.php");
    // ip = ip.data.toString("utf8"); // 将二进制数据转换为字符串
    // console.log(ip);
    // console.log(dateNow());
    // const text = ip;
    let sql = `select *
               from sw
               where name = 'timu'`;
    let res = await ctx.service.xr.query(sql);
    let sw = res[0]?.sw;
    if (sw !== 0) {
      // await axios
      //   .get(
      //     'http://127.0.0.1:7001/getjstimu?id=656600&limit=100&mode=syzc&biao=fbsy&biaocate=fbsycate&biaofullcate=fbsyfullcate',
      //   )
      //   .then((res) => {
      //     console.log('返回的数据', res.data);
      //     console.log(dateNow());
      //   })
      //   .catch((e) => {
      //     if (e) {
      //     }
      //   });
      // await axios
      //   .get(
      //     'http://127.0.0.1:7001/getjstimu?id=48642&limit=100&mode=xingce&biao=fbgwy&biaocate=fbgwycate&biaofullcate=fbgwyfullcate',
      //   )
      //   .then((res) => {
      //     console.log(res.data);
      //     console.log(dateNow());
      //   })
      //   .catch((e) => {
      //     if (e) {
      //     }
      //   });
      // await axios
      //   .get(
      //     'http://127.0.0.1:7001/fbgettimu2?id=48644&limit=100&biao=fbgwyzlfx&biaocate=fbgwycate&mode=xingce',
      //   )
      //   .then((res) => {
      //     // console.log(res.data);
      //     // console.log(dateNow());
      //   })
      //   .catch((e) => {
      //     if (e) {
      //     }
      //   });
      // await axios
      //   .get('http://127.0.0.1:7001/getzlfx?type=syzc&biao=fbsy&limit=100')
      //   .then((res) => {
      //     // console.log(res.data);
      //     // console.log(dateNow());
      //   })
      //   .catch((e) => {
      //     if (e) {
      //     }
      //   });
    }
    // console.log(x);
  },
};

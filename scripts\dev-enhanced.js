#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// 获取项目路径
const eggPath = process.cwd();
const vuePath = path.resolve(eggPath, '../vue');

// 使用 ANSI 颜色代码，更兼容
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// 创建彩色文本函数
function colorText(text, color) {
  return `${color}${text}${colors.reset}`;
}

// 显示标题
console.log(
  colorText(
    `
╔══════════════════════════════════════════════════════════════╗
║                    🚀 开发环境启动器 🚀                      ║
║                                                              ║
║  Vue.js Frontend  +  Egg.js Backend  =  ✨ 全栈开发体验    ║
╚══════════════════════════════════════════════════════════════╝
`,
    colors.cyan + colors.bright,
  ),
);

// 显示启动信息
console.log(colorText('\n📋 启动信息:', colors.cyan + colors.bright));
console.log(
  colorText('  • Vue.js 前端: ', colors.white) +
    colorText('http://localhost:6001', colors.green),
);
console.log(
  colorText('  • Egg.js 后端: ', colors.white) +
    colorText('http://localhost:7001', colors.blue),
);
console.log(
  colorText('  • 时间戳: ', colors.white) +
    colorText(new Date().toLocaleString(), colors.yellow),
);

// 显示帮助信息
console.log(colorText('\n💡 提示:', colors.dim));
console.log(colorText('  • 按 Ctrl+C 停止所有服务', colors.dim));
console.log(colorText('  • 日志会显示不同颜色以区分前后端', colors.dim));
console.log(colorText('  • 任一服务崩溃会自动停止所有服务', colors.dim));

// 简单的加载提示
console.log(
  colorText('\n⏳ 正在启动开发服务器...', colors.magenta + colors.bright),
);

// 延迟一下显示效果
setTimeout(() => {
  console.log(colorText('✅ 开始启动服务!', colors.green + colors.bright));

  console.log(colorText('\n🔧 执行命令:', colors.cyan + colors.bright));
  console.log(colorText(`Vue路径: ${vuePath}`, colors.dim));
  console.log(colorText(`Egg路径: ${eggPath}`, colors.dim));

  // 启动 concurrently（使用已有的脚本）
  const child = spawn('pnpm', ['run', 'dev:all'], {
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      FORCE_COLOR: '1', // 强制启用颜色
    },
  });

  // 处理进程退出
  child.on('close', (code) => {
    if (code === 0) {
      console.log(
        colorText('\n✅ 开发服务器已正常关闭', colors.green + colors.bright),
      );
    } else {
      console.log(
        colorText(
          `\n❌ 开发服务器异常退出，代码: ${code}`,
          colors.red + colors.bright,
        ),
      );
    }
  });

  // 处理错误
  child.on('error', (error) => {
    console.error(
      colorText('\n❌ 启动失败:', colors.red + colors.bright),
      error.message,
    );
  });

  // 优雅关闭处理
  process.on('SIGINT', () => {
    console.log(
      colorText(
        '\n\n🛑 收到关闭信号，正在停止服务器...',
        colors.yellow + colors.bright,
      ),
    );
    child.kill('SIGINT');
  });
}, 1000);

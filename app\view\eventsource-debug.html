<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventSource 调试工具</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            background: #1a1a1a;
            color: #fff;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .debug-section {
            background: #2d2d2d;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .log-box {
            background: #000;
            border: 1px solid #333;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            margin: 10px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
        }
        .status.connected { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.connecting { background: #ff9800; }
        .status.closed { background: #666; }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            background: #333;
            padding: 10px;
            border-radius: 4px;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
        .warning {
            color: #ffd43b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 EventSource 调试工具</h1>
        
        <div class="debug-section">
            <h2>📊 连接状态</h2>
            <div class="info-grid">
                <div class="info-item">
                    <strong>连接状态:</strong>
                    <span id="connectionStatus" class="status connecting">未连接</span>
                </div>
                <div class="info-item">
                    <strong>URL:</strong>
                    <span id="currentUrl">无</span>
                </div>
                <div class="info-item">
                    <strong>ReadyState:</strong>
                    <span id="readyState">-</span>
                </div>
                <div class="info-item">
                    <strong>接收消息数:</strong>
                    <span id="messageCount">0</span>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h2>🎮 控制面板</h2>
            <div>
                <label for="logType">日志类型:</label>
                <select id="logType">
                    <option value="web">Web日志</option>
                    <option value="agent">Agent日志</option>
                    <option value="error">Error日志</option>
                </select>
                
                <button id="connectBtn">连接 EventSource</button>
                <button id="disconnectBtn" disabled>断开连接</button>
                <button id="testFetchBtn">测试 Fetch API</button>
                <button id="clearLogsBtn">清空日志</button>
            </div>
        </div>

        <div class="debug-section">
            <h2>📝 EventSource 日志</h2>
            <div id="eventSourceLog" class="log-box">等待连接...</div>
        </div>

        <div class="debug-section">
            <h2>🌐 Fetch API 测试</h2>
            <div id="fetchLog" class="log-box">点击"测试 Fetch API"按钮开始测试...</div>
        </div>

        <div class="debug-section">
            <h2>🔧 调试信息</h2>
            <div id="debugInfo" class="log-box">
                <div>User-Agent: <span class="success">${navigator.userAgent}</span></div>
                <div>当前时间: <span id="currentTime"></span></div>
                <div>页面URL: <span class="success">${window.location.href}</span></div>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let messageCount = 0;

        // 更新时间
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 日志函数
        function logToEventSource(message, type = 'info') {
            const log = document.getElementById('eventSourceLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            log.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        function logToFetch(message, type = 'info') {
            const log = document.getElementById('fetchLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            log.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        // 更新状态
        function updateStatus(status, text) {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.className = `status ${status}`;
            statusEl.textContent = text;
        }

        function updateReadyState() {
            if (eventSource) {
                const states = {
                    0: 'CONNECTING',
                    1: 'OPEN', 
                    2: 'CLOSED'
                };
                document.getElementById('readyState').textContent = 
                    `${eventSource.readyState} (${states[eventSource.readyState]})`;
            } else {
                document.getElementById('readyState').textContent = '-';
            }
        }

        // EventSource 连接
        function connectEventSource() {
            const logType = document.getElementById('logType').value;
            const url = `/api/logs/${logType}`;
            
            if (eventSource) {
                eventSource.close();
            }

            logToEventSource(`开始连接: ${url}`, 'info');
            document.getElementById('currentUrl').textContent = url;
            
            try {
                eventSource = new EventSource(url);
                
                eventSource.onopen = function(event) {
                    logToEventSource('✅ EventSource 连接成功', 'success');
                    updateStatus('connected', '已连接');
                    updateReadyState();
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                };

                eventSource.onmessage = function(event) {
                    messageCount++;
                    document.getElementById('messageCount').textContent = messageCount;
                    
                    try {
                        const data = JSON.parse(event.data);
                        if (data.line) {
                            logToEventSource(`📥 消息 #${messageCount}: ${data.line.substring(0, 100)}...`, 'success');
                        } else {
                            logToEventSource(`📥 消息 #${messageCount}: ${JSON.stringify(data)}`, 'info');
                        }
                    } catch (e) {
                        logToEventSource(`📥 原始消息 #${messageCount}: ${event.data.substring(0, 100)}...`, 'warning');
                    }
                };

                eventSource.onerror = function(event) {
                    logToEventSource('❌ EventSource 错误', 'error');
                    logToEventSource(`错误详情: ${JSON.stringify({
                        type: event.type,
                        target: event.target ? {
                            url: event.target.url,
                            readyState: event.target.readyState,
                            withCredentials: event.target.withCredentials
                        } : null
                    })}`, 'error');
                    
                    updateStatus('error', '连接错误');
                    updateReadyState();
                    
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                };

                // 定期更新状态
                const statusInterval = setInterval(() => {
                    if (eventSource) {
                        updateReadyState();
                        if (eventSource.readyState === EventSource.CLOSED) {
                            clearInterval(statusInterval);
                        }
                    } else {
                        clearInterval(statusInterval);
                    }
                }, 1000);

            } catch (error) {
                logToEventSource(`❌ 创建 EventSource 失败: ${error.message}`, 'error');
                updateStatus('error', '创建失败');
            }
        }

        // 断开连接
        function disconnectEventSource() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                logToEventSource('🔌 手动断开连接', 'warning');
                updateStatus('closed', '已断开');
                updateReadyState();
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                document.getElementById('currentUrl').textContent = '无';
            }
        }

        // Fetch API 测试
        async function testFetchAPI() {
            const logType = document.getElementById('logType').value;
            const url = `/api/logs/${logType}`;
            
            logToFetch(`开始 Fetch 测试: ${url}`, 'info');
            
            try {
                // 测试普通 GET 请求
                logToFetch('🧪 测试普通 GET 请求...', 'info');
                const response1 = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/plain'
                    }
                });
                
                logToFetch(`普通 GET 响应: ${response1.status} ${response1.statusText}`, 
                    response1.ok ? 'success' : 'error');
                logToFetch(`响应头: ${JSON.stringify(Object.fromEntries(response1.headers.entries()))}`, 'info');
                
                // 测试 EventSource 类型请求
                logToFetch('🧪 测试 EventSource 类型请求...', 'info');
                const response2 = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache'
                    }
                });
                
                logToFetch(`EventSource 类型响应: ${response2.status} ${response2.statusText}`, 
                    response2.ok ? 'success' : 'error');
                logToFetch(`响应头: ${JSON.stringify(Object.fromEntries(response2.headers.entries()))}`, 'info');
                
                if (response2.ok) {
                    // 读取一些数据
                    const reader = response2.body.getReader();
                    const decoder = new TextDecoder();
                    let dataCount = 0;
                    
                    try {
                        while (dataCount < 3) {
                            const { done, value } = await reader.read();
                            if (done) break;
                            
                            const chunk = decoder.decode(value, { stream: true });
                            logToFetch(`📥 接收数据块 #${++dataCount}: ${chunk.substring(0, 100)}...`, 'success');
                        }
                    } finally {
                        reader.cancel();
                    }
                }
                
            } catch (error) {
                logToFetch(`❌ Fetch 测试失败: ${error.message}`, 'error');
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('eventSourceLog').innerHTML = '日志已清空...';
            document.getElementById('fetchLog').innerHTML = '日志已清空...';
            messageCount = 0;
            document.getElementById('messageCount').textContent = '0';
        }

        // 事件监听
        document.getElementById('connectBtn').addEventListener('click', connectEventSource);
        document.getElementById('disconnectBtn').addEventListener('click', disconnectEventSource);
        document.getElementById('testFetchBtn').addEventListener('click', testFetchAPI);
        document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });

        // 初始化
        logToEventSource('EventSource 调试工具已加载', 'info');
        logToFetch('Fetch API 测试工具已加载', 'info');
    </script>
</body>
</html>

function strB(b) {
    if (b < 1000) return b.toString() + 'B';
    if (b < 1000000) return (b / 1000).toFixed(2) + 'KB';
    if (b < 1000000000) return (b / 1000000).toFixed(2) + 'MB';
    if (b < 1000000000000) {
        return (b / 1000000000).toFixed(2) + 'GB';
    } else {
        return (b / 1000000000000).toFixed(2) + 'TB';
    }
}

getlist();

async function getlist() {
    var stats1 = await fetch('/stats/get4', {
        method: 'get',
        headers: {
            'content-type': 'application/json'
        }
    })
        .then(
            res => res.json()
        );
    let i = 0;
    for (const st of stats1.data.data) {
        getdata(st.url, i, st.name)
        i++;
    }
}

async function getd() {
    var stats1 = await fetch('/stats/get1', {
        method: 'post',
        body: JSON.stringify({
            'url': "*************:1036",
            'name': "腾讯香港HK"
        }),
        headers: {
            'content-type': 'application/json'
        }
    })
        .then(
            res => res.json()
        );
}

function getdata(url, i, name) {
    var mem_tooltips = {},
        host_tooltips = {};
    $("tbody").append("           <tr>\n" +
        "                <td id=\"status\">\n" +
        "                    <div class=\"progress\" style=\"margin-bottom: 0 !important;\">\n" +
        "                        <div style=\"width: 100%;    background: linear-gradient(to right, #44ce78 0%,#43ce9f 100%)!important;\"\n" +
        "                             class=\"progress-bar progress-bar-success\"><small>运行</small></div>\n" +
        "                    </div>\n" +
        "                </td>\n" +
        "                <td id=\"jiedian\">" + name + "</td>\n" +
        "                <td id=\"cpu\" style=\"width: 55px\">\n" +
        "                    <div class=\"progress\" style=\"margin-bottom: 0 !important;\">\n" +
        "                        <div id=\"cpu_progress\"\n" +
        "                             style=\"width: 100%;    background: linear-gradient(to right, #44ce78 0%,#43ce9f 100%)!important;\"\n" +
        "                             class=\"progress-bar progress-bar-success\"><small id=\"cpux\" style=\"color:black\">NaN</small>\n" +
        "                        </div>\n" +
        "                    </div>\n" +
        "                </td>\n" +
        "                <td id=\"mem\" style=\"width: 55px\">\n" +
        "                    <div class=\"progress\" style=\"margin-bottom: 0 !important;\">\n" +
        "                        <div id=\"mem_progress\"\n" +
        "                             style=\"width: 100%;    background: linear-gradient(to right, #44ce78 0%,#43ce9f 100%)!important;\"\n" +
        "                             class=\"progress-bar progress-bar-success\"><small id=\"memx\" style=\"color:black\">NaN</small>\n" +
        "                        </div>\n" +
        "                    </div>\n" +
        "                </td>\n" +
        "                <td id=\"uptime\">NaN</td>\n" +
        "                <td id=\"downnet\" style=\"width: 55px\">NaN</td>\n" +
        "                <td id=\"upnet\" style=\"width: 55px\">NaN</td>\n" +
        "                <td id=\"downtotal\">NaN</td>\n" +
        "                <td id=\"uptotal\">NaN</td>\n" +
        "                <td id=\"hostdata\"><i class=\"mdui-icon material-icons\" id=\"host_" + i + "\" class=\"fancy1\">info_outline</i></td>\n" +
        "            </tr>")
    setInterval(async () => {
        var stats = await fetch('/stats/get1', {
            method: 'post',
            body: JSON.stringify({
                'url': url
            }),
            headers: {
                'content-type': 'application/json'
            }
        })
            .then(
                res => res.json()
            );


        var {cpu, mem, net, host} = stats.data;
        let cur = $('tbody tr:eq(' + i + ')');
        if (!(host instanceof Array)) {
            var content =
                `系统: ${host.os}
平台: ${host.platform}
内核版本: ${host.kernelVersion}
内核架构: ${host.kernelArch}
启动: ${new Date(host.bootTime * 1000).toLocaleString()}
在线: ${(host.uptime / 86400).toFixed(2)}天`;
            if (!host_tooltips[sid]) host_tooltips[sid] = new mdui.Tooltip(`#host_` + i, {});
            host_tooltips[sid].$element[0].innerText = content;
        }
        if (!(host instanceof Array)) {
            cur.children('#uptime').text((host.uptime / 86400).toFixed(2) + '天');
        }
        if (cpu instanceof Array) {
            var cpus = [],
                cpu_p = 0;
            for (var x of cpu) cpus.push((x.multi * 100).toFixed(2) + '%'), cpu_p += x.multi;
            cpu_p /= cpu.length;
            E(`cpux`).innerText = cpus.join(' | ');
            E(`cpu_progress`).style.width = `${cpu_p * 100}%`;
        } else {
            // E(`cpux`).innerText = (cpu.multi * 100).toFixed(2) + '%';
            let cpuxx = (cpu.multi * 100).toFixed(2) + '%';
            cur.find('#cpux').text(cpuxx);
            let wd = `${cpu.multi * 100}%`;
            cur.find('#cpu_progress').css('width', wd);
        }
        if (mem instanceof Array) {
            var MEM = [],
                MEM_P = 0;
            for (var x of mem) {
                var {used, total} = x.virtual,
                    usage = used / total;
                MEM.push((usage * 100).toFixed(2) + '%'), MEM_P += usage;
            }
            MEM_P /= mem.length;
            E(`mem`).innerText = MEM.join(' | ');
            console.log(MEM.join(' | '));
            E(`${sid}_MEM_progress`).style.width = `${MEM_P * 100}%`;
        } else {
            var {used, total} = mem.virtual,
                usage = used / total;
            // E(`mem`).innerText = (usage * 100).toFixed(2) + '%';
            let memm = (usage * 100).toFixed(2) + '%';
            cur.find('#memx').text(memm);
            let wd = `${usage * 100}%`;
            cur.find('#mem_progress').css('width', wd);
            // E(`${sid}_MEM_progress`).style.width=`${usage*100}%`;
            var content = `${strB(used)}/${strB(total)}`;
            if (mem_tooltips[sid]) {
                mem_tooltips[sid].$element[0].innerText = content;
            } else {
                mem_tooltips[sid] = new mdui.Tooltip(`#${sid}_MEM_item`, {content});
            }
        }
        if (net instanceof Array) {
            var IN = [],
                OUT = [];
            for (var {delta} of net) IN.push(delta.in), OUT.push(delta.out);
            // E(`downnet`).innerText = strB(Math.min(...IN));
            cur.children('#downnet').text(strB(Math.min(...IN)));
            // E(`upnet`).innerText = strB(Math.min(...OUT));
            cur.children('#upnet').text(strB(Math.min(...OUT)));

            var IN = [],
                OUT = [];
            for (var {total} of net) IN.push(total.in), OUT.push(total.out);
            // E(`downtotal`).innerText = strB(Math.min(...IN));
            cur.find('#downtotal').text(strB(Math.min(...IN)));
            // E(`uptotal`).innerText = strB(Math.min(...OUT));
            cur.find('#uptotal').text(strB(Math.min(...OUT)));
        } else {

            // E(`downnet`).innerText = strB(net.delta.in);
            cur.find('#downnet').text(strB(net.delta.in));
            // E(`upnet`).innerText = strB(net.delta.out);
            cur.find('#upnet').text(strB(net.delta.out));

            // E(`downtotal`).innerText = strB(net.total.in);
            cur.find('#downtotal').text(strB(net.total.in));
            // E(`uptotal`).innerText = strB(net.total.out);
            cur.find('#uptotal').text(strB(net.total.out));
        }

        return;

        for (var [sid, node] of Object.entries(stats)) {
            if (node.stat && node.stat != -1) {
                var {cpu, mem, net, host} = node.stat;
                // console.log(sid,node);

                if (cpu instanceof Array) {
                    var cpus = [],
                        cpu_p = 0;
                    for (var x of cpu) cpus.push((x.multi * 100).toFixed(2) + '%'), cpu_p += x.multi;
                    cpu_p /= cpu.length;
                    E(`${sid}_CPU`).innerText = cpus.join(' | ');
                    E(`${sid}_CPU_progress`).style.width = `${cpu_p * 100}%`;
                } else {
                    E(`${sid}_CPU`).innerText = (cpu.multi * 100).toFixed(2) + '%';
                    E(`${sid}_CPU_progress`).style.width = `${cpu.multi * 100}%`;
                }

                if (mem instanceof Array) {
                    var MEM = [],
                        MEM_P = 0;
                    for (var x of mem) {
                        var {used, total} = x.virtual,
                            usage = used / total;
                        MEM.push((usage * 100).toFixed(2) + '%'), MEM_P += usage;
                    }
                    MEM_P /= mem.length;
                    E(`${sid}_MEM`).innerText = MEM.join(' | ');
                    E(`${sid}_MEM_progress`).style.width = `${MEM_P * 100}%`;
                } else {
                    var {used, total} = mem.virtual,
                        usage = used / total;
                    E(`${sid}_MEM`).innerText = (usage * 100).toFixed(2) + '%';
                    E(`${sid}_MEM_progress`).style.width = `${usage * 100}%`;
                    var content = `${strB(used)}/${strB(total)}`;
                    if (mem_tooltips[sid]) {
                        mem_tooltips[sid].$element[0].innerText = content;
                    } else {
                        mem_tooltips[sid] = new mdui.Tooltip(`#${sid}_MEM_item`, {content});
                    }
                }

                if (net instanceof Array) {
                    var IN = [],
                        OUT = [];
                    for (var {delta} of net) IN.push(delta.in), OUT.push(delta.out);
                    E(`${sid}_NET_IN`).innerText = strB(Math.min(...IN));
                    E(`${sid}_NET_OUT`).innerText = strB(Math.min(...OUT));

                    var IN = [],
                        OUT = [];
                    for (var {total} of net) IN.push(total.in), OUT.push(total.out);
                    E(`${sid}_NET_IN_TOTAL`).innerText = strB(Math.min(...IN));
                    E(`${sid}_NET_OUT_TOTAL`).innerText = strB(Math.min(...OUT));
                } else {
                    E(`${sid}_NET_IN`).innerText = strB(net.delta.in);
                    E(`${sid}_NET_OUT`).innerText = strB(net.delta.out);

                    E(`${sid}_NET_IN_TOTAL`).innerText = strB(net.total.in);
                    E(`${sid}_NET_OUT_TOTAL`).innerText = strB(net.total.out);
                }

                if (!(host instanceof Array)) {
                    var content =
                        `系统: ${host.os}
平台: ${host.platform}
内核版本: ${host.kernelVersion}
内核架构: ${host.kernelArch}
启动: ${new Date(host.bootTime * 1000).toLocaleString()}
在线: ${(host.uptime / 86400).toFixed(2)}天`;
                    console.log(host_tooltips[sid]);
                    console.log(host_tooltips);
                    if (!host_tooltips[sid]) host_tooltips[sid] = new mdui.Tooltip(`#host_` + i, {});
                    host_tooltips[sid].$element[0].innerText = content;
                }
            }
        }
        mdui.mutation();
    }, 1000);

}

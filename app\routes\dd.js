'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  router.get('/tele/dd', controller.dd['dd']);
  router.post('/tele/dd', controller.dd['ddpost']);
  router.get('/tele/dd2', controller.dd['dd2']);
  router.get('/tele/ddm', controller.dd['ddm']);
  router.get('/dd', controller.dd['dd']);
  router.post('/dd', controller.dd['ddpost']);
  router.get('/dd2', controller.dd['dd2']);
  router.get('/ddm', controller.dd['ddm']);
  router.post('/ddup', controller.dd['ddup']);
  router.get('/ddup', controller.dd['ddup']);
};

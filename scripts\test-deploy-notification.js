#!/usr/bin/env node

// 测试部署通知功能

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:7001';

// 测试数据
const successData = {
  projectName: 'Vue前端项目',
  environment: 'production',
  version: 'v1.2.3',
  branch: 'main',
  commitHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
  commitMessage: 'feat: 添加新功能和优化性能',
  deployTime: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
  deployDuration: '2分钟30秒',
  deployedBy: 'GitHub Actions',
  serverUrl: 'https://your-domain.com',
  buildSize: '2.5MB',
  features: [
    '🎨 新增用户管理功能',
    '⚡ 优化页面加载速度',
    '🐛 修复已知bug',
    '📦 更新UI组件库',
    '🔒 增强安全性',
    '📱 响应式设计优化'
  ],
  notes: '本次部署包含重要功能更新，请及时测试验证'
};

const failureData = {
  projectName: 'Vue前端项目',
  environment: 'production',
  branch: 'main',
  commitHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
  failureTime: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
  errorMessage: 'Build failed: Module not found error in src/components/UserManagement.vue',
  deployedBy: 'GitHub Actions',
  logUrl: 'https://github.com/your-org/your-repo/actions/runs/123456789',
  retryCount: 1
};

// 测试函数
async function testDeploySuccess() {
  console.log('🧪 测试部署成功通知...\n');
  
  try {
    const response = await axios.post(`${BASE_URL}/api/deploy/success`, successData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 部署成功通知测试通过');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 部署成功通知测试失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

async function testDeployFailure() {
  console.log('\n🧪 测试部署失败通知...\n');
  
  try {
    const response = await axios.post(`${BASE_URL}/api/deploy/failure`, failureData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 部署失败通知测试通过');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 部署失败通知测试失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

async function testTemplate() {
  console.log('\n🧪 测试获取模板...\n');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/deploy/template`);
    
    console.log('✅ 模板获取测试通过');
    console.log('响应状态:', response.status);
    console.log('模板数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 模板获取测试失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

async function testBuiltInTest() {
  console.log('\n🧪 测试内置测试接口...\n');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/deploy/test`);
    
    console.log('✅ 内置测试通过');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 内置测试失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

// 显示使用说明
function showUsage() {
  console.log('📋 部署通知API使用说明\n');
  
  console.log('🎯 可用的API接口:');
  console.log('1. POST /api/deploy/success  - 发送部署成功通知');
  console.log('2. POST /api/deploy/failure  - 发送部署失败通知');
  console.log('3. GET  /api/deploy/test     - 测试部署通知功能');
  console.log('4. GET  /api/deploy/template - 获取API模板\n');
  
  console.log('🚀 在GitHub Actions中使用:');
  console.log('curl -X POST "https://your-server.com/api/deploy/success" \\');
  console.log('  -H "Content-Type: application/json" \\');
  console.log('  -d \'{"projectName": "Vue项目", "environment": "production", ...}\'');
  
  console.log('\n📝 详细示例请查看: docs/deploy-notification-example.yml');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
    return;
  }
  
  console.log('🎉 部署通知功能测试\n');
  console.log('服务器地址:', BASE_URL);
  console.log('测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('─'.repeat(50));
  
  try {
    if (args.includes('--success') || args.length === 0) {
      await testDeploySuccess();
    }
    
    if (args.includes('--failure') || args.length === 0) {
      await testDeployFailure();
    }
    
    if (args.includes('--template') || args.length === 0) {
      await testTemplate();
    }
    
    if (args.includes('--test') || args.length === 0) {
      await testBuiltInTest();
    }
    
    console.log('\n🎊 所有测试完成!');
    
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  testDeploySuccess,
  testDeployFailure,
  testTemplate,
  testBuiltInTest
};

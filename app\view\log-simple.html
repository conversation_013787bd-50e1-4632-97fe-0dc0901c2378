<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化日志查看器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .log-content {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-line {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            word-break: break-all;
        }
        .log-line:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .log-line.error {
            background-color: rgba(239, 68, 68, 0.1);
            border-left: 3px solid #ef4444;
        }
        .log-line.warn {
            background-color: rgba(245, 158, 11, 0.1);
            border-left: 3px solid #f59e0b;
        }
        .log-line.info {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100">
    <!-- 头部 -->
    <header class="bg-gray-800 shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-white">📋 简化日志查看器</h1>
                <div class="flex items-center space-x-4">
                    <select id="logType" class="bg-gray-700 text-white px-3 py-2 rounded">
                        <option value="web">Web日志</option>
                        <option value="agent">Agent日志</option>
                        <option value="error">Error日志</option>
                    </select>
                    <button id="refreshBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg">
                        🔄 刷新
                    </button>
                    <button id="autoRefreshBtn" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg">
                        ⏰ 自动刷新: 关
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-6">
        <div class="bg-gray-800 rounded-lg shadow-xl">
            <div class="bg-gray-700 px-4 py-3 rounded-t-lg border-b border-gray-600">
                <div class="flex items-center justify-between">
                    <h2 id="logTitle" class="text-lg font-semibold text-white">Web日志</h2>
                    <div class="flex items-center space-x-4">
                        <span id="lastUpdate" class="text-sm text-gray-400">未更新</span>
                        <span id="lineCount" class="text-sm text-gray-400">0 行</span>
                        <button id="clearBtn" class="text-sm text-red-400 hover:text-red-300">清空显示</button>
                    </div>
                </div>
            </div>
            <div id="logContent" class="log-content bg-gray-900 h-96 overflow-y-auto p-4 rounded-b-lg">
                <div class="text-gray-500 text-center py-8">点击刷新按钮加载日志...</div>
            </div>
        </div>

        <!-- 状态信息 -->
        <div class="mt-6 bg-gray-800 rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-2">📊 状态信息</h3>
            <div id="statusInfo" class="text-sm text-gray-400">
                等待加载...
            </div>
        </div>
    </main>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadLogContent();
        });

        function setupEventListeners() {
            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', loadLogContent);

            // 日志类型切换
            document.getElementById('logType').addEventListener('change', function() {
                updateLogTitle();
                loadLogContent();
            });

            // 自动刷新切换
            document.getElementById('autoRefreshBtn').addEventListener('click', toggleAutoRefresh);

            // 清空显示
            document.getElementById('clearBtn').addEventListener('click', clearDisplay);
        }

        function updateLogTitle() {
            const logType = document.getElementById('logType').value;
            const titles = {
                web: 'Web日志 (egg-web.log)',
                agent: 'Agent日志 (egg-agent.log)',
                error: 'Error日志 (common-error.log)'
            };
            document.getElementById('logTitle').textContent = titles[logType];
        }

        async function loadLogContent() {
            const logType = document.getElementById('logType').value;
            const refreshBtn = document.getElementById('refreshBtn');
            
            refreshBtn.textContent = '🔄 加载中...';
            refreshBtn.disabled = true;

            try {
                // 使用fetch直接获取日志数据
                const response = await fetch(`/api/logs/${logType}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/plain'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 读取响应流
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let lines = [];
                let dataReceived = false;

                // 设置超时
                const timeout = setTimeout(() => {
                    reader.cancel();
                    if (!dataReceived) {
                        displayError('请求超时，请检查服务器状态');
                    }
                }, 10000);

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) break;
                        
                        dataReceived = true;
                        clearTimeout(timeout);
                        
                        buffer += decoder.decode(value, { stream: true });
                        
                        // 解析SSE数据
                        const sseLines = buffer.split('\n');
                        buffer = sseLines.pop(); // 保留最后一个可能不完整的行
                        
                        for (const sseLine of sseLines) {
                            if (sseLine.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(sseLine.substring(6));
                                    if (data.line) {
                                        lines.push(data.line);
                                    }
                                } catch (e) {
                                    // 忽略解析错误
                                }
                            }
                        }
                        
                        // 限制行数，只保留最后100行
                        if (lines.length > 100) {
                            lines = lines.slice(-100);
                        }
                        
                        // 如果已经收到足够的数据，停止读取
                        if (lines.length >= 50) {
                            reader.cancel();
                            break;
                        }
                    }
                } catch (readError) {
                    console.log('读取中断:', readError.message);
                }

                clearTimeout(timeout);

                if (lines.length > 0) {
                    displayLogLines(lines);
                    updateStatus(`成功加载 ${lines.length} 行日志`);
                } else {
                    displayError('未获取到日志数据，可能日志文件为空或服务器配置问题');
                }

            } catch (error) {
                console.error('加载日志失败:', error);
                displayError(`加载失败: ${error.message}`);
                updateStatus(`加载失败: ${error.message}`);
            } finally {
                refreshBtn.textContent = '🔄 刷新';
                refreshBtn.disabled = false;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            }
        }

        function displayLogLines(lines) {
            const container = document.getElementById('logContent');
            container.innerHTML = '';

            lines.forEach((line, index) => {
                const logLine = document.createElement('div');
                logLine.className = 'log-line';
                
                // 根据日志内容添加样式
                if (line.toLowerCase().includes('error')) {
                    logLine.classList.add('error');
                } else if (line.toLowerCase().includes('warn')) {
                    logLine.classList.add('warn');
                } else if (line.toLowerCase().includes('info')) {
                    logLine.classList.add('info');
                }

                logLine.textContent = line;
                container.appendChild(logLine);
            });

            document.getElementById('lineCount').textContent = `${lines.length} 行`;
            
            // 滚动到底部
            container.scrollTop = container.scrollHeight;
        }

        function displayError(message) {
            const container = document.getElementById('logContent');
            container.innerHTML = `
                <div class="text-red-400 text-center py-8">
                    <div class="text-lg mb-2">❌ ${message}</div>
                    <div class="text-sm text-gray-500">
                        <p>可能的解决方案:</p>
                        <p>1. 检查服务器是否正常运行</p>
                        <p>2. 确认日志文件是否存在</p>
                        <p>3. 检查网络连接</p>
                    </div>
                </div>
            `;
        }

        function clearDisplay() {
            document.getElementById('logContent').innerHTML = 
                '<div class="text-gray-500 text-center py-8">显示已清空，点击刷新重新加载</div>';
            document.getElementById('lineCount').textContent = '0 行';
        }

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (isAutoRefresh) {
                // 停止自动刷新
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                btn.textContent = '⏰ 自动刷新: 关';
                btn.className = 'px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg';
            } else {
                // 开始自动刷新
                autoRefreshInterval = setInterval(loadLogContent, 5000); // 每5秒刷新
                isAutoRefresh = true;
                btn.textContent = '⏰ 自动刷新: 开';
                btn.className = 'px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg';
            }
        }

        function updateStatus(message) {
            document.getElementById('statusInfo').innerHTML = `
                <div>状态: ${message}</div>
                <div>时间: ${new Date().toLocaleString()}</div>
                <div>自动刷新: ${isAutoRefresh ? '开启' : '关闭'}</div>
            `;
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>

#!/usr/bin/env node

/**
 * 性能报告生成脚本
 * 分析所有性能数据，生成综合性能优化报告
 */

const fs = require('fs');
const path = require('path');

// 报告配置
const REPORT_CONFIG = {
  dataDir: path.join(__dirname, '..', 'run'),
  outputDir: path.join(__dirname, '..', 'reports'),
  templateDir: path.join(__dirname, '..', 'templates'),
};

/**
 * 读取性能数据文件
 */
function readPerformanceData() {
  const data = {};

  // 读取启动性能报告
  const performanceReportPath = path.join(REPORT_CONFIG.dataDir, 'performance-report.json');
  if (fs.existsSync(performanceReportPath)) {
    try {
      data.startupReports = JSON.parse(fs.readFileSync(performanceReportPath, 'utf8'));
    } catch (e) {
      console.warn('读取启动性能报告失败:', e.message);
    }
  }

  // 读取基准测试结果
  const benchmarkPath = path.join(REPORT_CONFIG.dataDir, 'benchmark-results.json');
  if (fs.existsSync(benchmarkPath)) {
    try {
      data.benchmark = JSON.parse(fs.readFileSync(benchmarkPath, 'utf8'));
    } catch (e) {
      console.warn('读取基准测试结果失败:', e.message);
    }
  }

  // 读取热重载日志
  const hotReloadPath = path.join(REPORT_CONFIG.dataDir, 'hot-reload-log.json');
  if (fs.existsSync(hotReloadPath)) {
    try {
      data.hotReload = JSON.parse(fs.readFileSync(hotReloadPath, 'utf8'));
    } catch (e) {
      console.warn('读取热重载日志失败:', e.message);
    }
  }

  // 读取集群配置
  const clusterConfigPath = path.join(REPORT_CONFIG.dataDir, 'cluster-config.json');
  if (fs.existsSync(clusterConfigPath)) {
    try {
      data.clusterConfig = JSON.parse(fs.readFileSync(clusterConfigPath, 'utf8'));
    } catch (e) {
      console.warn('读取集群配置失败:', e.message);
    }
  }

  // 读取Node.js优化配置
  const nodeOptionsPath = path.join(REPORT_CONFIG.dataDir, 'node-options.json');
  if (fs.existsSync(nodeOptionsPath)) {
    try {
      data.nodeOptions = JSON.parse(fs.readFileSync(nodeOptionsPath, 'utf8'));
    } catch (e) {
      console.warn('读取Node.js配置失败:', e.message);
    }
  }

  // 读取健康检查报告
  const healthReportsPath = path.join(REPORT_CONFIG.dataDir, 'health-reports.json');
  if (fs.existsSync(healthReportsPath)) {
    try {
      data.healthReports = JSON.parse(fs.readFileSync(healthReportsPath, 'utf8'));
    } catch (e) {
      console.warn('读取健康检查报告失败:', e.message);
    }
  }

  return data;
}

/**
 * 分析启动性能趋势
 */
function analyzeStartupPerformance(startupReports) {
  if (!startupReports || startupReports.length === 0) {
    return null;
  }

  const startupTimes = startupReports.map((report) => report.totalStartupTime);
  const recent10 = startupTimes.slice(-10);
  const older10 = startupTimes.slice(-20, -10);

  const analysis = {
    total: startupReports.length,
    latest: startupTimes[startupTimes.length - 1],
    average: Math.round(startupTimes.reduce((sum, time) => sum + time, 0) / startupTimes.length),
    min: Math.min(...startupTimes),
    max: Math.max(...startupTimes),
    trend: null,
  };

  // 计算趋势
  if (recent10.length > 0 && older10.length > 0) {
    const recentAvg = recent10.reduce((sum, time) => sum + time, 0) / recent10.length;
    const olderAvg = older10.reduce((sum, time) => sum + time, 0) / older10.length;
    const improvement = olderAvg - recentAvg;

    analysis.trend = {
      improvement: Math.round(improvement),
      percentage: Math.round((improvement / olderAvg) * 100),
      direction: improvement > 0 ? 'improved' : 'degraded',
    };
  }

  return analysis;
}

/**
 * 分析热重载性能
 */
function analyzeHotReloadPerformance(hotReloadLogs) {
  if (!hotReloadLogs || hotReloadLogs.length === 0) {
    return null;
  }

  const reloadTimes = hotReloadLogs.map((log) => log.reloadTime);
  const validTimes = reloadTimes.filter((time) => time < 60000); // 过滤异常值

  if (validTimes.length === 0) {
    return null;
  }

  return {
    total: hotReloadLogs.length,
    validSamples: validTimes.length,
    average: Math.round(validTimes.reduce((sum, time) => sum + time, 0) / validTimes.length),
    min: Math.min(...validTimes),
    max: Math.max(...validTimes),
    recent: hotReloadLogs.slice(-5).map((log) => ({
      time: log.reloadTime,
      files: log.changedFiles,
      timestamp: log.timestamp,
    })),
  };
}

/**
 * 生成优化建议
 */
function generateOptimizationSuggestions(data) {
  const suggestions = [];

  // 启动时间建议
  if (data.startupAnalysis && data.startupAnalysis.latest > 2000) {
    suggestions.push({
      category: '启动性能',
      priority: 'high',
      issue: '启动时间较长',
      suggestion: '考虑进一步优化模块加载和数据库连接',
      current: `${data.startupAnalysis.latest}ms`,
      target: '< 1500ms',
    });
  }

  // 热重载建议
  if (data.hotReloadAnalysis && data.hotReloadAnalysis.average > 5000) {
    suggestions.push({
      category: '热重载',
      priority: 'medium',
      issue: '热重载响应时间较长',
      suggestion: '优化文件监听配置，减少监听范围',
      current: `${data.hotReloadAnalysis.average}ms`,
      target: '< 2000ms',
    });
  }

  // 基准测试建议
  if (data.benchmark && data.benchmark.tests.optimized && data.benchmark.tests.optimized.summary) {
    const optimized = data.benchmark.tests.optimized.summary;
    if (optimized.startup && optimized.startup.average > 1500) {
      suggestions.push({
        category: '基准性能',
        priority: 'medium',
        issue: '基准测试启动时间可以进一步优化',
        suggestion: '考虑使用更激进的优化策略',
        current: `${optimized.startup.average}ms`,
        target: '< 1000ms',
      });
    }
  }

  // 系统配置建议
  if (data.clusterConfig && data.clusterConfig.systemInfo) {
    const { cpuCount, totalMemoryGB } = data.clusterConfig.systemInfo;
    if (cpuCount >= 8 && totalMemoryGB >= 16) {
      suggestions.push({
        category: '系统配置',
        priority: 'low',
        issue: '高性能硬件未充分利用',
        suggestion: '考虑启用Worker Threads处理CPU密集型任务',
        current: `${cpuCount}核 ${totalMemoryGB}GB`,
        target: '启用Worker Threads',
      });
    }
  }

  return suggestions;
}

/**
 * 生成Markdown报告
 */
function generateMarkdownReport(data) {
  const report = [];

  // 报告头部
  report.push('# EggJS 性能优化报告');
  report.push('');
  report.push(`**生成时间:** ${new Date().toLocaleString('zh-CN')}`);
  report.push('');

  // 执行摘要
  report.push('## 📊 执行摘要');
  report.push('');

  if (data.startupAnalysis) {
    const trend = data.startupAnalysis.trend;
    const trendText = trend
      ? `${trend.direction === 'improved' ? '提升' : '下降'} ${Math.abs(trend.improvement)}ms (${Math.abs(trend.percentage)}%)`
      : '暂无趋势数据';

    report.push(
      `- **启动性能:** 当前 ${data.startupAnalysis.latest}ms，平均 ${data.startupAnalysis.average}ms`,
    );
    report.push(`- **性能趋势:** ${trendText}`);
  }

  if (data.hotReloadAnalysis) {
    report.push(
      `- **热重载性能:** 平均 ${data.hotReloadAnalysis.average}ms，最快 ${data.hotReloadAnalysis.min}ms`,
    );
  }

  if (data.benchmark && data.benchmark.tests.optimized && data.benchmark.tests.optimized.summary) {
    const optimized = data.benchmark.tests.optimized.summary;
    report.push(
      `- **基准测试:** 启动 ${optimized.startup.average}ms，成功率 ${optimized.successRate.toFixed(1)}%`,
    );
  } else if (data.benchmark && data.benchmark.tests.optimized) {
    report.push(`- **基准测试:** 测试失败，无有效数据`);
  }

  report.push('');

  // 详细分析
  report.push('## 🔍 详细分析');
  report.push('');

  // 启动性能分析
  if (data.startupAnalysis) {
    report.push('### 启动性能分析');
    report.push('');
    report.push('| 指标 | 数值 |');
    report.push('|------|------|');
    report.push(`| 最新启动时间 | ${data.startupAnalysis.latest}ms |`);
    report.push(`| 平均启动时间 | ${data.startupAnalysis.average}ms |`);
    report.push(`| 最快启动时间 | ${data.startupAnalysis.min}ms |`);
    report.push(`| 最慢启动时间 | ${data.startupAnalysis.max}ms |`);
    report.push(`| 测试次数 | ${data.startupAnalysis.total} |`);
    report.push('');
  }

  // 热重载性能分析
  if (data.hotReloadAnalysis) {
    report.push('### 热重载性能分析');
    report.push('');
    report.push('| 指标 | 数值 |');
    report.push('|------|------|');
    report.push(`| 平均重载时间 | ${data.hotReloadAnalysis.average}ms |`);
    report.push(`| 最快重载时间 | ${data.hotReloadAnalysis.min}ms |`);
    report.push(`| 最慢重载时间 | ${data.hotReloadAnalysis.max}ms |`);
    report.push(`| 重载次数 | ${data.hotReloadAnalysis.total} |`);
    report.push('');
  }

  // 系统配置
  if (data.clusterConfig) {
    report.push('### 系统配置');
    report.push('');
    const config = data.clusterConfig;
    report.push('| 配置项 | 数值 |');
    report.push('|--------|------|');
    report.push(`| CPU核心数 | ${config.systemInfo.cpuCount} |`);
    report.push(`| 总内存 | ${config.systemInfo.totalMemoryGB}GB |`);
    report.push(`| 推荐Worker数 | ${config.clusterConfig.workers} |`);
    report.push(`| Worker Threads支持 | ${config.workerThreadsSupport.supported ? '✅' : '❌'} |`);
    report.push('');
  }

  // 优化建议
  const suggestions = generateOptimizationSuggestions(data);
  if (suggestions.length > 0) {
    report.push('## 💡 优化建议');
    report.push('');

    suggestions.forEach((suggestion, index) => {
      const priorityIcon =
        suggestion.priority === 'high' ? '🔴' : suggestion.priority === 'medium' ? '🟡' : '🟢';

      report.push(`### ${index + 1}. ${suggestion.category} ${priorityIcon}`);
      report.push('');
      report.push(`**问题:** ${suggestion.issue}`);
      report.push(`**建议:** ${suggestion.suggestion}`);
      report.push(`**当前状态:** ${suggestion.current}`);
      report.push(`**目标:** ${suggestion.target}`);
      report.push('');
    });
  }

  // 结论
  report.push('## 🎯 结论');
  report.push('');

  if (data.startupAnalysis && data.startupAnalysis.latest < 1500) {
    report.push('✅ **启动性能优秀** - 启动时间在可接受范围内');
  } else if (data.startupAnalysis && data.startupAnalysis.latest < 3000) {
    report.push('🟡 **启动性能良好** - 有进一步优化空间');
  } else {
    report.push('🔴 **启动性能需要优化** - 建议实施优化建议');
  }

  if (data.hotReloadAnalysis && data.hotReloadAnalysis.average < 2000) {
    report.push('✅ **热重载性能优秀** - 开发体验良好');
  } else if (data.hotReloadAnalysis && data.hotReloadAnalysis.average < 5000) {
    report.push('🟡 **热重载性能良好** - 可接受的开发体验');
  } else {
    report.push('🔴 **热重载性能需要优化** - 影响开发效率');
  }

  report.push('');
  report.push('---');
  report.push('');
  report.push('*本报告由 EggJS 性能优化工具自动生成*');

  return report.join('\n');
}

/**
 * 主函数
 */
function main() {
  console.log('📊 生成性能优化报告...');

  try {
    // 读取性能数据
    const data = readPerformanceData();

    // 分析数据
    data.startupAnalysis = analyzeStartupPerformance(data.startupReports);
    data.hotReloadAnalysis = analyzeHotReloadPerformance(data.hotReload);

    // 确保输出目录存在
    if (!fs.existsSync(REPORT_CONFIG.outputDir)) {
      fs.mkdirSync(REPORT_CONFIG.outputDir, { recursive: true });
    }

    // 生成Markdown报告
    const markdownReport = generateMarkdownReport(data);
    const reportPath = path.join(REPORT_CONFIG.outputDir, 'performance-report.md');
    fs.writeFileSync(reportPath, markdownReport);

    console.log(`✅ 性能报告已生成: ${reportPath}`);

    // 保存原始数据
    const dataPath = path.join(REPORT_CONFIG.outputDir, 'performance-data.json');
    fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));

    console.log(`📝 原始数据已保存: ${dataPath}`);

    return true;
  } catch (error) {
    console.error('❌ 生成报告失败:', error.message);
    return false;
  }
}

// 如果直接运行脚本
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = {
  readPerformanceData,
  analyzeStartupPerformance,
  analyzeHotReloadPerformance,
  generateOptimizationSuggestions,
  generateMarkdownReport,
  main,
};

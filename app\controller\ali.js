const Controller = require('egg').Controller;
const Core = require('@alicloud/pop-core');

class AliController extends Controller {
  async main() {
    const ctx = this.ctx;
    let p = ctx.query.p;
    let pass = ctx.query.pass;
    let text = ctx.query.text ?? '动车';
    let fs = ctx.query.fs ?? '动车';
    await ctx.service.feishu.fs(fs);
    if (!pass) {
      ctx.body = { error: 'pass error' };
      return;
    }
    const client = new Core({
      accessKeyId: 'LTAI5tRS2i8cAs98XmW7YK4q',
      accessKeySecret: '******************************',
      endpoint: 'https://dysmsapi.aliyuncs.com',
      apiVersion: '2017-05-25',
    });

    const params = {
      PhoneNumbers: p,
      SignName: '嗯葱',
      TemplateCode: 'SMS_167041703',
      TemplateParam: `{"product":"${text}"}`,
    };

    const requestOption = {
      method: 'POST',
      formatParams: false,
    };

    let er = [];
    await client.request('SendSms', params, requestOption).then(
      (result) => {
        // console.log(JSON.stringify(result));
        er.push(result);
        ctx.body = { info: er };
      },
      (ex) => {
        // console.log(ex);
        er.push(ex);
        ctx.body = { error: er };
      },
    );
  }

  async updateip() {
    const ctx = this.ctx;
    let ip = ctx.query.ip;

    const client = new Core({
      accessKeyId: 'LTAI5tRS2i8cAs98XmW7YK4q',
      accessKeySecret: '******************************',
      endpoint: 'https://dns.aliyuncs.com',
      apiVersion: '2015-01-09',
    });

    try {
      const result = await client.request(
        'DescribeDomainRecords',
        {
          DomainName: 'xctcc.com',
          RRKeyWord: 'home',
          KeyWord: 'home',
        },
        {
          method: 'POST',
          formatParams: false,
        },
      );

      const records = result.DomainRecords.Record;

      for (const record of records) {
        if (record.RR === 'home') {
          const updateParams = {
            RecordId: record.RecordId,
            RR: 'home',
            Type: 'A',
            Value: ip,
          };

          await client.request('UpdateDomainRecord', updateParams, {
            method: 'POST',
            formatParams: false,
          });

          // You might want to break the loop if you've updated the intended record.
          break;
        }
      }

      ctx.body = { success: true, message: 'DNS record updated successfully.' };
    } catch (ex) {
      console.error(ex);
      ctx.body = { success: false, message: 'Error updating DNS record.' };
    }
  }
}

module.exports = AliController;

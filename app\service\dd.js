const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const FormData = require('form-data');
const fs = require('fs');
const { Buffer } = require('buffer');
const { log } = require('console');
const path = require('path');

class DdService extends Service {
  async dd(text) {
    const url =
      'https://oapi.dingtalk.com/robot/send?access_token=be9cdffd1f41b26ca86e83189edcf65b927e57b75d3535aa3a52348d43fe1a68';
    const secret =
      'SEC7628afe6c8ed2c5083c271da3dacc81aefa203d9c58a980a81904030e613170f';
    let data = {
      msgtype: 'text',
      text: {
        content: text,
      },
    };
    let time = Date.now();
    let stringToSign = time + '\n' + secret;
    let base = crypto
      .createHmac('sha256', secret)
      .update(stringToSign)
      .digest('base64');
    let sign = encodeURIComponent(base); //签名
    let xurl = url + `&timestamp=${time}&sign=${sign}`;
    return await axios.post(xurl, data).then(function (response) {
      return response.data;
    });
  }

  async dd2(text) {
    const url =
      'https://oapi.dingtalk.com/robot/send?access_token=6056c84c102d269cd5f8b2b770d3f11eb6c50d5c12492a884e9eeee335c85002';
    const secret =
      'SECb8edc368295a05a4e0b73b053d3b07c66ddd3e74d821ba6898dfb580399fa751';
    let data = {
      msgtype: 'text',
      text: {
        content: text,
      },
    };
    let time = Date.now();
    let stringToSign = time + '\n' + secret;
    let base = crypto
      .createHmac('sha256', secret)
      .update(stringToSign)
      .digest('base64');
    let sign = encodeURIComponent(base); //签名
    let xurl = url + `&timestamp=${time}&sign=${sign}`;
    return await axios.post(xurl, data).then(function (response) {
      return response.data;
    });
  }

  async ddm(text, title, index = 1) {
    let url =
      'https://oapi.dingtalk.com/robot/send?access_token=be9cdffd1f41b26ca86e83189edcf65b927e57b75d3535aa3a52348d43fe1a68';
    let secret =
      'SEC7628afe6c8ed2c5083c271da3dacc81aefa203d9c58a980a81904030e613170f';
    if (index !== 1) {
      url =
        'https://oapi.dingtalk.com/robot/send?access_token=6056c84c102d269cd5f8b2b770d3f11eb6c50d5c12492a884e9eeee335c85002';
      secret =
        'SECb8edc368295a05a4e0b73b053d3b07c66ddd3e74d821ba6898dfb580399fa751';
    }
    const imgurl = text;
    const time = Date.now();
    const stringToSign = time + '\n' + secret;
    const base = crypto
      .createHmac('sha256', secret)
      .update(stringToSign)
      .digest('base64');
    const sign = encodeURIComponent(base); //签名
    const data = {
      msgtype: 'markdown',
      markdown: {
        title: title?.toString() || '图片',
        text: `####  ![screenshot](${imgurl})\n > `,
      },
    };
    const xurl = `${url}&timestamp=${time}&sign=${sign}`;
    try {
      const response = await axios.post(xurl, data);
      return response.data;
    } catch (error) {
      console.error(error);
    }
  }
}

module.exports = DdService;

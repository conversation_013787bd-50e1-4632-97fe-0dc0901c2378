#!/usr/bin/env node

// 测试日志SSE连接

const axios = require('axios');

const BASE_URL = 'http://localhost:7001';
// const BASE_URL = 'https://egg.wcy9.com';

// 模拟浏览器的User-Agent
const browserUA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

async function testLogAPI(logType) {
  console.log(`\n🧪 测试 ${logType} 日志API:`);
  
  try {
    const response = await axios.get(`${BASE_URL}/api/logs/${logType}`, {
      headers: {
        'User-Agent': browserUA,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      responseType: 'stream',
      timeout: 5000
    });
    
    console.log(`✅ ${logType} API连接成功`);
    console.log(`状态码: ${response.status}`);
    console.log(`响应头:`, response.headers);
    
    let dataReceived = false;
    let lineCount = 0;
    
    response.data.on('data', (chunk) => {
      dataReceived = true;
      const chunkStr = chunk.toString('utf8');
      lineCount++;
      
      console.log(`📥 ${logType} 接收数据 (第${lineCount}块):`, chunkStr.substring(0, 100) + '...');
      
      // 接收到几块数据后停止
      if (lineCount >= 3) {
        response.data.destroy();
      }
    });
    
    response.data.on('end', () => {
      console.log(`✅ ${logType} 数据流结束`);
    });
    
    response.data.on('error', (error) => {
      console.error(`❌ ${logType} 数据流错误:`, error.message);
    });
    
    // 等待3秒
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    if (!dataReceived) {
      console.log(`⚠️ ${logType} 未接收到数据`);
    }
    
  } catch (error) {
    console.error(`❌ ${logType} API测试失败:`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error(`状态文本: ${error.response.statusText}`);
      console.error(`响应头:`, error.response.headers);
    } else {
      console.error(`错误: ${error.message}`);
    }
  }
}

async function testLogInfo() {
  console.log(`\n📋 测试日志信息API:`);
  
  try {
    const response = await axios.get(`${BASE_URL}/api/logs/info`, {
      headers: {
        'User-Agent': browserUA
      },
      timeout: 3000
    });
    
    console.log(`✅ 日志信息API成功`);
    console.log(`响应:`, JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error(`❌ 日志信息API失败:`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error(`错误: ${error.response.data}`);
    } else {
      console.error(`错误: ${error.message}`);
    }
  }
}

async function testLogPage() {
  console.log(`\n🌐 测试日志页面:`);
  
  try {
    const response = await axios.get(`${BASE_URL}/logs`, {
      headers: {
        'User-Agent': browserUA
      },
      timeout: 3000
    });
    
    console.log(`✅ 日志页面访问成功`);
    console.log(`状态码: ${response.status}`);
    console.log(`内容长度: ${response.data.length} 字符`);
    
    // 检查是否包含关键元素
    if (response.data.includes('Egg.js 实时日志监控')) {
      console.log(`✅ 页面内容正常`);
    } else {
      console.log(`⚠️ 页面内容可能有问题`);
    }
    
  } catch (error) {
    console.error(`❌ 日志页面访问失败:`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
    } else {
      console.error(`错误: ${error.message}`);
    }
  }
}

async function testWithDifferentUA() {
  console.log(`\n🔍 测试不同User-Agent:`);
  
  const userAgents = [
    { name: 'Chrome', ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36' },
    { name: 'Firefox', ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0' },
    { name: 'Safari', ua: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15' },
    { name: 'Edge', ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0' }
  ];
  
  for (const { name, ua } of userAgents) {
    try {
      const response = await axios.get(`${BASE_URL}/api/logs/web`, {
        headers: {
          'User-Agent': ua,
          'Accept': 'text/event-stream'
        },
        timeout: 2000,
        responseType: 'stream'
      });
      
      console.log(`✅ ${name} 可以访问日志API`);
      response.data.destroy(); // 立即关闭连接
      
    } catch (error) {
      if (error.response?.status === 404) {
        console.log(`❌ ${name} 被中间件过滤 (404)`);
      } else {
        console.log(`⚠️ ${name} 其他错误: ${error.message}`);
      }
    }
  }
}

async function main() {
  console.log('🔍 日志SSE连接测试工具');
  console.log('服务器地址:', BASE_URL);
  console.log('测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('═'.repeat(60));
  
  // 测试基础API
  await testLogInfo();
  await testLogPage();
  
  // 测试不同User-Agent
  await testWithDifferentUA();
  
  // 测试各个日志API
  const logTypes = ['web', 'agent', 'error'];
  for (const logType of logTypes) {
    await testLogAPI(logType);
  }
  
  console.log('\n📊 测试总结:');
  console.log('1. 如果API返回404，检查中间件User-Agent过滤');
  console.log('2. 如果连接成功但无数据，检查日志文件是否存在');
  console.log('3. 如果页面无法访问，检查路由配置');
  console.log('4. 确认服务器在正确端口运行');
  
  console.log('\n💡 解决方案:');
  console.log('1. 已添加更多浏览器User-Agent到白名单');
  console.log('2. 已修复日志页面布局为竖向排列');
  console.log('3. 已改进EventSource错误处理');
  
  console.log('\n🎉 测试完成!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testLogAPI, testLogInfo, testLogPage };

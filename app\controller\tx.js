const Controller = require('egg').Controller;
const tencentcloud = require('tencentcloud-sdk-nodejs');
const axios = require('axios');
const { readFileSync } = require('node:fs');
const LighthouseClient = tencentcloud.lighthouse.v20200324.Client;
const smsClient = tencentcloud.sms.v20210111.Client;
const DnspodClient = tencentcloud.dnspod.v20210323.Client;
const OcrClient = tencentcloud.ocr.v20181119.Client;
const key = {
  secretId: 'AKIDAygNAC2uWtnzzzsvL9r0uTFmBvV62oqD',
  secretKey: 'O0J55pBixsNyMw97i1z3xcI71FJXXkkv',
};
const profile = {
  httpProfile: {
    endpoint: 'lighthouse.tencentcloudapi.com',
  },
};
const hkclientConfig = {
  credential: key,
  region: 'ap-hongkong',
  profile: profile,
};
const gzclientConfig = {
  credential: key,
  region: 'ap-guangzhou',
  profile: profile,
};
const gzclient = new LighthouseClient(gzclientConfig);
const hkclient = new LighthouseClient(hkclientConfig);
const smsclient = new smsClient({
  credential: key,
  /* 必填：地域信息，可以直接填写字符串ap-guangzhou，支持的地域列表参考 https://cloud.tencent.com/document/api/382/52071#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8 */
  region: 'ap-guangzhou',
  /* 非必填:
   * 客户端配置对象，可以指定超时时间等配置 */
  profile: {
    /* SDK默认用TC3-HMAC-SHA256进行签名，非必要请不要修改这个字段 */
    signMethod: 'HmacSHA256',
    httpProfile: {
      /* SDK默认使用POST方法。
       * 如果你一定要使用GET方法，可以在这里设置。GET方法无法处理一些较大的请求 */
      reqMethod: 'POST',
      /* SDK有默认的超时时间，非必要请不要进行调整
       * 如有需要请在代码中查阅以获取最新的默认值 */
      reqTimeout: 30,
      /**
       * 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com
       */
      endpoint: 'sms.tencentcloudapi.com',
    },
  },
});

class TxController extends Controller {
  async index() {
    const { ctx } = this;
    ctx.body = 'hi, egg';
  }

  async sms() {
    const ctx = this.ctx;
    let pass = ctx.query.pass;
    let text = ctx.query.text ?? '动车';
    let fs = ctx.query.fs ?? '动车';
    await ctx.service.feishu.fs(fs);
    if (!pass) {
      ctx.body = { error: 'pass error' };
      return;
    }
    const params = {
      SmsSdkAppId: '1400834648',
      SignName: '技术学习笔记网',
      TemplateId: '1847720',
      TemplateParamSet: [text],
      PhoneNumberSet: ['+8618698303335'],
    };
    let data = [];
    await smsclient.SendSms(params, function (err, response) {
      if (err) {
        data.push(err);
        return;
      }
      data.push(response);
    });
    ctx.body = { data: data };
  }

  async gzllcx() {
    const params = {};
    let data = await gzclient.DescribeInstancesTrafficPackages(params).then(
      (data) => {
        // const ctx = this.ctx;
        // ctx.body = 'hi, egg';
        return data;
        // console.log(data);
      },
      (err) => {
        console.error('error', err);
      },
    );
    const ctx = this.ctx;
    let TrafficPackageSet =
      data.InstanceTrafficPackageSet[0].TrafficPackageSet[0];
    let d = {};
    // for (let t in TrafficPackageSet){
    //     d[t] = TrafficPackageSet[t];
    // }
    d['已经使用'] = TrafficPackageSet['TrafficUsed'] / 1024 / 1024 / 1024;
    d['剩余'] =
      TrafficPackageSet['TrafficPackageRemaining'] / 1024 / 1024 / 1024;
    d['总量'] = TrafficPackageSet['TrafficPackageTotal'] / 1024 / 1024 / 1024;
    ctx.body = d;
  }

  async hkllcx() {
    const params = {};
    let data = await hkclient.DescribeInstancesTrafficPackages(params).then(
      (data) => {
        return data;
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    const ctx = this.ctx;
    let TrafficPackageSet =
      data.InstanceTrafficPackageSet[0].TrafficPackageSet[0];
    let d = {};
    // for (let t in TrafficPackageSet){
    //     d[t] = TrafficPackageSet[t];
    // }
    d['已经使用'] = TrafficPackageSet['TrafficUsed'] / 1024 / 1024 / 1024;
    d['剩余'] =
      TrafficPackageSet['TrafficPackageRemaining'] / 1024 / 1024 / 1024;
    d['总量'] = TrafficPackageSet['TrafficPackageTotal'] / 1024 / 1024 / 1024;
    ctx.body = d;
  }

  async hksnp() {
    const params = {};
    let data = await hkclient.DescribeSnapshots(params).then(
      (data) => {
        // const ctx = this.ctx;
        // ctx.body = 'hi, egg';
        return data.SnapshotSet;
        // console.log(data);
      },
      (err) => {
        console.error('error', err);
      },
    );
    const ctx = this.ctx;
    ctx.body = data;
  }

  async hkcresnp() {
    const params = {
      InstanceId: 'lhins-g51bzcej',
    };
    let data = await hkclient.CreateInstanceSnapshot(params).then(
      (data) => {
        // const ctx = this.ctx;
        // ctx.body = 'hi, egg';
        return data;
        // console.log(data);
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    const ctx = this.ctx;
    ctx.body = data;
  }

  async hkdelsnp() {
    const params = {};
    let data = await hkclient.DescribeSnapshots(params).then(
      (data) => {
        return data.SnapshotSet;
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    let total = data.length;
    const ctx = this.ctx;
    let last = {};
    let i = 0;
    for (const ctxKey of data) {
      if (i === 1) {
        last = ctxKey;
      }
      i++;
    }
    let snpid = last.SnapshotId;
    const params1 = {
      SnapshotIds: [snpid],
    };
    if (total !== 2) {
      ctx.body = { total: total };
    }
    let resp = await hkclient.DeleteSnapshots(params1).then(
      (data) => {
        return data;
        console.log(data);
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    ctx.body = resp;
  }

  async gzsnp() {
    const params = {};
    let data = await gzclient.DescribeSnapshots(params).then(
      (data) => {
        return data;
      },
      (err) => {
        console.error('error', err);
      },
    );
    const ctx = this.ctx;
    ctx.body = data;
  }

  async gzcresnp() {
    const params = {
      InstanceId: 'lhins-rbyiaig3',
    };
    let data = await gzclient.CreateInstanceSnapshot(params).then(
      (data) => {
        // const ctx = this.ctx;
        // ctx.body = 'hi, egg';
        return data;
        // console.log(data);
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    const ctx = this.ctx;
    ctx.body = data;
  }

  async gzdelsnp() {
    const params = {};
    let data = await gzclient.DescribeSnapshots(params).then(
      (data) => {
        return data.SnapshotSet;
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    let total = data.length;
    const ctx = this.ctx;
    let last = {};
    let i = 0;
    for (const ctxKey of data) {
      if (i === 1) {
        last = ctxKey;
      }
      i++;
    }
    let snpid = last.SnapshotId;
    const params1 = {
      SnapshotIds: [snpid],
    };
    if (total !== 2) {
      ctx.body = { total: total };
    }
    let resp = await gzclient.DeleteSnapshots(params1).then(
      (data) => {
        return data;
        console.log(data);
      },
      (err) => {
        return err;
        console.error('error', err);
      },
    );
    ctx.body = resp;
  }

  async updateip() {
    const { ctx } = this;
    let ip = ctx.query.ip;
    const clientConfig = {
      credential: key,
      region: '',
      profile: {
        httpProfile: {
          endpoint: 'dnspod.tencentcloudapi.com',
        },
      },
    };
    const client = new DnspodClient(clientConfig);

    try {
      const data = await client.DescribeRecordList({
        Domain: 'wcy9.com',
        Subdomain: 'home',
      });

      const params = {
        Domain: 'wcy9.com',
        SubDomain: 'home',
        RecordType: 'A',
        RecordLine: '默认',
        Value: ip,
        RecordId: data.RecordList[0].RecordId,
      };

      const result = await client.ModifyRecord(params);
      console.log(result);
      ctx.body = result;
    } catch (err) {
      console.error('error', err);
    }
  }

  async ocr() {
    const { ctx, app } = this;
    // let base64String = ctx.request.body.text;
    // 1. 获取上传的图片文件
    const file = ctx.request.files[0];
    if (!file) {
      ctx.body = { error: '未上传文件' };
      return;
    }
    const fileBuffer = readFileSync(file.filepath);
    const base64String = fileBuffer.toString('base64');
    // console.log(data);
    // const fileBuffer = readFileSync(data.filepath);
    // const base64String = fileBuffer.toString('base64');
    // console.log(base64String);

    const clientConfig = {
      credential: key,
      region: '',
      profile: {
        httpProfile: {
          endpoint: 'ocr.tencentcloudapi.com',
        },
      },
    };

    try {
      const client = new OcrClient(clientConfig);
      const params = {
        ImageBase64: base64String,
      };
      let text = await client.GeneralAccurateOCR(params).then(
        (data) => {
          // console.log(data.TextDetections[0].DetectedText);
          return data.TextDetections[0].DetectedText;
        },
        (err) => {
          console.error('error', err);
        },
      );
      //text去除所有空格
      let newText = text.replace(/\s/g, '');
      // console.log(newText);
      ctx.body = { text: newText };
    } catch (err) {
      console.error('error', err);
    }
  }

  async mathocr() {
    const { ctx, app } = this;
    let data = ctx.request.body.text;
    // console.log(data);
    // const fileBuffer = readFileSync(data.filepath);
    // const base64String = fileBuffer.toString('base64');
    // console.log(base64String);
    const clientConfig = {
      credential: key,
      region: 'ap-guangzhou',
      profile: {
        httpProfile: {
          endpoint: 'ocr.tencentcloudapi.com',
        },
      },
    };

    try {
      const client = new OcrClient(clientConfig);
      const params = {
        ImageBase64: data,
      };
      let text = await client.FormulaOCR(params).then(
        (data) => {
          // console.log(data.FormulaInfos[0].DetectedText);
          return data.FormulaInfos[0].DetectedText;
        },
        (err) => {
          console.error('error', err);
        },
      );

      ctx.body = { text: text };
    } catch (err) {
      console.error('error', err);
    }
  }
}

module.exports = TxController;

{
  // ==================== 自动保存和格式化 ====================
  "files.autoSave": "onFocusChange",
  "files.autoSaveDelay": 1000,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },

  // ==================== 编辑器外观和行为 ====================
  "editor.fontSize": 14,
  "editor.fontFamily": "'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, 'Courier New', monospace",
  "editor.fontLigatures": false,
  "editor.lineHeight": 1.5,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": true,
  "editor.trimAutoWhitespace": true,
  "editor.renderWhitespace": "boundary",
  "editor.renderControlCharacters": true,
  "editor.rulers": [80, 120],
  "editor.wordWrap": "on",
  "editor.wordWrapColumn": 120,
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 120,
  "editor.scrollBeyondLastLine": false,
  "editor.cursorBlinking": "smooth",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.smoothScrolling": true,

  // ==================== 复制/剪切整行设置 ====================
  "editor.emptySelectionClipboard": true,
  "editor.copyWithSyntaxHighlighting": false,
  "editor.multiCursorModifier": "ctrlCmd",
  "editor.selectionHighlight": true,
  "editor.occurrencesHighlight": "singleFile",

  // ==================== 智能提示和补全 ====================
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },
  "editor.suggestOnTriggerCharacters": true,
  "editor.acceptSuggestionOnCommitCharacter": true,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.snippetSuggestions": "top",
  "editor.tabCompletion": "on",
  "editor.parameterHints.enabled": true,
  "editor.hover.enabled": true,
  "editor.hover.delay": 300,

  // ==================== 文件和搜索 ====================
  "files.exclude": {
    "**/node_modules": true,
    "**/logs": true,
    "**/run": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/*.log": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/logs": true,
    "**/run": true,
    "**/.git": true,
    "**/dist": true,
    "**/build": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/logs/**": true,
    "**/run/**": true
  },

  // ==================== 语言特定设置 ====================
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.wordWrap": "on"
  },

  // ==================== Node.js 和 Egg.js 特定设置 ====================
  "javascript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.importModuleSpecifier": "relative",
  "javascript.suggest.autoImports": true,
  "typescript.suggest.autoImports": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "typescript.updateImportsOnFileMove.enabled": "always",
  "npm.enableScriptExplorer": true,
  "npm.scriptExplorerAction": "run",

  // ==================== ESLint 配置 ====================
  "eslint.enable": true,
  "eslint.validate": ["javascript", "typescript", "javascriptreact", "typescriptreact"],
  "eslint.run": "onType",
  "eslint.autoFixOnSave": true,

  // ==================== Prettier 配置 ====================
  "prettier.enable": true,
  "prettier.requireConfig": false,
  "prettier.configPath": ".prettierrc",
  "prettier.ignorePath": ".prettierignore",

  // ==================== Git 配置 ====================
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "git.autofetchPeriod": 180,
  "git.decorations.enabled": true,
  "gitlens.currentLine.enabled": true,
  "gitlens.hovers.enabled": true,

  // ==================== 终端配置 ====================
  "terminal.integrated.fontSize": 13,
  "terminal.integrated.fontFamily": "'JetBrains Mono', 'Fira Code', Consolas, monospace",
  "terminal.integrated.shell.windows": "C:\\Program Files\\Git\\bin\\bash.exe",
  "terminal.integrated.shell.linux": "/bin/bash",
  "terminal.integrated.shell.osx": "/bin/zsh",
  "terminal.integrated.cwd": "${workspaceFolder}",

  // ==================== 调试配置 ====================
  "debug.console.fontSize": 13,
  "debug.console.fontFamily": "'JetBrains Mono', Consolas, monospace",
  "debug.inlineValues": true,
  "debug.showBreakpointsInOverviewRuler": true,

  // ==================== 工作区配置 ====================
  "workbench.startupEditor": "welcomePage",
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.closeOnFileDelete": true,
  "workbench.editor.highlightModifiedTabs": true,
  "workbench.tree.indent": 20,
  "workbench.tree.renderIndentGuides": "always",

  // ==================== 扩展配置 ====================
  "extensions.autoUpdate": true,
  "extensions.autoCheckUpdates": true,
  "extensions.showRecommendationsOnlyOnDemand": true,
  "extensions.ignoreRecommendations": true,

  // ==================== Egg.js 项目特定配置 ====================
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "emmet.triggerExpansionOnTab": true,

  // 文件关联
  "files.associations": {
    "*.js": "javascript",
    "*.mjs": "javascript",
    "*.ts": "typescript",
    "*.json": "jsonc"
  },

  // 排除不必要的文件监控
  "typescript.disableAutomaticTypeAcquisition": false,
  "javascript.implicitProjectConfig.checkJs": false,

  // 性能优化
  "search.followSymlinks": false,
  "search.smartCase": true,
  "search.useIgnoreFiles": true,
  "search.useGlobalIgnoreFiles": true,
  "cSpell.enabled": false
}

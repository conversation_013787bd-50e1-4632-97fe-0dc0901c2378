const { toJSON } = require('lodash/seq');
const axios = require('axios');
const cheerio = require('cheerio');
const { dateNow, dateformat } = require('../extend/helper');
const Controller = require('egg').Controller;

class SydwController extends Controller {
  async sydw() {
    const ctx = this.ctx;
    let ids = [];
    let data = await ctx.service.xr.select('zzsydw');
    let res = data.data;
    for (let item of res) {
      let zy = item.zy;
      zy = zy.trim();
      if (zy.match('计算机')) {
        ids.push(item.id);
      }
    }
    ctx.body = ids;
  }

  async xm() {
    const { ctx, app } = this;
    let data = await ctx.service.xr.xm();
    let obj = new Function('return ' + data)();

    function in_array(search, array) {
      for (let i in array) {
        if (array[i] === search) {
          return true;
        }
      }
      return false;
    }

    let all = ctx.query.all;
    // let ids = [152, 34, 202, 82, 14, 29, 253, 249, 99, 189, 42, 26, 217, 67, 18, 196, 280, 129, 225, 160, 162, 181, 215, 278, 161, 95];
    // let ids = [
    //   33, 34, 38, 39, 46, 73, 77, 86, 104, 152, 153, 241, 278, 281, 284, 285,
    //   301, 312,
    // ];
    let ids = [8, 10, 12, 29, 40, 65, 75, 173, 179, 190];
    let jsj = [];
    data = obj.Rows;
    console.log(data);
    for (let item of data) {
      item.takenum = item.takenum / 1;
      item.dshrs = item.dshrs / 1;
      item.shtgyjf = item.shtgyjf / 1;
      item.shtgwjf = item.shtgwjf / 1;
      item.shbtg = item.shbtg / 1;
      item.zbmrs = item.zbmrs / 1;
      item.sjzbmrs = (item.dshrs + item.shtgyjf) / 1;
      item.link =
        'https://app.hrss.xm.gov.cn/syzp/index/postinfo!viewPostinfo4index?postinfoid=' +
        item.postinfoid;
      if (in_array(item.postinfono / 1, ids)) {
        jsj.push(item);
      }
    }

    let res;
    if (all) {
      res = data;
    } else {
      res = jsj;
    }
    ctx.body = res;
  }

  async xminfo() {
    const { ctx, app } = this;
    let postinfoid = '10000000000000023737';
    let url =
      'https://app.hrss.xm.gov.cn/syzp/index/postinfo!viewPostinfo4index?postinfoid=' +
      postinfoid;
    let list = [];
    let res = await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);
      odata('textarea').each(function (index, item) {
        let td0 = odata(item).closest('.ylztable_td5').prev('.ylztable_td0');
        let id =
          odata(item).attr('id') || odata(item).find('textarea').attr('id');
        let name = td0.text().trim();
        let selectedOptionText = odata(item).text().trim();
        console.log(name);
        console.log(selectedOptionText);
        console.log('================');
        let obj = {
          name: name,
          id: id,
          text: selectedOptionText,
        };
        list.push(obj);
      });
      odata('select option:selected').each(function (index, item) {
        let td0 = odata(item).closest('.ylztable_td0').prev('.ylztable_td0');
        let name = td0.text().trim();
        let id =
          odata(item).find('input').attr('id') ||
          odata(item).parent().attr('id') ||
          odata(item).attr('id') ||
          td0.children().attr('id');
        let selectedOptionText = odata(item).text().trim();
        console.log(name);
        console.log(selectedOptionText);
        console.log('================');
        let obj = {
          name: name,
          id: id,
          text: selectedOptionText,
        };
        list.push(obj);
      });
      // return list;
    });
    ctx.body = list;
  }

  async xmsydw() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/xm.html', { all: all });
  }

  async qz() {
    const { ctx, app } = this;

    // let data = await ctx.service.xr.qz();
    let url =
      'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=9316b0b00aa711f0a82f286ed48928e1';
    let data = await axios.get(url).then(function (res) {
      console.log(res.data.data.data);
      return res.data.data.data;
    });

    function in_array(search, array) {
      for (let i in array) {
        if (array[i] === search) {
          return true;
        }
      }
      return false;
    }

    let all = ctx.query.all;

    let ids = ['05301', '11302'];
    let jsj = [];
    let time = data.update ? data.update : dateNow();
    data = data.applyPostMap;
    let code = await app.redis.get('qzbmrs');
    // let code = 11;

    for (let item of data) {
      item.postNum = item.postNum / 1;
      item.allNum = item.allNum / 1;
      item.examineNum = item.examineNum / 1;
      item.notExamineNum = item.notExamineNum / 1;
      item.notApprovedNum = item.notApprovedNum / 1;
      item.stuHasAbandon = item.stuHasAbandon / 1;
      item.update = time;

      if (in_array(item.deptCode, ids)) {
        if (item.deptCode === '11302' && item.postCode === '01') {
          if (code / 1 !== item.allNum / 1) {
            await app.redis.set('qzbmrs', item.allNum);
            let text =
              item.deptName +
              item.postCode +
              '\n' +
              '最新总人数:' +
              item.allNum +
              '\n' +
              '审核人数:' +
              item.examineNum +
              '\n' +
              '未审核人数:' +
              item.notExamineNum +
              '\n' +
              '未批准人数:' +
              item.notApprovedNum +
              '\n' +
              '放弃人数:' +
              item.stuHasAbandon +
              '\n' +
              '时间:' +
              dateformat(item.update);
            await ctx.service.tele.dd(text);
          }
        }

        jsj.push(item);
      }
    }
    let res;
    if (all) {
      res = data;
    } else {
      res = jsj;
    }
    ctx.body = res;
  }

  async qzsydw() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/zz.html', { all: all, riqi: 'qz' });
  }

  async lysydw() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/zz.html', { all: all, riqi: 'ly' });
  }

  async szsydw() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/sz.html', { all: all, riqi: 'sz' });
  }

  async fjxmdd() {
    const { ctx, app } = this;
    let data = await ctx.service.xr.xm();
    let obj = new Function('return ' + data)();
    // let info = await axios.get(infourl).then(function (res) {
    //     return res.data.data.rows;
    // })
    let update = obj.Rows[0].zbmrs / 1;
    let currentDate = new Date();
    let updatetime = (await app.redis.get('xm')) / 1;
    if (updatetime !== update) {
      // console.log(currentDate.toLocaleString());  // 输出当前日期和时间，使用本地日期和时间格式
      await app.redis.set('xm', update);
      let text =
        update +
        '更新' +
        'https://egg.wcy9.com/xmsydw' +
        '' +
        '现在时间:\n' +
        '\n\r' +
        '' +
        currentDate.toLocaleString();
      // await ctx.service.tele.dd(text);
      // await ctx.service.feishu.fs(text)
      // await ctx.service.wecom.qywx(text);
      await axios.get('http://127.0.0.1:7001/fs?text=' + text);
      await axios.get('http://127.0.0.1:7001/dd?text=' + text);
      await axios.get('http://127.0.0.1:7001/wecom?text=' + text);
    }
    // if (all) {
    //     let text = update + "更新" + "https://egg.wcy9.com/xmsydw" +
    //         "" + "现在时间:\n" + "\n\r" +
    //         "" + currentDate.toLocaleString()
    //     // await ctx.service.tele.dd(text);
    //     // await ctx.service.feishu.fs(text)
    //     // await ctx.service.wecom.qywx(text);
    //     await axios.get("http://127.0.0.1:7001/fs?text=" + text);
    //     await axios.get("http://127.0.0.1:7001/dd?text=" + text);
    //     await axios.get("http://127.0.0.1:7001/wecom?text=" + text);
    // }
    ctx.body = {
      lastupdate: updatetime,
      update: update,
    };
  }

  async fjzzdd() {
    const { ctx, app } = this;
    let url =
      'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=93edb48a106511f0a96c286ed48928e1';
    // let infourl = 'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    let data = await axios.get(url).then(function (res) {
      return res.data.data.data;
    });
    // let info = await axios.get(infourl).then(function (res) {
    //     return res.data.data.rows;
    // })
    let all = ctx.query.all;
    let update = data.update;
    let updatetime = await app.redis.get('zzsydw');
    if (updatetime !== update) {
      // console.log(currentDate.toLocaleString());  // 输出当前日期和时间，使用本地日期和时间格式
      await app.redis.set('zzsydw', update);
      let text =
        `漳州事业单位更新\n${update}\n` +
        'https://egg.wcy9.com/zzsydw' +
        '\n现在时间:' +
        dateNow();
      // await ctx.service.tele.dd(text);
      await ctx.service.feishu.fs(text);
      // await ctx.service.wecom.qywx(text);
      // await axios.get('http://127.0.0.1:7001/fs?text=' + text);
      // await axios.get('http://127.0.0.1:7001/dd?text=' + text);
      // await axios.get('http://127.0.0.1:7001/wecom?text=' + text);
    }
    if (all) {
      let text =
        update +
        '更新\n' +
        'https://egg.wcy9.com/zzsydw' +
        '\n现在时间:' +
        dateNow();
      // await ctx.service.tele.dd(text);
      await ctx.service.feishu.fs(text);
      // await ctx.service.wecom.qywx(text);
      // await axios.get('http://127.0.0.1:7001/fs?text=' + text);
      // await axios.get('http://127.0.0.1:7001/dd?text=' + text);
      // await axios.get('http://127.0.0.1:7001/wecom?text=' + text);
    }
    ctx.body = {
      lastupdate: updatetime,
      update: update,
    };
  }

  async fjzzsydw() {
    const { ctx, app } = this;

    function in_array(search, array) {
      for (let i in array) {
        if (array[i] === search) {
          return true;
        }
      }
      return false;
    }

    let all = ctx.query.all;
    let riqi = ctx.query.riqi;

    let url =
      'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=93edb48a106511f0a96c286ed48928e1';
    let infourl =
      'http://220.160.53.33:8903/ksbm/student/home/<USER>';

    if (riqi === '20230429') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=c12db1e2cf5c11ed96fb0cda411df2b7';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    } else if (riqi === '20230826') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=153596a62b9111ee96fb0cda411df2b7';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    } else if (riqi === '20240928') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=5841f89565b411efa90a286ed4896763';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    } else if (riqi === '20240427') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=36f6ecd7ea3f11ee9119286ed4896763';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    } else if (riqi === 'qz') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=9316b0b00aa711f0a82f286ed48928e1';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    } else if (riqi === 'ly') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=515d25ff683311efa90a286ed4896763';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    } else if (riqi === 'sz') {
      url =
        'http://220.160.53.33:8903/ksbm/student/jobCase/applyPostCount?keyword=&kjcId=8122c2540ea611f09606286ed48928e1';
      infourl =
        'http://220.160.53.33:8903/ksbm/student/home/<USER>';
    }
    let data = await axios.get(url).then(function (res) {
      return res.data.data.data;
    });
    let info = await axios.get(infourl).then(function (res) {
      return res.data.data.rows;
    });
    let update = data.update;
    // let updatetime = await app.redis.get('zz0429')
    // if (updatetime !== update) {
    //     let currentDate = new Date();
    //     // console.log(currentDate.toLocaleString());  // 输出当前日期和时间，使用本地日期和时间格式
    //     await app.redis.set('zz0429', update);
    //     let text = update + "更新" + "https://egg.wcy9.com/fjzz0429" + "\n" + "\n" + currentDate.toLocaleString()
    //     await ctx.service.tele.dd(text);
    // }

    let infoarr = [];
    let postarr = [];
    let j = 0;
    for (let item of info) {
      item.code = item.deptCode + item.postCode;
      infoarr[j] = item;
      j++;
    }
    data = data.applyPostMap;

    let jsj = [];
    let i = 0;
    for (let item of data) {
      item.code = item.deptCode + item.postCode;
      postarr[i] = item;
      i++;
      // console.log(postarr);
    }
    const mergedArray = [...infoarr, ...postarr].reduce(
      (accumulator, currentValue) => {
        const existingItem = accumulator.find(
          (item) => item.code === currentValue.code,
        );
        if (!existingItem) {
          accumulator.push(currentValue);
        } else {
          Object.assign(existingItem, currentValue);
        }
        return accumulator;
      },
      [],
    );

    //         console.log(mergedArray)
    //         ctx.body = mergedArray;
    //
    // return;

    let time = update ? update : dateNow();

    for (let item of mergedArray) {
      item.postNum = item.postNum / 1;
      item.allNum = item.allNum / 1;
      item.examineNum = item.examineNum / 1;
      item.notExamineNum = item.notExamineNum / 1;
      item.notApprovedNum = item.notApprovedNum / 1;
      item.stuHasAbandon = item.stuHasAbandon / 1;
      item.holdnum = item.allNum - item.notApprovedNum - item.stuHasAbandon;
      item.update = time;
      item.code = item.deptCode + item.postCode;
      if (
        item.specialty.match('计算机') &&
        (item.education.match('本科') || item.education.match('大专')) &&
        item.examineType.match('不限') &&
        !item.sex.match('女') &&
        !item.deptName.match('执法') &&
        item.poltc.match('不')
      ) {
        jsj.push(item);
      }
    }

    let res;
    if (all) {
      res = mergedArray;
    } else {
      res = jsj;
    }
    ctx.body = res;
  }

  async fjzz20230429() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/zz20230429.html', { all: all, riqi: '20230429' });
  }

  async fjzz20230826() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/zz.html', { all: all, riqi: '20230826' });
  }

  async fjzz() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('sydw/zz.html', { all: all });
  }

  async zyx() {
    const { ctx, app } = this;
    const options = {
      method: 'POST',
      url: 'http://bm.ptgzrc.com/api/jobs/get_jobs_count',
      headers: {
        'Content-Type': 'multipart/form-data;',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
      data: {
        shop_id: 62,
        exam_id: 181,
      },
    };
    const response = await axios.request(options).catch(function (error) {
      console.error(error);
    });

    if (
      response &&
      response.data &&
      response.data.data &&
      response.data.data.list
    ) {
      const jobData = response.data.data.list.find(
        (item) => item.job_id === 1861,
      );

      if (jobData) {
        const text =
          jobData.unit_name +
          ' ' +
          jobData.job_title +
          '\n通过人数：' +
          jobData.pass_count +
          '\n未通过人数：' +
          jobData.no_pass_count +
          '\n未审核人数：' +
          jobData.no_check_count +
          '\n总人数：' +
          jobData.total_count;

        const pass_count = (await app.redis.get('zyx_pass_count')) / 1;
        const no_pass_count = (await app.redis.get('zyx_no_pass_count')) / 1;
        const no_check_count = (await app.redis.get('zyx_no_check_count')) / 1;
        const total_count = (await app.redis.get('zyx_total_count')) / 1;

        if (
          jobData.pass_count !== pass_count ||
          jobData.no_pass_count !== no_pass_count ||
          jobData.no_check_count !== no_check_count ||
          jobData.total_count !== total_count
        ) {
          await ctx.service.feishu.fs(text);
          await app.redis.set('zyx_pass_count', jobData.pass_count);
          await app.redis.set('zyx_no_pass_count', jobData.no_pass_count);
          await app.redis.set('zyx_no_check_count', jobData.no_check_count);
          await app.redis.set('zyx_total_count', jobData.total_count);
        }
      }
    }

    ctx.body = response ? response.data : null;
  }

  async zyxinfo() {
    const ctx = this.ctx;
    const options = {
      method: 'POST',
      url: 'http://bm.ptgzrc.com/api/user/login',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
      },
      data: {
        account: '<EMAIL>',
        password: '960423wc',
        exam_id: '157',
      },
    };
    const response = await axios.request(options).catch(function (error) {
      console.error(error);
    });
    if (response && response.data) {
      const data = response.data.data.userinfo;
      const name = data.username;
      const name1 = data.nickname;
      if (name !== '我擦' || name1 !== '我擦') {
        let text = name + '\n' + name1;
        await ctx.service.feishu.fs(text);
      }
    }
    ctx.body = response.data;
  }

  async socretime() {
    const { ctx, app } = this;
    const options = {
      method: 'GET',
      url: 'http://220.160.53.33:8903/ksbm/student/home/<USER>',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
      data: {
        proCode: 'sz25027',
      },
    };
    const response = await axios.request(options).catch(function (error) {
      console.error(error);
    });
    const data = response.data.data.data;

    const score = await app.redis.get('ZZscoreTime');
    const scoreTime = data.scoreTime;
    // console.log(score);
    // console.log(scoreTime);
    if (score !== scoreTime && scoreTime) {
      const text = `省直成绩将在公布：${scoreTime}`;
      await ctx.service.feishu.fs(text);
      await app.redis.set('ZZscoreTime', scoreTime);
    }
    ctx.body = {
      title: data.title,
      scoreTime: scoreTime,
    };
  }

  async ProjectByName() {
    const { ctx, app } = this;
    const options = {
      method: 'GET',
      url: 'http://220.160.53.33:8903/ksbm/score/selectProjectByName?title=&pageNum=1&pageSize=10',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
    };
    const response = await axios.request(options).catch(function (error) {
      console.error(error);
    });
    const data = response.data.data.rows;
    for (let item of data) {
      if (item.title.match('省直')) {
        const text = `省直成绩出现：${item.title}`;
        await ctx.service.feishu.fs(text);
      }
    }
    ctx.body = {
      wxl: data,
    };
  }

  async zzgxjyfz() {
    const { ctx, app } = this;
    let url1 =
      'http://zzgx.lywsrc.com/web_files/staticHtmls/PostStatistics/58/122/area_id=123&page=1.html';
    let url2 =
      'http://zzgx.lywsrc.com/web_files/staticHtmls/PostStatistics/58/122/area_id=123&page=2.html';
    let timer = '';
    let list = [];
    await axios.get(url1).then(function (response) {
      let odata = cheerio.load(response.data);
      timer = odata('span:eq(0)').text();
      odata('tr').each(function (index, item) {
        let tr = odata('tbody').children('tr:eq(' + index + ')');
        let obj = {};
        tr.children('td').each(function (i, item) {
          let tdd = tr.children('td:eq(' + i + ')').text();
          let tda = tr.children('td:eq(' + i + ')').attr('href');
          if (i === 0) {
            obj.zpdw = tdd;
          }
          if (i === 1) {
            obj.gwdm = tdd / 1;
          }
          if (i === 2) {
            obj.zpgw = tdd;
          }
          if (i === 3) {
            obj.zprs = tdd / 1;
          }
          if (i === 4) {
            obj.dsh = tdd / 1;
          }
          if (i === 5) {
            obj.shbtg = tdd / 1;
          }
          if (i === 6) {
            obj.shtgwqr = tdd / 1;
          }
          if (i === 7) {
            obj.yqrrs = tdd / 1;
          }
        });
        if (obj.gwdm === 10103) {
          list.push(obj);
        }
      });

      return list;
    });
    await axios.get(url2).then(function (response) {
      let odata = cheerio.load(response.data);
      odata('tr').each(function (index, item) {
        let tr = odata('tbody').children('tr:eq(' + index + ')');
        let obj = {};
        tr.children('td').each(function (i, item) {
          let tdd = tr.children('td:eq(' + i + ')').text();
          let tda = tr.children('td:eq(' + i + ')').attr('href');
          if (i === 0) {
            obj.zpdw = tdd;
          }
          if (i === 1) {
            obj.gwdm = tdd / 1;
          }
          if (i === 2) {
            obj.zpgw = tdd;
          }
          if (i === 3) {
            obj.zprs = tdd / 1;
          }
          if (i === 4) {
            obj.dsh = tdd / 1;
          }
          if (i === 5) {
            obj.shbtg = tdd / 1;
          }
          if (i === 6) {
            obj.shtgwqr = tdd / 1;
          }
          if (i === 7) {
            obj.yqrrs = tdd / 1;
          }
        });
        if (obj.gwdm === 10701 || obj.gwdm === 10902) {
          list.push(obj);
        }
      });

      return list;
    });
    ctx.body = { timer: timer, list: list };
  }

  async gxjyfz() {
    const { ctx, app } = this;
    let res = await axios.get('http://127.0.0.1:7001/zzgxjyfz');
    let prx = 'jyfz';
    for (let item of res.data.list) {
      let oldsum = await app.redis.get(prx + item.gwdm);
      let sum = item.zprs + item.dsh + item.shbtg + item.shtgwqr + item.yqrrs;

      if (+sum !== +oldsum) {
        let text =
          item.zpdw +
          item.zpgw +
          '\n' +
          '' +
          '待审核人数:' +
          item.dsh +
          '\n' +
          '审核不通过人数:' +
          item.shbtg +
          '\n' +
          '审核通过未确认人数:' +
          item.shtgwqr +
          '\n' +
          '已确认人数:' +
          item.yqrrs;
        await ctx.service.dd.dd2(text);
        await ctx.service.feishu.fs(text);
        await app.redis.set(prx + item.gwdm, +sum);
      }
    }
    ctx.body = res.data;
  }

  async rsj() {
    const { ctx, app } = this;
    // const url =
    //   'http://rsj.zhangzhou.gov.cn/cms/html/zzsrlzyhshbzj/tzgg/index.html';
    const url =
      'http://rsj.zhangzhou.gov.cn/cms/sitemanage/index.shtml?siteId=20196557614060000&page=1';
    const response = await axios.get(url);
    const $ = cheerio.load(response.data);
    const list = [];
    $('.mid-mj-list li').each(async function (index, item) {
      const title = $(this).find('a').text();
      const href =
        `http://rsj.zhangzhou.gov.cn` + $(this).find('a').attr('href');
      const time = $(this).find('.list-time').text(); // 如果需要时间信息的话
      if (title.match('漳州市事业单位公开')) {
        let text = title + '\n' + href;
        await ctx.service.feishu.fs(text);
        // await ctx.service.dd.dd2(text);
        // await ctx.service.wecom.qywx(text);
      }
      // console.log(title, href, time);
      list.push({ title, href, time });
    });
    ctx.body = { list: list };
  }

  async gwy() {
    const { ctx, app } = this;
    let year = `2025`;
    // const url =
    //   'http://rsj.zhangzhou.gov.cn/cms/html/zzsrlzyhshbzj/tzgg/index.html';
    const url = `https://gwykl.fujian.gov.cn/Portal/news/bulletin${year}`;
    const response = await axios.get(url);
    const $ = cheerio.load(response.data);
    let text = $('.container p').text();
    if (!text.match('不存在')) {
      let text1 =
        `福建省${year}年度考试录用公务员公告出来了` +
        '\n' +
        `https://gwykl.fujian.gov.cn/Portal/news/bulletin${year}`;
      await ctx.service.feishu.fs(text1);
    }
    ctx.body = { text: text };
  }

  async gwysignupcount() {
    const { ctx, app } = this;
    let years = ctx.query.years || '2025';
    let url1 = `https://gwykl.fujian.gov.cn/kl${years}/signupcount`;
    let jscode = '';
    let list = [];
    let timer = '';
    let bmss = 0;

    let res = await axios.get(url1).then(function (response) {
      let odata = cheerio.load(response.data);
      jscode = odata('script ').text();
      timer = odata('h4 ').eq(1).text();
      // console.log( timer);
      let tds = '';
      tds = jscode.split('!');

      for (let item in tds) {
        let x = tds[item].split('|');
        if (
          (x[0].match('00180') && x[1].match('01')) ||
          (x[0].match('00202') && x[1].match('03')) ||
          (x[0].match('00203') && x[1].match('01')) ||
          (x[0].match('00210') && x[1].match('03')) ||
          (x[0].match('14026') && x[1].match('03')) ||
          (x[0].match('14081') && x[1].match('13')) ||
          (x[0].match('14208') && x[1].match('01')) ||
          (x[0].match('14214') && x[1].match('01')) ||
          (x[0].match('14215') && x[1].match('01')) ||
          (x[0].match('15058') && x[1].match('01')) ||
          (x[0].match('16090') && x[1].match('02')) ||
          (x[0].match('16095') && x[1].match('01')) ||
          (x[0].match('18030') && x[1].match('01')) ||
          (x[0].match('18064') && x[1].match('01')) ||
          (x[0].match('18201') && x[1].match('01')) ||
          (x[0].match('19055') && x[1].match('01')) ||
          (x[0].match('19084') && x[1].match('01')) ||
          (x[0].match('19090') && x[1].match('01')) ||
          (x[0].match('20095') && x[1].match('03')) ||
          (x[0].match('21134') && x[1].match('01')) ||
          (x[0].match('21158') && x[1].match('03')) ||
          (x[0].match('21204') && x[1].match('01')) ||
          (x[0].match('22026') && x[1].match('02')) ||
          (x[0].match('22201') && x[1].match('01')) ||
          (x[0].match('22223') && x[1].match('01')) ||
          (x[0].match('24026') && x[1].match('02')) ||
          (x[0].match('24058') && x[1].match('01')) ||
          (x[0].match('24081') && x[1].match('01')) ||
          (x[0].match('24210') && x[1].match('01')) ||
          (x[0].match('25212') && x[1].match('01')) ||
          (x[0].match('25216') && x[1].match('01')) ||
          (x[0].match('26184') && x[1].match('01')) ||
          (x[0].match('26221') && x[1].match('01')) ||
          (x[0].match('26221') && x[1].match('02')) ||
          (x[0].match('26221') && x[1].match('03')) ||
          (x[0].match('26221') && x[1].match('04')) ||
          (x[0].match('27054') && x[1].match('01')) ||
          (x[0].match('27204') && x[1].match('02')) ||
          (x[0].match('27210') && x[1].match('03')) ||
          (x[0].match('28132') && x[1].match('02')) ||
          (x[0].match('28141') && x[1].match('01')) ||
          (x[0].match('28203') && x[1].match('01')) ||
          (x[0].match('28207') && x[1].match('03')) ||
          (x[0].match('29201') && x[1].match('03')) ||
          (x[0].match('29206') && x[1].match('03')) ||
          (x[0].match('30025') && x[1].match('03')) ||
          (x[0].match('30210') && x[1].match('01')) ||
          (x[0].match('31055') && x[1].match('01')) ||
          (x[0].match('31132') && x[1].match('01')) ||
          (x[0].match('31228') && x[1].match('01')) ||
          (x[0].match('32002') && x[1].match('01')) ||
          (x[0].match('32204') && x[1].match('01')) ||
          (x[0].match('32206') && x[1].match('02')) ||
          (x[0].match('32208') && x[1].match('02'))
        ) {
          bmss = bmss + +x[4];
          list.push({
            dw: x[0],
            zw: x[1],
            kslx: x[2],
            zks: +x[3],
            bms: +x[4],
            shtg: +x[5],
            wsh: +x[6],
          });
        }
      }
      return list;
    });

    // console.log(bmss);
    let bms = await app.redis.get(`gwy${years}`);
    if (+bms !== +bmss) {
      await app.redis.set(`gwy${years}`, +bmss);
      let text =
        `福建省${years}年度考试录用公务员更新\n` +
        'https://vue.wcy9.com/gwy\n' +
        timer +
        '\n现在时间:  ' +
        dateNow();
      await ctx.service.feishu.fs(text);
    }
    ctx.body = { list: res, timer: timer };
  }

  async chengji() {
    const { ctx, app } = this;
    const url =
      'http://220.160.53.33:8903/ksbm/score/selectProjectByName?title=';

    try {
      const response = await axios.get(url);
      const data = response.data;

      if (data && data.data && data.data.rows) {
        // Map through the rows to extract the ids
        // const ids = data.data.rows.map((row) => row.id);
        // const titles = data.data.rows.map((row) => row.title);
        for (let item of data.data.rows) {
          // console.log(item['id'], item['title']);
          let cj = await app.redis.get('sydwcj');
          if (
            cj !== item['title'] &&
            item['title'].match('漳州') &&
            item['title'].match('事业单位')
          ) {
            // console.log(item['title'], cj);
            await app.redis.set('sydwcj', item['title']);
            let text =
              '漳州事业单位成绩更新\n' +
              'http://220.160.53.33:8903/score/index?loginflag=false\n' +
              '\n现在时间:  ' +
              dateNow();
            await ctx.service.feishu.fs(text);
          }
        }

        // Return the ids in the response body
        ctx.body = { title: data.data.rows };
      } else {
        ctx.body = { error: 'Invalid response structure' };
      }
    } catch (error) {
      ctx.body = { error: error.message };
    }
  }

  async cj() {
    const { ctx, app } = this;
    const url =
      'http://220.160.53.33:8903/ksbm/score/selectScore?projectId=36f6ecd7ea3f11ee9119286ed4896763&idCard=350681199604230512&examNum=243200201315312';
    try {
      const response = await axios.get(url);
      const data = response.data;
      let cj = await app.redis.get('zzcj');
      if (cj !== data.data.data.score && data.data.data.score !== null) {
        await app.redis.set('zzcj', data.data.data.score);
        await ctx.service.feishu.fs('成绩：' + data.data.data.score);
      }
      ctx.body = data.data.data;
    } catch (error) {
      ctx.body = { error: error.message };
    }
  }

  async ptgzrc() {
    const { ctx, app } = this;
    const url = 'http://bm.ptgzrc.com/api/exam/getExam';
    try {
      const response = await axios.post(url);
      let data = response.data.data;
      let list = [];
      // console.log(data.sign_up[0]);
      for (let x of data.sign_up) {
        let y = {
          exam_name: x.exam_name,
          printing_time: x.printing_time,
        };
        list.push(y);
      }
      for (let x of data.print) {
        let y = {
          exam_name: x.exam_name,
          printing_time: x.printing_time,
        };
        list.push(y);
      }
      for (let x of data.finish) {
        let y = {
          exam_name: x.exam_name,
          printing_time: x.printing_time,
        };
        list.push(y);
      }
      for (let x of list) {
        if (x.exam_name.match('程农')) {
          let cj = await app.redis.get('fjcnjt');
          if (x.printing_time !== '' && cj !== x.printing_time) {
            await app.redis.set('fjcnjt', x.printing_time);
            await ctx.service.feishu.fs(
              '福建省程农投资集团有限公司人员招聘准考证：' + x.printing_time,
            );
          }
        }
      }
      ctx.body = list;
    } catch (error) {
      ctx.body = { error: error.message };
    }
  }

  async check0928() {
    const { ctx, app } = this;
    const urls = {
      zz: 'http://220.160.53.33:8903/ksbm/student/home/<USER>',
      xm: 'http://220.160.53.33:8903/ksbm/student/home/<USER>',
      qz: 'http://220.160.53.33:8903/ksbm/student/home/<USER>',
    };

    try {
      const responses = await Promise.all(
        Object.values(urls).map((url) => axios.get(url)),
      );

      const allData = responses.flatMap((response) => response.data.data.data);

      const filteredData = allData.filter(
        (item) =>
          item.examTime?.match('09-28') || item.examTimeExplain?.match('9月28'),
      );

      for (let item of filteredData) {
        console.log(item);
        const text = `${item.title}\n考试时间：${dateformat(item.examTime)}`;
        await ctx.service.feishu.fs(text);
      }

      ctx.body = allData;
    } catch (error) {
      ctx.body = { error: error.message };
    }
  }

  async jszp() {
    const { ctx, app } = this;
    let data = ctx.query.data;
    if (data) {
      const url = 'https://jszk.eeafj.cn/news8774'; // 替换为实际页面地址
      const url1 = 'https://jszk.eeafj.cn/news8237'; // 替换为实际页面地址
      const response = await axios.get(url);
      const response1 = await axios.get(url1);
      const $ = cheerio.load(response.data);
      const $1 = cheerio.load(response1.data);
      const list = [];
      const time = $('.titlep').text().trim();
      let total = 0;
      console.log(time);
      // 选择目标表格，跳过表头行
      $1('table.showtable.ke-zeroborder tbody tr').each(function (index, item) {
        if (index === 0) return; // 跳过表头
        const cells = $(item).find('td');
        const administrative = $(cells[0]).text().trim(); // 行政辖区/所属
        const recruitment = $(cells[1]).text().trim(); // 招聘岗位
        const count = $(cells[2]).text().trim(); // 初审通过数
        total = total + +count;
        if (recruitment.match('互联网') || recruitment.match('网络安全')) {
          list.push({ administrative, recruitment, count });
        }
      });
      $('table.showtable.ke-zeroborder tbody tr').each(function (index, item) {
        if (index === 0) return; // 跳过表头
        const cells = $(item).find('td');
        const administrative = $(cells[0]).text().trim(); // 行政辖区/所属
        const recruitment = $(cells[1]).text().trim(); // 招聘岗位
        const count = $(cells[2]).text().trim(); // 初审通过数
        if (
          administrative.match('漳州') &&
          (recruitment.match('信息') || recruitment.match('中学通用'))
        ) {
          list.push({ administrative, recruitment, count });
        }
      });
      console.log(total);
      const score = await app.redis.get('fjjszp');
      // console.log(score)
      // console.log(scoreTime)
      if (+score !== +total) {
        let text =
          `福建教师招聘人数更新：${total}\n` +
          'https://egg.wcy9.com/jszp\n' +
          '\n现在时间:  ' +
          dateNow();
        await ctx.service.feishu.fs(text);
        await app.redis.set('fjjszp', total);
      }

      ctx.body = { time: time, list: list };
    } else {
      await ctx.render('sydw/jszp');
      return;
    }
  }
}

module.exports = SydwController;

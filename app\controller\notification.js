'use strict';

const Controller = require('egg').Controller;

class NotificationController extends Controller {
  
  /**
   * 处理告警通知请求
   */
  async alert() {
    const { ctx } = this;
    
    try {
      const { action, message, reason, details } = ctx.request.body;
      
      switch (action) {
        case 'sendAlert':
          await ctx.service.notification.sendDownAlert({
            reason: reason || '监控脚本检测到异常',
            details: details || {},
            severity: 'critical'
          });
          break;
          
        case 'sendRecovery':
          await ctx.service.notification.sendRecoveryAlert();
          break;
          
        case 'sendCustom':
          if (message) {
            await ctx.service.feishu.fsMarkdown(message, '📋 自定义通知');
          }
          break;

        case 'testMarkdown':
          if (message) {
            await ctx.service.feishu.fsMarkdown(message, '🧪 Markdown测试');
          }
          break;

        default:
          // 默认发送自定义消息（使用Markdown格式）
          if (message) {
            await ctx.service.feishu.fsMarkdown(message, '📢 通知');
          }
      }
      
      ctx.body = {
        success: true,
        message: '通知发送成功'
      };
      
    } catch (error) {
      ctx.logger.error('通知发送失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '通知发送失败',
        error: error.message
      };
    }
  }
  
  /**
   * 发送测试通知
   */
  async test() {
    const { ctx } = this;
    
    try {
      const testMessage = `🧪 **测试通知**
      
**服务器**: ${require('os').hostname()}
**时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**状态**: 飞书通知功能正常

✅ 这是一条测试消息，表明通知系统工作正常`;

      await ctx.service.feishu.fsText(testMessage, '🧪 测试通知');
      
      ctx.body = {
        success: true,
        message: '测试通知发送成功'
      };
      
    } catch (error) {
      ctx.logger.error('测试通知发送失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '测试通知发送失败',
        error: error.message
      };
    }
  }
  
  /**
   * 发送健康报告
   */
  async healthReport() {
    const { ctx } = this;
    
    try {
      // 获取健康检查数据
      const healthData = await this.getHealthData();
      
      // 发送健康报告
      await ctx.service.notification.sendHealthReport(healthData);
      
      ctx.body = {
        success: true,
        message: '健康报告发送成功',
        data: healthData
      };
      
    } catch (error) {
      ctx.logger.error('健康报告发送失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '健康报告发送失败',
        error: error.message
      };
    }
  }
  
  /**
   * 获取健康检查数据
   */
  async getHealthData() {
    const os = require('os');
    const memUsage = process.memoryUsage();
    const systemMem = {
      total: os.totalmem(),
      free: os.freemem(),
    };
    
    // 检查各项服务状态
    const checks = {};
    
    // MySQL检查
    try {
      if (this.app.mysql) {
        await this.app.mysql.query('SELECT 1');
        checks.mysql = { status: 'ok' };
      }
    } catch (error) {
      checks.mysql = { status: 'error', message: error.message };
    }
    
    // Redis检查
    try {
      if (this.app.redis) {
        await this.app.redis.ping();
        checks.redis = { status: 'ok' };
      }
    } catch (error) {
      checks.redis = { status: 'error', message: error.message };
    }
    
    return {
      status: Object.values(checks).every(check => check.status === 'ok') ? 'ok' : 'error',
      uptime: process.uptime(),
      system: {
        loadAverage: {
          '1min': os.loadavg()[0],
          '5min': os.loadavg()[1],
          '15min': os.loadavg()[2],
        },
        memory: {
          total: Math.round(systemMem.total / 1024 / 1024), // MB
          free: Math.round(systemMem.free / 1024 / 1024), // MB
          usage: Math.round(((systemMem.total - systemMem.free) / systemMem.total) * 100), // %
        },
      },
      application: {
        memory: {
          rss: Math.round(memUsage.rss / 1024 / 1024), // MB
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        },
      },
      checks,
    };
  }
}

module.exports = NotificationController;

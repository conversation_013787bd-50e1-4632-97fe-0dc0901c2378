#!/bin/bash

echo "🔍 服务器环境诊断工具"
echo "================================"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 系统信息
echo -e "${BLUE}📋 系统信息${NC}"
echo "操作系统: $(uname -a)"
echo "发行版: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "内核版本: $(uname -r)"
echo ""

# 2. 内存信息
echo -e "${BLUE}💾 内存信息${NC}"
free -h
echo ""

# 3. 磁盘空间
echo -e "${BLUE}💿 磁盘空间${NC}"
df -h
echo ""

# 4. 检查 wkhtmltoimage
echo -e "${BLUE}🔧 检查 wkhtmltoimage${NC}"
if command -v wkhtmltoimage &> /dev/null; then
    echo -e "${GREEN}✅ wkhtmltoimage 已安装${NC}"
    echo "版本: $(wkhtmltoimage --version 2>&1 | head -1)"
    echo "路径: $(which wkhtmltoimage)"
else
    echo -e "${RED}❌ wkhtmltoimage 未安装${NC}"
    echo -e "${YELLOW}💡 安装命令:${NC}"
    echo "  sudo apt-get install wkhtmltopdf  # Ubuntu/Debian"
    echo "  sudo yum install wkhtmltopdf      # CentOS/RHEL"
fi
echo ""

# 5. 检查 Node.js 和 npm
echo -e "${BLUE}🟢 检查 Node.js 环境${NC}"
if command -v node &> /dev/null; then
    echo -e "${GREEN}✅ Node.js 已安装${NC}"
    echo "版本: $(node --version)"
    echo "路径: $(which node)"
else
    echo -e "${RED}❌ Node.js 未安装${NC}"
fi

if command -v npm &> /dev/null; then
    echo -e "${GREEN}✅ npm 已安装${NC}"
    echo "版本: $(npm --version)"
else
    echo -e "${RED}❌ npm 未安装${NC}"
fi
echo ""

# 6. 检查 Chrome/Chromium 依赖
echo -e "${BLUE}🌐 检查 Chrome 依赖${NC}"
dependencies=(
    "libatk1.0-0"
    "libatk-bridge2.0-0"
    "libcups2"
    "libdrm2"
    "libgtk-3-0"
    "libgtk-4-1"
    "libnss3"
    "libxcomposite1"
    "libxdamage1"
    "libxrandr2"
    "libgbm1"
    "libxss1"
    "libasound2"
)

missing_deps=()
for dep in "${dependencies[@]}"; do
    if dpkg -l | grep -q "^ii  $dep "; then
        echo -e "${GREEN}✅ $dep${NC}"
    else
        echo -e "${RED}❌ $dep${NC}"
        missing_deps+=("$dep")
    fi
done

if [ ${#missing_deps[@]} -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}💡 安装缺失依赖:${NC}"
    echo "sudo apt-get install ${missing_deps[*]}"
fi
echo ""

# 7. 检查字体
echo -e "${BLUE}🔤 检查字体${NC}"
font_dirs=(
    "/usr/share/fonts/"
    "/usr/local/share/fonts/"
    "/home/<USER>/.fonts/"
)

for font_dir in "${font_dirs[@]}"; do
    if [ -d "$font_dir" ]; then
        font_count=$(find "$font_dir" -name "*.ttf" -o -name "*.otf" | wc -l)
        echo -e "${GREEN}✅ $font_dir (字体数: $font_count)${NC}"
    else
        echo -e "${YELLOW}⚠️ $font_dir 不存在${NC}"
    fi
done

# 检查中文字体
if find /usr/share/fonts/ -name "*zh*" -o -name "*chinese*" -o -name "*wqy*" | grep -q .; then
    echo -e "${GREEN}✅ 中文字体已安装${NC}"
else
    echo -e "${YELLOW}⚠️ 未找到中文字体${NC}"
    echo -e "${YELLOW}💡 安装中文字体:${NC}"
    echo "  sudo apt-get install fonts-wqy-zenhei fonts-wqy-microhei"
fi
echo ""

# 8. 检查临时目录
echo -e "${BLUE}📁 检查临时目录${NC}"
temp_dirs=("/tmp/" "/var/tmp/")

for temp_dir in "${temp_dirs[@]}"; do
    if [ -d "$temp_dir" ]; then
        if [ -w "$temp_dir" ]; then
            space=$(df -h "$temp_dir" | tail -1 | awk '{print $4}')
            echo -e "${GREEN}✅ $temp_dir 可写 (可用空间: $space)${NC}"
        else
            echo -e "${RED}❌ $temp_dir 不可写${NC}"
            echo -e "${YELLOW}💡 修复权限: sudo chmod 777 $temp_dir${NC}"
        fi
    else
        echo -e "${RED}❌ $temp_dir 不存在${NC}"
    fi
done
echo ""

# 9. 检查进程和端口
echo -e "${BLUE}🔄 检查相关进程${NC}"
if pgrep -f "node.*egg" > /dev/null; then
    echo -e "${GREEN}✅ Egg.js 进程运行中${NC}"
    echo "进程ID: $(pgrep -f 'node.*egg')"
else
    echo -e "${YELLOW}⚠️ 未找到 Egg.js 进程${NC}"
fi

if pgrep -f "chrome\|chromium" > /dev/null; then
    echo -e "${YELLOW}⚠️ 发现 Chrome/Chromium 进程${NC}"
    echo "进程数: $(pgrep -f 'chrome\|chromium' | wc -l)"
    echo -e "${YELLOW}💡 如果过多可能需要清理: pkill -f chrome${NC}"
else
    echo -e "${GREEN}✅ 无残留 Chrome 进程${NC}"
fi
echo ""

# 10. 网络检查
echo -e "${BLUE}🌐 网络检查${NC}"
if ping -c 1 google.com &> /dev/null; then
    echo -e "${GREEN}✅ 网络连接正常${NC}"
else
    echo -e "${RED}❌ 网络连接异常${NC}"
fi

if nslookup google.com &> /dev/null; then
    echo -e "${GREEN}✅ DNS 解析正常${NC}"
else
    echo -e "${RED}❌ DNS 解析异常${NC}"
fi
echo ""

# 11. 生成建议
echo -e "${BLUE}💡 诊断建议${NC}"
echo "================================"

# 检查内存
total_mem=$(free -m | awk 'NR==2{print $2}')
free_mem=$(free -m | awk 'NR==2{print $7}')

if [ "$total_mem" -lt 2048 ]; then
    echo -e "${YELLOW}⚠️ 系统内存较少 (${total_mem}MB)，建议增加 swap 空间${NC}"
    echo "  sudo fallocate -l 2G /swapfile"
    echo "  sudo chmod 600 /swapfile"
    echo "  sudo mkswap /swapfile"
    echo "  sudo swapon /swapfile"
fi

if [ "$free_mem" -lt 512 ]; then
    echo -e "${YELLOW}⚠️ 可用内存不足 (${free_mem}MB)，可能影响 Puppeteer 性能${NC}"
fi

# 检查磁盘空间
tmp_space=$(df /tmp | tail -1 | awk '{print $4}')
if [ "$tmp_space" -lt 1048576 ]; then  # 小于1GB
    echo -e "${YELLOW}⚠️ /tmp 目录空间不足，建议清理${NC}"
    echo "  sudo find /tmp -type f -atime +7 -delete"
fi

echo ""
echo -e "${GREEN}🎉 诊断完成！${NC}"
echo "如果发现问题，请根据上述建议进行修复。"

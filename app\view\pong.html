<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="https://ip.wcy9.com/js/jquery.js"></script>
    <title>Document</title>
</head>
<body>
<div class="pong" style="font-size: 62px">

</div>
</body>
<script>
    let i = 0;

    function pong() {
        i++
        console.log("ping")
        $.get('/dd?text=ping' + i, function () {
            $('.pong').text("ping" + i)
        })
        // $.get('/wecom?text=ping'+i)
        // console.log("ping"+i)
    }

    setInterval("pong()", 1000)
</script>
</html>
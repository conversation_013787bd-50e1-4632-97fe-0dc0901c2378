<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Document</title>
</head>
<body>
<div id="app">
    <div>
        <el-menu :default-active="activeIndex" class="el-menu-vertical-demo" :unique-opened="true">
            <template v-for="(subMenu, mainMenu) in menuData" >
                <el-submenu :index="mainMenu">
                    <template slot="title">
                        {{ mainMenu }}
                    </template>
                    <el-menu-item v-for="menuItem in subMenu" :index="mainMenu + '|' + menuItem">{{ menuItem }}</el-menu-item>
                </el-submenu>
            </template>
        </el-menu>
    </div>
</div>
</body>
<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                activeIndex: '',
                menuData: {}
            }
        },
        created() {

        },
        mounted() {
            axios
                .get("/minnavjson")
                .then(response => {
                    this.menuData = response.data
                    console.log(this.menuData)
                })
                .catch(error => {
                    console.log(error)
                })
        },
        methods: {
            handleSelect(index) {
                this.activePath = index;
                // 处理菜单选中事件
            },
            toTop() {
                document.body.scrollTop = 0;
            },
            handleOpen(key, keyPath) {
                console.log(key, keyPath);
            },
            handleClose(key, keyPath) {
                console.log(key, keyPath);
            }
        }
    })


</script>
</html>

#!/usr/bin/env node

// 测试GitHub Actions User-Agent

const axios = require('axios');

const BASE_URL = 'http://localhost:7001';
// const BASE_URL = 'https://egg.wcy9.com';

// GitHub Actions可能使用的User-Agent
const githubActionsUAs = [
  'curl/7.68.0',
  'curl/7.81.0', 
  'GitHub-Actions',
  'github-actions',
  'GitHub-Actions/1.0',
  'curl/7.68.0 (GitHub Actions)',
];

async function testGitHubActionsUA(userAgent) {
  console.log(`\n🧪 测试User-Agent: ${userAgent}`);
  
  const testData = {
    projectName: "Vue3前端项目",
    environment: "production",
    version: "main",
    branch: "main", 
    commitHash: "test123456",
    commitMessage: "测试GitHub Actions部署通知",
    deployTime: new Date().toLocaleString('zh-CN'),
    deployDuration: "2分钟30秒",
    deployedBy: "github-actions",
    serverUrl: "https://vue.wcy9.com",
    buildSize: "2.5MB",
    features: [
      "🎨 Vue3 + Vite 构建",
      "📦 pnpm 包管理", 
      "⚡ 缓存优化"
    ],
    notes: "GitHub Actions自动部署测试"
  };

  try {
    const response = await axios.post(`${BASE_URL}/api/deploy/success`, testData, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': userAgent
      },
      timeout: 5000
    });
    
    console.log(`✅ 请求成功 - 状态码: ${response.status}`);
    console.log(`响应: ${JSON.stringify(response.data, null, 2)}`);
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ 请求失败 - 状态码: ${error.response.status}`);
      console.log(`错误: ${error.response.statusText}`);
      if (error.response.status === 404) {
        console.log(`💡 可能被中间件过滤了`);
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`🔌 连接被拒绝 - 服务器可能未启动`);
    } else {
      console.log(`💥 请求错误: ${error.message}`);
    }
  }
}

async function testTemplate() {
  console.log(`\n📋 测试模板接口 (用于验证服务器状态):`);
  
  try {
    const response = await axios.get(`${BASE_URL}/api/deploy/template`, {
      headers: {
        'User-Agent': 'curl/7.68.0'
      },
      timeout: 3000
    });
    
    console.log(`✅ 模板接口正常 - 状态码: ${response.status}`);
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ 模板接口失败 - 状态码: ${error.response.status}`);
    } else {
      console.log(`💥 模板接口错误: ${error.message}`);
    }
  }
}

async function main() {
  console.log('🔍 GitHub Actions User-Agent 测试工具');
  console.log('服务器地址:', BASE_URL);
  console.log('测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('═'.repeat(60));
  
  // 先测试模板接口确认服务器状态
  await testTemplate();
  
  // 测试各种GitHub Actions可能的User-Agent
  for (const ua of githubActionsUAs) {
    await testGitHubActionsUA(ua);
  }
  
  console.log('\n📊 测试总结:');
  console.log('1. 如果所有请求都成功，说明白名单配置正确');
  console.log('2. 如果返回404，可能是中间件过滤问题');
  console.log('3. 如果连接被拒绝，检查服务器是否启动');
  
  console.log('\n💡 GitHub Actions实际使用的User-Agent:');
  console.log('- curl/7.68.0 (最常见)');
  console.log('- curl/7.81.0 (较新版本)');
  console.log('- 可能包含GitHub-Actions标识');
  
  console.log('\n🎯 建议的白名单配置:');
  console.log('- curl (已添加)');
  console.log('- GitHub-Actions (已添加)');
  console.log('- github-actions (已添加)');
  
  console.log('\n🎉 测试完成!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testGitHubActionsUA, githubActionsUAs };

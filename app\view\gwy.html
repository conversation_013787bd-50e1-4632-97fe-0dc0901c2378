<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <script src="/public/vue/vue.min.js"></script>
  <script src="/public/element/index.js"></script>
  <link rel="stylesheet" href="/public/element/index.css">
  <script src="/public/js/axios.min.js"></script>
  <title>Element Plus demo</title>
</head>
<body>
<div>
  {{jzsj}}
</div>
<div id="app">
  <el-table
          :data="tableData"
          style="width: 100%"
          :default-sort = "{prop: 'date', order: 'descending'}"
  >
    <el-table-column
            prop="dw"
            label="单位"
            sortable
            width="180">
    </el-table-column>
    <el-table-column
            prop="zw"
            label="职位"
            sortable
            width="180">
    </el-table-column>
    <el-table-column
            prop="kslx"
            label="考试类型"
            width="180">
    </el-table-column>
    <el-table-column
            prop="zkrs"
            label="招考人数"
            sortable
            width="180">
    </el-table-column>
    <el-table-column
            prop="zbmrs"
            label="总报名人数"
            sortable
            width="180">
    </el-table-column>
    <el-table-column
            prop="shtg"
            label="	审核通过人数"
            sortable
            width="180"
    >
    </el-table-column>
    <el-table-column
            prop="shbtg"
            label="审核不通过人数"
            sortable
            width="180">
    </el-table-column>
    <el-table-column
            prop="total"
            label="总人数"
            sortable
            width="180">
    </el-table-column>
  </el-table>
</div>
<script>
  const App = {
    data() {
      return {
        tableData: {{list | safe}}
      };
    },
    methods: {
      formatter(row, column) {
        return (row.shtg+row.shbtg)/1;
      }
    }
  };
  const app = Vue.createApp(App);
  app.use(ElementPlus);
  app.mount("#app");
</script>
</body>
</html>

'use strict';

/**
 * 全局错误处理中间件
 * 捕获和处理应用中的未处理异常
 */

module.exports = (options, app) => {
  return async function errorHandler(ctx, next) {
    try {
      await next();
    } catch (err) {
      // 记录错误日志
      ctx.logger.error('Unhandled Error:', {
        error: err.message,
        stack: err.stack,
        url: ctx.url,
        method: ctx.method,
        headers: ctx.headers,
        body: ctx.request.body,
        query: ctx.query,
        ip: ctx.ip,
        userAgent: ctx.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      // 设置响应状态码
      const status = err.status || err.statusCode || 500;
      ctx.status = status;

      // 根据请求类型返回不同格式的错误响应
      if (ctx.acceptJSON) {
        // API请求返回JSON格式错误
        ctx.body = {
          success: false,
          error: {
            message: status === 500 ? '服务器内部错误' : err.message,
            code: err.code || 'INTERNAL_ERROR',
            status: status,
            timestamp: new Date().toISOString()
          }
        };
      } else {
        // 页面请求返回错误页面
        try {
          await ctx.render('error.html', {
            title: '出错了',
            status: status,
            message: status === 500 ? '服务器内部错误，请稍后重试' : err.message,
            timestamp: new Date().toLocaleString('zh-CN')
          });
        } catch (renderError) {
          // 如果渲染错误页面也失败，返回简单的HTML
          ctx.type = 'html';
          ctx.body = `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>服务器错误</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 50px; 
                        background: #f5f5f5; 
                    }
                    .error-container { 
                        background: white; 
                        padding: 40px; 
                        border-radius: 10px; 
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
                        max-width: 500px; 
                        margin: 0 auto; 
                    }
                    .error-code { 
                        font-size: 72px; 
                        font-weight: bold; 
                        color: #e74c3c; 
                        margin-bottom: 20px; 
                    }
                    .error-message { 
                        font-size: 18px; 
                        color: #666; 
                        margin-bottom: 30px; 
                    }
                    .back-button { 
                        background: #3498db; 
                        color: white; 
                        padding: 12px 24px; 
                        border: none; 
                        border-radius: 5px; 
                        text-decoration: none; 
                        display: inline-block; 
                        cursor: pointer; 
                    }
                    .back-button:hover { 
                        background: #2980b9; 
                    }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-code">${status}</div>
                    <div class="error-message">
                        ${status === 500 ? '服务器内部错误，请稍后重试' : err.message}
                    </div>
                    <a href="/" class="back-button">返回首页</a>
                </div>
            </body>
            </html>
          `;
        }
      }

      // 在开发环境下，将错误抛出以便调试
      if (app.config.env === 'local' || app.config.env === 'dev') {
        throw err;
      }
    }
  };
};

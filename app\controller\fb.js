const Controller = require('egg').Controller;
const axios = require('axios');
const path = require('path');
const { forEach, set } = require('lodash');
const dayjs = require('dayjs');
const FormData = require('form-data');
const { dayformat, dateformat, dateNow } = require('../extend/helper');
const cheerio = require('cheerio');
const fs = require('fs');
const { HttpsProxyAgent } = require('https-proxy-agent');

class FbController extends Controller {
  async index() {
    const ctx = this.ctx;
    ctx.body = 1;
  }

  async setcookie() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    await app.redis.set('fbcookie', cookies.cookie);
    ctx.body = await app.redis.get('fbcookie');
  }

  async setcookie1() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    await app.redis.set('fbcookie1', cookies.cookie);
    ctx.body = await app.redis.get('fbcookie1');
  }

  async cate() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let biao = ctx.query.biao || 'fbgwycatejs';
    let cate = ctx.query.cate || 'xingce';
    let type = ctx.query.type || '';
    let response = await axios.get(`https://tiku.fenbi.com/api/${cate}/categories`, {
      params: {
        filter: 'keypoint',
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
      headers: {
        Cookie: cookie,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      },
    });
    const categoryList = response.data;
    for (let category in categoryList) {
      if (categoryList[category].children) {
        // console.log(categoryList[category].id, categoryList[category].name);
        let res = await ctx.service.xr['find'](biao, {
          id: categoryList[category].id,
        });
        if (!res.data) {
          await ctx.service.xr['create'](biao, {
            id: categoryList[category].id,
            name: categoryList[category].name,
            level: 0,
            total: categoryList[category].count,
          });
        } else {
          await ctx.service.xr['query'](
            'update ' +
              biao +
              ' set total = ' +
              categoryList[category].count +
              ' where id = ' +
              categoryList[category].id,
          );
        }

        let children1 = categoryList[category].children;
        for (let child1 in children1) {
          let child4 = children1[child1];
          console.log(
            child4.id,
            child4.name,
            categoryList[category].id,
            categoryList[category].name,
          );
          let res = await ctx.service.xr['find'](biao, { id: child4.id });
          if (!res.data) {
            await ctx.service.xr['create'](biao, {
              id: child4.id,
              name: child4.name,
              parentid: categoryList[category].id,
              parent: categoryList[category].name,
              level: 1,
              total: child4.count,
            });
          } else {
            await ctx.service.xr['query'](
              'update ' + biao + ' set total = ' + child4.count + ' where id = ' + child4.id,
            );
          }

          if (children1[child1].children) {
            let children2 = children1[child1].children;
            for (let child2 in children2) {
              let child3 = children2[child2];
              console.log(child3.id, child3.name, child4.id, child4.name);
              let res = await ctx.service.xr['find'](biao, {
                id: child3.id,
              });
              if (!res.data) {
                await ctx.service.xr['create'](biao, {
                  id: child3.id,
                  name: child3.name,
                  parentid: child4.id,
                  parent: child4.name,
                  level: 2,
                  total: child3.count,
                });
              } else {
                await ctx.service.xr['query'](
                  'update ' + biao + ' set total = ' + child3.count + ' where id = ' + child3.id,
                );
              }
              if (child3.children) {
                let children3 = child3.children;
                for (let child5 in children3) {
                  let child6 = children3[child5];
                  console.log(child6.id, child6.name, child3.id, child3.name);
                  let res = await ctx.service.xr['find'](biao, {
                    id: child6.id,
                  });
                  if (!res.data) {
                    await ctx.service.xr['create'](biao, {
                      id: child6.id,
                      name: child6.name,
                      parentid: child3.id,
                      parent: child3.name,
                      level: 3,
                      total: child6.count,
                    });
                  } else {
                    await ctx.service.xr['query'](
                      'update ' +
                        biao +
                        ' set total = ' +
                        child6.count +
                        ' where id = ' +
                        child6.id,
                    );
                  }
                }
              }
            }
          }
        }
      }
    }
    ctx.body = categoryList;
  }

  async sycate() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let biao = 'fbjscate';
    let response = await axios.get('https://tiku.fenbi.com/api/jszgjy/categories', {
      params: {
        filter: 'keypoint',
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
      headers: {
        Cookie: cookie,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      },
    });
    const categoryList = response.data;
    for (let category in categoryList) {
      if (categoryList[category].children) {
        // console.log(categoryList[category].id, categoryList[category].name);
        let res = await ctx.service.xr['find'](biao, {
          id: categoryList[category].id,
        });
        if (!res.data) {
          await ctx.service.xr['create'](biao, {
            id: categoryList[category].id,
            name: categoryList[category].name,
            level: 0,
            total: categoryList[category].count,
          });
        } else {
          await ctx.service.xr['query'](
            'update ' +
              biao +
              ' set total = ' +
              categoryList[category].count +
              ' where id = ' +
              categoryList[category].id,
          );
        }

        let children1 = categoryList[category].children;
        for (let child1 in children1) {
          let child4 = children1[child1];
          console.log(
            child4.id,
            child4.name,
            categoryList[category].id,
            categoryList[category].name,
          );
          let res = await ctx.service.xr['find'](biao, { id: child4.id });
          if (!res.data) {
            await ctx.service.xr['create'](biao, {
              id: child4.id,
              name: child4.name,
              parentid: categoryList[category].id,
              parent: categoryList[category].name,
              level: 1,
              total: child4.count,
            });
          } else {
            await ctx.service.xr['query'](
              'update ' + biao + ' set total = ' + child4.count + ' where id = ' + child4.id,
            );
          }

          if (children1[child1].children) {
            let children2 = children1[child1].children;
            for (let child2 in children2) {
              let child3 = children2[child2];
              console.log(child3.id, child3.name, child4.id, child4.name);
              let res = await ctx.service.xr['find'](biao, {
                id: child3.id,
              });
              if (!res.data) {
                await ctx.service.xr['create'](biao, {
                  id: child3.id,
                  name: child3.name,
                  parentid: child4.id,
                  parent: child4.name,
                  level: 2,
                  total: child3.count,
                });
              } else {
                await ctx.service.xr['query'](
                  'update ' + biao + ' set total = ' + child3.count + ' where id = ' + child3.id,
                );
              }
              if (child3.children) {
                let children3 = child3.children;
                for (let child5 in children3) {
                  let child6 = children3[child5];
                  console.log(child6.id, child6.name, child3.id, child3.name);
                  let res = await ctx.service.xr['find'](biao, {
                    id: child6.id,
                  });
                  if (!res.data) {
                    await ctx.service.xr['create'](biao, {
                      id: child6.id,
                      name: child6.name,
                      parentid: child3.id,
                      parent: child3.name,
                      level: 3,
                      total: child6.count,
                    });
                  } else {
                    await ctx.service.xr['query'](
                      'update ' +
                        biao +
                        ' set total = ' +
                        child6.count +
                        ' where id = ' +
                        child6.id,
                    );
                  }
                }
              }
            }
          }
        }
      }
    }
    ctx.body = categoryList;
  }

  async getcate(id) {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let url = `https://tiku.fenbi.com/api/xingce/solution/keypoints?ids=${id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let res = await axios.get(url, { headers });
    return res.data;
  }

  async getFullCategoryInfo(id) {
    const { ctx } = this;

    // Retrieve category information based on id
    let categoryInfo = await ctx.service.xr['find']('fbgwycate', { id });

    if (!categoryInfo.data) {
      // Handle the case where no category information is found
      return { error: id };
    }

    const { level, parentid } = categoryInfo.data;

    if (level === 0) {
      // Level 0 category
      return { catezero: categoryInfo.data.name };
    }

    if (level === 1) {
      // Level 1 category
      let parentCategory = await ctx.service.xr['find']('fbgwycate', {
        id: parentid,
      });
      return {
        catezero: parentCategory.data.parent,
        cateone: categoryInfo.data.name,
      };
    }

    if (level === 2) {
      // Level 2 category
      let parentCategory = await ctx.service.xr['find']('fbgwycate', {
        id: parentid,
      });
      return {
        catezero: parentCategory.data.parent,
        cateone: categoryInfo.data.parent,
        catetwo: categoryInfo.data.name,
      };
    }

    if (level === 3) {
      // Level 3 category
      let parentCategory = await ctx.service.xr['find']('fbgwycate', {
        id: parentid,
      });
      let grandparentCategory = await ctx.service.xr['find']('fbgwycate', {
        id: parentCategory.data.parentid,
      });
      return {
        catezero: grandparentCategory.data.parent,
        cateone: grandparentCategory.data.name,
        catetwo: parentCategory.data.parent,
        catethree: categoryInfo.data.name,
      };
    }
  }

  async gettimu() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let xid = ctx.query.id;
    let limit = ctx.query.limit;
    let type = ctx.query.type;
    let correctRatioLow = ctx.query.correctRatioLow || 0.3;
    let correctRatioHigh = ctx.query.correctRatioHigh || 0.6;
    // console.log(ctx.query);
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let mode;
    if (type === 'gk' || type === 'gwy') {
      mode = 'xingce';
    } else if (type === 'js') {
      mode = 'jszgjy';
    } else if (type === 'syzc') {
      mode = 'syzc';
    } else {
      mode = 'gk';
    }
    try {
      const response = await axios.post(
        `https://tiku.fenbi.com/api/${mode}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        `type=3&keypointId=${xid}&limit=${limit}&exerciseTimeMode=2&yearScope=0&correctRatioLow=${correctRatioLow}&correctRatioHigh=${correctRatioHigh}`,
        { headers: headers },
      );

      const data = response.data;
      // console.log(data);
      let list = {
        id: data.id,
        key: data.key,
        userId: data.userId,
        questionIds: data.sheet.questionIds,
      };
      let ids = list.questionIds.join(',');
      await ctx.service.xr['create']('fbkaojuan', {
        id: list.id,
        sid: String(list.id),
        userId: list.userId,
        questionIds: ids,
        mode: type,
        cateid: xid,
      });
      // console.log(list);
      ctx.body = list;
    } catch (error) {
      console.error(error);
    }
  }

  // 🚀 优化版本：快速获取题目列表（只返回前端需要的基本数据）
  async gethtimu() {
    const { ctx, app } = this;
    let key = ctx.query.kjid;
    let biao = ctx.query.biao || 'fbsy';
    let type = ctx.query.type || 'syzc';

    // 🔥 性能优化：检查是否只需要基本信息
    const fastMode = ctx.query.fast === '1' || ctx.query.fast === 'true';
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const Exercise = await axios
        .get('https://tiku.fenbi.com/combine/exercise/getExercise', {
          params: {
            format: 'html',
            key: key,
            routecs: 'syzc',
            kav: 120,
            av: 120,
            hav: 120,
            app: 'web',
          },
          headers: headers,
          withCredentials: true, // 携带跨域请求的cookie
        })
        .then((response) => {
          // console.log('请求成功:', response.data);
          return response;
        })
        .catch((error) => {
          console.error('请求失败:', error);
        });
      // 🚀 快速模式：获取完整题目列表，跳过复杂处理
      if (fastMode) {
        console.log('🚀 gethtimu快速模式：获取完整题目列表');
        console.log('🔍 Exercise response code:', Exercise?.data?.code);
        console.log('🔍 Exercise response msg:', Exercise?.data?.msg);

        // 🔥 关键修复：使用与非fastMode相同的逻辑来确定API端点和requestKey
        let qs;
        let Solution;
        let flag;
        let rkey;

        if (+Exercise.data.code === 1) {
          flag = true;
          qs = 'exercise';
          rkey = Exercise?.data?.data?.switchVO?.requestKey;
          console.log('🎯 使用exercise模式, requestKey:', rkey);
        } else {
          flag = false;
          qs = 'solution';
          console.log('🎯 需要获取solution数据...');

          // 获取Solution数据
          Solution = await axios
            .get('https://tiku.fenbi.com/combine/exercise/getSolution', {
              params: {
                format: 'html',
                key: key,
                routecs: 'syzc',
                kav: 120,
                av: 120,
                hav: 120,
                app: 'web',
              },
              headers: headers,
              withCredentials: true,
            })
            .then((response) => {
              console.log('✅ getSolution请求成功');
              return response;
            })
            .catch((error) => {
              console.error('❌ getSolution请求失败:', error);
              throw error;
            });

          rkey = Solution?.data?.data?.switchVO?.requestKey;
          console.log('🎯 使用solution模式, requestKey:', rkey);
        }

        if (!rkey) {
          console.log('🚨 未找到requestKey');
          ctx.body = [];
          return;
        }

        // 检查消息状态
        let msg = flag ? Exercise.data.msg : Solution.data.msg;
        console.log('📋 API消息状态:', msg);

        if (msg !== 'SUCCESS') {
          console.log('🚨 API返回状态不是SUCCESS:', msg);
          ctx.body = [];
          return;
        }

        try {
          console.log(`🌐 调用API: https://tiku.fenbi.com/combine/static/${qs}`);
          const staticRes = await axios.get(`https://tiku.fenbi.com/combine/static/${qs}`, {
            params: {
              key: rkey,
              routecs: 'syzc',
              type: 1,
              kav: 120,
              av: 120,
              hav: 120,
              app: 'web',
            },
            headers: headers,
            withCredentials: true,
          });

          // 🔥 关键修复：根据模式选择正确的数据字段
          let m = flag ? 'questions' : 'solutions';
          const questions = staticRes.data[m] || [];
          console.log(`🔍 获取到完整${m}列表:`, questions.length, '个题目');
          console.log('📊 API响应数据结构:', Object.keys(staticRes.data));

          if (questions.length === 0) {
            console.log('⚠️ 获取到的题目列表为空');
            console.log('🔍 完整响应数据:', JSON.stringify(staticRes.data, null, 2));
          }

          const fastResult = questions.map((item) => ({
            id: item.id,
            globalId: item.globalId,
          }));

          ctx.body = fastResult;
          return;
        } catch (error) {
          console.error('🚨 快速模式API调用失败:', error.response?.data || error.message);
          console.error('🔍 错误详情:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url,
            params: error.config?.params,
          });
          // 如果快速模式失败，继续执行正常流程
        }
      }

      // console.log(response.data);
      let qs;
      let Solution;
      let flag;
      if (+Exercise.data.code === 1) {
        flag = true;
        qs = 'exercise';
      } else {
        flag = false;
        qs = 'solution';
        Solution = await axios
          .get('https://tiku.fenbi.com/combine/exercise/getSolution', {
            params: {
              format: 'html',
              key: key,
              routecs: 'syzc',
              kav: 120,
              av: 120,
              hav: 120,
              app: 'web',
            },
            headers: headers,
            withCredentials: true, // 携带跨域请求的cookie
          })
          .then((response) => {
            // console.log('请求成功:', response.data);
            return response;
          })
          .catch((error) => {
            console.error('请求失败:', error);
          });
      }
      let rkey =
        Exercise?.data?.data?.switchVO?.requestKey || Solution?.data?.data?.switchVO?.requestKey;
      let res;
      let msg = flag ? Exercise.data.msg : Solution.data.msg;

      if (msg === 'SUCCESS') {
        res = await axios
          .get(`https://tiku.fenbi.com/combine/static/${qs}`, {
            params: {
              key: rkey,
              routecs: 'syzc',
              type: 1,
              kav: 120,
              av: 120,
              hav: 120,
              app: 'web',
            },
            headers: headers,
            withCredentials: true, // 携带跨域请求的cookie
          })
          .then(async (response) => {
            // console.log('请求成功:', response.data);

            let m = flag ? 'questions' : 'solutions';
            let questions = response.data[m];
            let x = [];
            let ids = [];
            for (let item of questions) {
              ids.push(item.id);
            }
            ids = ids.join(',');
            // console.log(ids);
            let i = 0;
            let j = 0;

            let resc = await axios.get(
              `https://tiku.fenbi.com/api/${type}/solution/keypoints?ids=${ids}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
              { headers: headers },
            );

            for (let item of questions) {
              let globalId = item.globalId;
              let c = await ctx.service.xr['find'](biao, {
                id: item.id,
              });
              if (c.data) {
                // console.log(`item`, item);

                // console.log(globalId);
                // console.log(Solution?.data);
                const userAnswers = Solution?.data?.data?.userAnswers;
                const match = userAnswers?.[globalId];
                if (match) {
                  c.data.choice = match?.answer?.choice;
                }

                if (c.data?.ds) {
                  if (c.data.ds.match(/```/g)) {
                    await ctx.service.xr['update'](biao, {
                      ds: c.data.ds.replace(/```/g, ''),
                      id: c.data.id,
                    });
                  }
                }
                // console.log('Current item globalId:', item.globalId);

                // 从 card.children 中找到与当前 solution 对应的卡片
                let matchedCard = null;
                for (let child of response.data.card.children) {
                  for (let subChild of child.children) {
                    if (subChild.key === item.globalId) {
                      matchedCard = subChild;
                      break;
                    }
                  }
                  if (matchedCard) break;
                }

                if (!matchedCard) {
                  // console.log(
                  //   'No matching card found for globalId:',
                  //   item.globalId,
                  // );
                }

                // 从 materials 中找到该卡片所关联的材料
                const materialKey = matchedCard?.materialKeys?.[0];
                const matchedMaterial = materialKey
                  ? response.data?.materials?.find((m) => m?.globalId === materialKey)
                  : null;

                // console.log('Matched material:', matchedMaterial);

                if (!matchedMaterial) {
                  // console.log(
                  //   'No matching material found for globalId:',
                  //   item.globalId,
                  // );
                }

                let obj = {};
                obj.material = matchedMaterial?.content.replace(/fenbike/g, 'fbstatic') || null;
                obj.materialKeys = matchedMaterial?.globalId || null;
                obj.parentid = matchedMaterial?.id || null; // 材料 id 存入 parentid
                if (obj?.material) {
                  obj.id = c.data.id;
                  await ctx.service.xr['update'](biao, obj);
                }
                c.data.globalId = item.globalId;
                // console.log(c.data);
                j++;
                x.push(c.data);
              } else {
                if (!flag) {
                  const obj = {};
                  obj.id = item.id;

                  // console.log('Current item globalId:', item.globalId);

                  // 从 card.children 中找到与当前 solution 对应的卡片
                  let matchedCard = null;
                  for (let child of response.data.card.children) {
                    for (let subChild of child.children) {
                      if (subChild.key === item.globalId) {
                        matchedCard = subChild;
                        break;
                      }
                    }
                    if (matchedCard) break;
                  }

                  if (!matchedCard) {
                    // console.log(
                    //   'No matching card found for globalId:',
                    //   item.globalId,
                    // );
                  }

                  // 从 materials 中找到该卡片所关联的材料
                  const materialKey = matchedCard?.materialKeys?.[0];
                  const matchedMaterial = materialKey
                    ? response.data.materials?.find((m) => m.globalId === materialKey)
                    : null;

                  // console.log('Matched material:', matchedMaterial);

                  if (!matchedMaterial) {
                    // console.log(
                    //   'No matching material found for globalId:',
                    //   item.globalId,
                    // );
                  }

                  obj.material = matchedMaterial?.content.replace(/fenbike/g, 'fbstatic') || '';
                  obj.materialKeys = matchedMaterial?.globalId || '';
                  obj.parentid = matchedMaterial?.id || null; // 材料 id 存入 parentid

                  obj.content = item.content.replace(/fenbike/g, 'fbstatic') || '';

                  if (item.accessories?.[0]?.options) {
                    const options = item.accessories[0].options;
                    obj.answerone = options[0] || '';
                    obj.answertwo = options[1] || '';
                    obj.answerthree = options[2] || '';
                    obj.answerfour = options[3] || '';
                  }
                  const userAnswers = Solution?.data?.data?.userAnswers;
                  const match = userAnswers?.[globalId];
                  if (match) {
                    obj.choice = match.answer.choice;
                  }
                  obj.answer =
                    +item.correctAnswer.choice === 0
                      ? 'A'
                      : +item.correctAnswer.choice === 1
                        ? 'B'
                        : +item.correctAnswer.choice === 2
                          ? 'C'
                          : 'D';
                  obj.solution = item.solution.replace(/fenbike/g, 'fbstatic') || '';
                  obj.source = item.source || '';
                  obj.createdTime = dateformat(item.createdTime || Date.now());

                  // console.log(obj.id);
                  // obj.ds = null;
                  obj.ds = '重新更新';
                  obj.allcateid = '';
                  obj.tag = null;
                  obj.correctRatio = null;
                  obj.mostWrongAnswer = '';
                  let textArray = [];
                  // textArray.push(xid);

                  for (let itemx in resc.data[i]) {
                    let all = await this.getAllCategoryInfo('fbsycate', resc.data[i][itemx].id);
                    const result = Object.values(all).join(',');
                    textArray.push(result);
                  }
                  i++;
                  const text = textArray.join(',');
                  const newTextArray = text.split(',');
                  const uniqueTextArray = [...new Set(newTextArray)];
                  const newTextArrayx = uniqueTextArray.join(',');
                  obj.allcateid = newTextArrayx;

                  let res = await ctx.service.xr['find'](biao, { id: item.id });
                  i++;
                  if (!res.data) {
                    await ctx.service.xr['create'](biao, obj);
                  } else {
                    let m = {
                      id: obj.id,
                      choice: obj.choice,
                      material: matchedMaterial?.content.replace(/fenbike/g, 'fbstatic') || null,
                      materialKeys: matchedMaterial?.globalId || null,
                      parentid: matchedMaterial?.id || null, // 材料 id 存入 parentid
                    };
                    // console.log(m);
                    await ctx.service.xr['update'](biao, m);
                  }
                }
              }
            }
            return { data: x };
          })
          .catch((error) => {
            console.error('请求失败:', error);
          });
      }
      ctx.body = res.data;
    } catch (error) {
      console.log(error);
    }
  }

  async getjstimu() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let xid = ctx.query.id;
    let limit = ctx.query.limit;
    let mode = ctx.query.mode || 'xingce';
    let biao = ctx.query.biao || 'fbgwy';
    let biaocate = ctx.query.biaocate || 'fbgwycate';
    let biaofullcate = ctx.query.biaofullcate || 'fbgwyfullcate';
    // let mode = 'xingce';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const response = await axios.post(
        `https://tiku.fenbi.com/api/${mode}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        `type=3&keypointId=${xid}&limit=${limit}&exerciseTimeMode=2`,
        { headers: headers },
      );

      const data = response.data;
      let list = {
        id: data.id,
        userId: data.userId,
        questionIds: data.sheet.questionIds,
      };
      let ids = list.questionIds.join(',');
      // console.log(data.sheet.questionIds);
      // console.log(ids);
      await ctx.service.xr['create']('fbexercise', {
        id: list.id,
        sid: String(list.id),
        userId: list.userId,
        questionIds: ids,
        mode: mode,
      });
      const ress = await axios.get(
        `https://tiku.fenbi.com/api/${mode}/universal/auth/questions?type=0&id=${list.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let resc = await axios.get(
        `https://tiku.fenbi.com/api/${mode}/solution/keypoints?ids=${ids}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );

      console.log('resc', resc.data);
      let questions = ress.data.questions;
      console.log(questions.length);
      for (let item in questions) {
        let x = {};
        x.id = questions[item].id;
        x.content = questions[item].content;
        let options = questions[item].accessories[0].options;
        x.answerone = options[0];
        x.answertwo = options[1];
        x.answerthree = options[2];
        x.answerfour = options[3];
        // console.log(res);
        // console.log(
        //   dateNow() + '\n旧的：' + res.data.content + '\n新的：' + x.content,
        // );
        let counti = 0;
        let countj = 0;
        const choices = ['0', '1', '2', '3'];
        const randomIndex = Math.floor(Math.random() * choices.length);
        console.log(item, x.id, randomIndex, choices[randomIndex].toString());
        const datax = [
          {
            questionIndex: +item,
            questionId: x.id,
            time: 18,
            flag: 0,
            answer: {
              type: 201,
              choice: '1',
            },
          },
        ];

        await axios
          .post(
            `https://tiku.fenbi.com/api/${mode}/async/exercises/${list.id}/incr?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            datax,
            {
              headers: {
                accept: 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'content-type': 'application/json',
                'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                cookie: cookie,
                Referer: 'https://www.fenbi.com/',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
              },
            },
          )
          .catch((res) => {
            console.log(res);
          });
        let res = await ctx.service.xr['find'](biao, { id: x.id });

        if (!res.data) {
          await ctx.service.xr['create'](biao, x);
          counti++;
        } else {
          let res1 = await ctx.service.xr['update'](biao, x);
          countj++;
          if (res.data.content === x.content) {
            await ctx.service.dd.dd2(
              dateNow() + '\n旧的：' + res.data.content + '\n新的：' + x.content,
            );
          }
          if (res.data.id === 5430324) {
            await ctx.service.feishu.fs(dateNow() + '\n' + res.data.id);
          }
        }
        await ctx.service.dd.dd2(dateNow() + '\n' + counti + '新增，' + countj + '更新');
      }
      await axios.post(
        `https://tiku.fenbi.com/api/${mode}/async/exercises/${list.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        'status=1',
        { headers },
      );

      const res = await axios.get(
        `https://tiku.fenbi.com/api/${mode}/universal/auth/solutions?type=0&id=${list.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let datapack = res.data.solutions;
      for (let item in datapack) {
        if (datapack[item].correctAnswer.choice === '0') {
          datapack[item].correctAnswer.choice = 'A';
        } else if (datapack[item].correctAnswer.choice === '1') {
          datapack[item].correctAnswer.choice = 'B';
        } else if (datapack[item].correctAnswer.choice === '2') {
          datapack[item].correctAnswer.choice = 'C';
        } else if (datapack[item].correctAnswer.choice === '3') {
          datapack[item].correctAnswer.choice = 'D';
        }
        let x = {};
        x.answer = datapack[item].correctAnswer.choice;
        x.solution = datapack[item].solution;
        x.source = datapack[item].source;
        x.createdTime = dateformat(datapack[item].createdTime);
        //时间戳转换成正常时间
        // x.createdTime = new Date(x.createdTime).toLocaleString();

        let textArray = [];
        textArray.push(xid);

        for (let itemx in resc.data[item]) {
          console.log('resc_id', resc.data[item][itemx].id);

          let all = await this.getAllCategoryInfo(biaocate, resc.data[item][itemx].id);
          console.log(all);
          const result = Object.values(all).join(',');
          textArray.push(result);
        }
        const text = textArray.join(',');
        const newTextArray = text.split(',');
        const uniqueTextArray = [...new Set(newTextArray)];
        const newTextArrayx = uniqueTextArray.join(',');

        // console.log(newTextArrayx);
        // let sqlx = `UPDATE ${biao} SET `;
        // sqlx = sqlx + "answer='" + x.answer + "'";
        // sqlx = sqlx + ",solution='" + x.solution + "'";
        // sqlx = sqlx + ",source='" + x.source + "'";
        // sqlx = sqlx + ",createdTime='" + x.createdTime + "'";
        // sqlx = sqlx + ",allcateid='" + newTextArrayx + "'";
        // sqlx = sqlx + ' WHERE id=' + datapack[item].id;
        // await ctx.service.xr['query'](sqlx);

        await ctx.service.xr['update'](biao, {
          id: datapack[item].id,
          answer: x.answer,
          solution: x.solution,
          source: x.source,
          createdTime: x.createdTime,
          allcateid: newTextArrayx,
        });
        for (const item1 of resc.data[item]) {
          let sqlc = `SELECT *
                      FROM \`${biaofullcate}\`
                      where questionId = ${questions[item].id}
                        and cateid = ${item1.id}`;
          let res = await ctx.service.xr['query'](sqlc);
          if (!res[0]) {
            await ctx.service.xr['create'](biaofullcate, {
              cateid: item1.id,
              name: item1.name,
              status: item.status,
              questionid: questions[item].id,
            });
          } else {
            // console.log('已经存在');
          }
        }
      }

      let sqlm = `SELECT count(id) as total
                  FROM ${biao}
                  where allcateid like '%${xid}%'`;
      let info = await ctx.service.xr['query'](sqlm);
      console.log(info);
      console.log(dateNow());
      await ctx.service.feishu.fs3(`${info[0].total} 时间: ${dateNow()}`);
      ctx.body = info;
    } catch (error) {
      console.error(error.response.data);
      ctx.body = error.response.data;
    }
  }

  async incr() {
    const { ctx, app } = this;
    let x = ctx.request.body;
    const cookie = await app.redis.get('fbcookie');
    const headers = {
      accept: 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      pragma: 'no-cache',
      priority: 'u=1, i',
      'sec-ch-ua': '"Chromium";v="124", "Google Chrome";v="124", "Not-A.Brand";v="99"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      cookie: cookie,
      Referer: 'https://www.fenbi.com/',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    };
    let elist = [];
    const choiceMap = {
      A: '0',
      B: '1',
      C: '2',
      D: '3',
    };
    x.choice = choiceMap[x.choice];

    elist.push({
      key: x.globalId,
      time: Math.floor(Math.random() * 90) + 30,
      answer: {
        choice: x.choice,
        type: 201,
      },
    });

    try {
      // console.log(
      //   `https://tiku.fenbi.com/combine/exercise/incrUpdate?key=${x.kjid}&routecs=${x.mode}&kav=120&av=120&hav=120&app=web`,
      //   elist,
      // );

      let tijiao = await axios.post(
        `https://tiku.fenbi.com/combine/exercise/incrUpdate?key=${x.kjid}&routecs=${x.mode}&kav=120&av=120&hav=120&app=web`,
        elist,
        { headers },
      );

      if (tijiao) {
        ctx.body = { data: tijiao.data };
      }
    } catch (error) {
      ctx.body = { error: error };
    }
  }

  async submit() {
    const { ctx, app } = this;
    let x = ctx.request.body;
    // console.log('x', x);
    const cookie = await app.redis.get('fbcookie');
    const headers = {
      accept: 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'cache-control': 'no-cache',
      'content-type': 'application/json',
      pragma: 'no-cache',
      priority: 'u=1, i',
      'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      cookie: cookie,
      Referer: 'https://spa.fenbi.com/',
    };
    try {
      let url = `https://tiku.fenbi.com/combine/exercise/submit?key=${x.kjid}&routecs=${x.mode}&kav=120&av=120&hav=120&app=web`;
      let res = axios.post(url, {}, { headers });

      if (res) {
        ctx.body = { msg: res.data };
      }
    } catch (error) {
      ctx.body = { error: error };
    }
  }

  async gettimu2() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let xid = ctx.query.id || 656604;
    let limit = ctx.query.limit || 5;
    let biao = ctx.query.biao || 'fbgwyzlfxjs';
    let biaocate = ctx.query.biaocate || 'fbgwycatejs';
    let biaofullcate = ctx.query.biaofullcate || 'fbsyfullcate';
    let mode = ctx.query.mode || 'xingce';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const response = await axios.post(
        `https://tiku.fenbi.com/api/${mode}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        `type=151&keypointId=${xid}&limit=${limit}&exerciseTimeMode=2&yearScope=0&correctRatioLow=0&correctRatioHigh=1`,
        { headers: headers },
      );

      const data = response.data;
      let list = {
        id: data.id,
        questionIds: data.sheet.questionIds,
      };
      let ids = list.questionIds.join(',');
      await ctx.service.xr['create']('fbexercise', {
        id: list.id,
        sid: String(list.id),
        userId: list.userId,
        questionIds: ids,
        mode: mode,
      });
      const ress = await axios.get(
        `https://tiku.fenbi.com/api/${mode}/universal/auth/questions?type=0&id=${list.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      // console.log(ress.data);
      let resc = await axios.get(
        `https://tiku.fenbi.com/api/${mode}/solution/keypoints?ids=${ids}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );

      let questions = ress.data.questions;
      let parent = ress.data.materials;
      for (let item in parent) {
        let x = {};
        x.id = parent[item].id;
        x.content = parent[item].content;
        let parentres = await ctx.service.xr['find'](biao, { id: x.id });
        if (!parentres.data) {
          await ctx.service.xr['create'](biao, x);
        }
      }

      for (let item in questions) {
        let x = {};
        x.id = questions[item].id;
        x.content = questions[item].content;
        let options = questions[item].accessories[0].options;
        x.answerone = options[0];
        x.answertwo = options[1];
        x.answerthree = options[2];
        x.answerfour = options[3];

        let res = await ctx.service.xr['find'](biao, { id: x.id });
        const choices = ['0', '1', '2', '3'];
        const randomIndex = Math.floor(Math.random() * choices.length);
        const datax = [
          {
            questionIndex: +item,
            questionId: x.id,
            time: 18,
            flag: 0,
            answer: {
              type: 201,
              choice: choices[randomIndex].toString(),
            },
          },
        ];

        await axios.post(
          `https://tiku.fenbi.com/api/${mode}/async/exercises/${list.id}/incr?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          datax,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9',
              'content-type': 'application/json',
              'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://www.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        if (!res.data) {
          await ctx.service.xr['create'](biao, x);
        }
      }

      await axios.post(
        `https://tiku.fenbi.com/api/${mode}/async/exercises/${list.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        'status=1',
        { headers },
      );

      const res = await axios.get(
        `https://tiku.fenbi.com/api/${mode}/universal/auth/solutions?type=0&id=${list.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let datapack = res.data.solutions;
      for (let item in datapack) {
        if (datapack[item].correctAnswer.choice === '0') {
          datapack[item].correctAnswer.choice = 'A';
        } else if (datapack[item].correctAnswer.choice === '1') {
          datapack[item].correctAnswer.choice = 'B';
        } else if (datapack[item].correctAnswer.choice === '2') {
          datapack[item].correctAnswer.choice = 'C';
        } else if (datapack[item].correctAnswer.choice === '3') {
          datapack[item].correctAnswer.choice = 'D';
        }
        let x = {};
        x.answer = datapack[item].correctAnswer.choice;
        x.solution = datapack[item].solution;
        x.source = datapack[item].source;

        x.createdTime = datapack[item].createdTime;
        //时间戳转换成正常时间
        x.createdTime = new Date(x.createdTime).toLocaleString();
        x.parentid = parent[datapack[item].materialIndexes[0]].id;
        console.log(x.source);
        console.log(x.parentid);
        let textArray = [];
        textArray.push(xid);
        for (let itemx in resc.data[item]) {
          let all = await this.getAllCategoryInfo(biaocate, resc.data[item][itemx].id);
          // console.log(all);
          const result = Object.values(all).join(',');
          textArray.push(result);
        }
        const text = textArray.join(',');
        const newTextArray = text.split(',');
        const uniqueTextArray = [...new Set(newTextArray)];
        const newTextArrayx = uniqueTextArray.join(',');

        // console.log(newTextArrayx);
        // let sqlx = `UPDATE ${biao} SET `;
        // sqlx = sqlx + "answer='" + x.answer + "'";
        // sqlx = sqlx + ",solution='" + x.solution + "'";
        // sqlx = sqlx + ",source='" + x.source + "'";
        // sqlx = sqlx + ",createdTime='" + x.createdTime + "'";
        // sqlx = sqlx + ",parentid='" + x.parentid + "'";
        // sqlx = sqlx + ",allcateid='" + newTextArrayx + "'";
        // sqlx = sqlx + ' WHERE id=' + datapack[item].id;
        // await ctx.service.xr['query'](sqlx);
        await ctx.service.xr['update'](biao, {
          id: datapack[item].id,
          answer: x.answer,
          solution: x.solution,
          source: x.source,
          createdTime: x.createdTime,
          allcateid: newTextArrayx,
          parentid: x.parentid,
        });
        // for (const item1 of resc.data[item]) {
        //   let sqlc = `SELECT * FROM \`${biaofullcate}\` where questionId = ${questions[item].id} and cateid = ${item1.id}`;
        //   let res = await ctx.service.xr['query'](sqlc);
        //   if (!res[0]) {
        //     await ctx.service.xr['create'](biaofullcate, {
        //       cateid: item1.id,
        //       name: item1.name,
        //       status: item.status,
        //       questionid: questions[item].id,
        //     });
        //   } else {
        //     // console.log('已经存在');
        //   }
        // }
      }

      let sqlm = `SELECT count(id) as total
                  FROM ${biao}
                  where parentid is null`;
      let infom = await ctx.service.xr['query'](sqlm);
      // console.log(infom);
      ctx.body = {
        total: infom[0].total,
        parent: parent.length,
        data: questions.length,
      };
    } catch (error) {
      // console.error(error.response.data);
      ctx.body = error.response.data;
    }
  }

  async downloadvideo(page, path, biao, cateid) {
    const { ctx, app } = this;
    // let page = 6;
    let i = 0;
    let j = 0;
    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    async function axiosretry(url, retries = 10) {
      let attempt = 0;
      while (attempt < retries) {
        try {
          // const proxy = 'http://127.0.0.1:7897'; // 代理地址
          // const proxy = "http://192.168.3.33:7893"; // 代理地址
          const proxy = 'http://192.168.111.224:8080'; // 代理地址
          const agent = new HttpsProxyAgent(proxy); // 创建代理实例
          return await axios.get(url, {
            responseType: 'stream',
            httpAgent: agent,
            httpsAgent: agent,
            timeout: 10000,
          }); // 尝试请求
        } catch (error) {
          attempt++;
          await ctx.service.feishu.fs3(`$Attempt ${attempt} failed: ${error.message}`);
          console.error(`Attempt ${attempt} failed: ${error.message}`);
          if (attempt >= retries) throw error; // 达到最大重试次数
          await new Promise((resolve) => setTimeout(resolve, 2000)); // 重试前等待
        }
      }
    }

    async function axiosretry1(url, opti, retries = 10) {
      let attempt = 0;
      while (attempt < retries) {
        try {
          return await axios.get(url, opti); // 尝试请求
        } catch (error) {
          attempt++;
          await ctx.service.feishu.fs3(`$Attempt ${attempt} failed: ${error.message}`);
          console.error(`Attempt ${attempt} failed: ${error.message}`);
          if (attempt >= retries) throw error; // 达到最大重试次数
          await new Promise((resolve) => setTimeout(resolve, 2000)); // 重试前等待
        }
      }
    }

    let per = 40;
    const offset = (page - 1) * per;
    let sqlquery = `SELECT *
                    FROM ${biao}
                    where allcateid like '%${cateid}%'
                    order by id desc limit ${per}
                    offset ${offset}`;
    let data = await ctx.service.xr['query'](sqlquery);
    let temp_ids = [];
    for (let item of data) {
      temp_ids.push(item.id);
    }
    let ids = temp_ids.join(',');
    const response = await axiosretry1(
      `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${ids}&tiku_prefix=xingce&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let datalist = response.data.data;
    console.log(ids, temp_ids.length);
    let video_url = '';
    let highestResolutionUrl = '';
    let maxResolution = 0;
    for (let [key, value] of Object.entries(datalist)) {
      if (value && value[0] && value[0].id) {
        console.log('value[0].id', value[0].id);
        // await new Promise((resolve) => setTimeout(resolve, 1000));
        const res = await axiosretry1(
          `https://ke.fenbi.com/api/gwy/v3/episodes/${value[0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers },
        );
        // console.log(res.data.datas);
        for (let item of res.data.datas) {
          if (item) {
            const resolution = item.height * item.width;
            if (resolution > maxResolution) {
              maxResolution = resolution;
              highestResolutionUrl = item.url;
            }
          }
          video_url = item.url;
        }
        if (video_url) {
          let m = {};
          for (let c of data) {
            if (+c.id === +key) {
              console.log(+c.id, video_url);

              m = c;
            }
          }
          const prepath = `E:\\0粉笔视频\\`;
          const zlfx = m.parentid ? m.parentid + `-` : '';
          const correctRatio = m.correctRatio ? Math.round(m.correctRatio) + `-` : '';
          // const filename = `${correctRatio}${zlfx}${m.id}-${m.allcateid.replace(
          //   /,/g,
          //   '-',
          // )}-${m.source}.mp4`;
          let filename = `${m.id}-${correctRatio}${zlfx}${m.source}.mp4`;
          filename = filename.replace(/\\/g, '-');
          filename = filename.replace(/[<>:"/\\|?*]/g, '-');
          filename = filename.replace(/\//g, ' ');

          const res = await axiosretry(video_url);
          if (path) {
            if (!fs.existsSync(`${prepath}${path}`)) {
              fs.mkdirSync(`${prepath}${path}`);
            }
            if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
              continue;
            }
            res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
          } else {
            if (!fs.existsSync(`${prepath}`)) {
              fs.mkdirSync(`${prepath}`);
            }
            if (fs.existsSync(`${prepath}${filename}`)) {
              continue;
            }
            res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
          }
          i++;
        }
        j++;
      }
    }
    console.log('下载完成', i, j);
    return { page: page, total: i };
  }

  async down() {
    const { ctx, app } = this;
    let cateid = ctx.query.id || 0;
    let per = ctx.query.per || 40;
    let path = ctx.query.path;
    let biao = ctx.query.biao || 'fbgwygk';
    let sql = ` select count(id) as total
                from ${biao}
                where allcateid like '%${cateid}%'`;
    console.log(sql);
    let res = await ctx.service.xr['query'](sql);
    let page = res[0].total;
    let total = Math.ceil(page / per);
    console.log(page, per, total, path);
    let j = 0;
    let text1 = path + '开始下载\n' + dateNow();
    await ctx.service.feishu.fs(text1);
    let start = ctx.query.page || 1;
    for (let i = start; i <= total; i++) {
      console.log(dateNow());
      console.log(cateid, i, path, biao);
      let data = await this.downloadvideo(i, path, biao, cateid);
      console.log(data);
      j = j + data.total;
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
    let text = path + '结束，一共:' + j + '\n' + dateNow();
    await ctx.service.feishu.fs(text);
    console.log(text);

    ctx.body = { total: j };
  }

  async downkjlistvideo() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let kjid = ctx.query.kjid || 2850564360;
    let type = ctx.query.type || 'xingce';
    let path = ctx.query.path || 'kj';

    async function addvideo(id) {
      let cookie = await app.redis.get('fbcookie1');
      let headers = {
        Cookie: cookie,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      };

      const response = await axios.get(
        `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${id}&tiku_prefix=${type}&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let datalist = response.data.data;
      let x = '';
      let highestResolutionUrl = '';
      let maxResolution = 0;
      for (let item in datalist) {
        if (datalist[item] && datalist[item][0] && datalist[item][0].id) {
          const res = await axios.get(
            `https://ke.fenbi.com/api/gwy/v3/episodes/${datalist[item][0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers },
          );
          for (let item of res.data.datas) {
            if (item) {
              const resolution = item.height * item.width;
              if (resolution > maxResolution) {
                maxResolution = resolution;
                highestResolutionUrl = item.url;
              }
            }
          }
          x = highestResolutionUrl;
        }
      }
      return x;
    }

    let kjindex = [{ id: 3715, name: '福建' }];
    for (let kjitem of kjindex) {
      const topage = 0;
      const pagesize = 100;
      const labelid = kjitem.id;
      const prepath = `H:\\0粉笔\\${kjitem.name}\\`;
      if (!fs.existsSync(`${prepath}`)) {
        fs.mkdirSync(`${prepath}`);
      }

      const regex = /(\d{4})年/; // 匹配4位数字后跟"年"
      const regex1 = /(\d{4})/; // 匹配4位数字后跟"年"
      const kjlist = await axios.get(
        `https://tiku.fenbi.com/api/${type}/papers/?toPage=${topage}&pageSize=${pagesize}&labelId=${labelid}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      // console.log(kjlist.data.list);
      for (let item of kjlist.data.list) {
        const match1 = item.name.match(regex) || item.name.match(regex1);
        const year = match1[1];
        if (year < 2022) {
          // continue;
        }
        const videoname = item.name;
        path = videoname.replace('/', '');
        if (!fs.existsSync(`${prepath}${path}`)) {
          fs.mkdirSync(`${prepath}${path}`);
        } else {
          continue;
        }
        const kjid = item.id;
        console.log(year, videoname, item.id);
        const response = await axios.post(
          `https://tiku.fenbi.com/api/${type}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          `type=1&paperId=${kjid}&exerciseTimeMode=2`,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
              'content-type': 'application/x-www-form-urlencoded',
              priority: 'u=1, i',
              'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Microsoft Edge";v="132"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://www.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        const data = response.data;
        console.log(data.id);
        const kjres = await axios.get(
          `https://tiku.fenbi.com/api/${type}/universal/auth/questions?type=0&id=${data.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers },
        );

        async function fetchWithRetry(url, options, retries = 10) {
          let attempt = 0;
          while (attempt < retries) {
            try {
              return await axios.get(url, options); // 尝试请求
            } catch (error) {
              attempt++;
              await ctx.service.feishu.fs3(`${path} Attempt ${attempt} failed: ${error.message}`);
              console.error(`Attempt ${attempt} failed: ${error.message}`);
              if (attempt >= retries) throw error; // 达到最大重试次数
              await new Promise((resolve) => setTimeout(resolve, attempt * 2000)); // 重试前等待
            }
          }
        }

        async function axiosretry(url, retries = 10) {
          let attempt = 0;
          while (attempt < retries) {
            try {
              // const proxy = 'http://127.0.0.1:7897'; // 代理地址
              const proxy = 'http://192.168.3.33:7893'; // 代理地址
              const agent = new HttpsProxyAgent(proxy); // 创建代理实例
              return await axios.get(url, {
                responseType: 'stream',
                httpAgent: agent,
                httpsAgent: agent,
                timeout: 10000,
              }); // 尝试请求
            } catch (error) {
              attempt++;
              await ctx.service.feishu.fs3(`$Attempt ${attempt} failed: ${error.message}`);
              console.error(`Attempt ${attempt} failed: ${error.message}`);
              if (attempt >= retries) throw error; // 达到最大重试次数
              await new Promise((resolve) => setTimeout(resolve, 2000)); // 重试前等待
            }
          }
        }

        let questions = kjres.data.questions;
        let i = 1;
        for (let item of questions) {
          new Promise((resolve) => setTimeout(resolve, 6000));
          let x = await addvideo(item.id);
          if (x) {
            const res = await axiosretry(x);
            console.log(i, item.id);
            // const prepath = `H:\\0粉笔\\`;
            const filename = `${i}-${item.id}.mp4`;
            if (path) {
              if (!fs.existsSync(`${prepath}${path}`)) {
                fs.mkdirSync(`${prepath}${path}`);
              }
              if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
                fs.unlinkSync(`${prepath}${path}\\${filename}`);
              }
              res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
              new Promise((resolve) => setTimeout(resolve, 6000));
            } else {
              if (!fs.existsSync(`${prepath}`)) {
                fs.mkdirSync(`${prepath}`);
              }
              if (fs.existsSync(`${prepath}${filename}`)) {
                fs.unlinkSync(`${prepath}${filename}`);
              }
              res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
              new Promise((resolve) => setTimeout(resolve, 6000));
            }
            new Promise((resolve) => setTimeout(resolve, 6000));
          } else {
            console.log(i, item.id, '没有视频');
          }
          i++;
        }

        // const tasks = questions.map((item, i) =>
        //   limit(async () => {
        //     console.log(i + 1, item.id);
        //     const videoUrl = await addvideo(item.id);
        //     if (videoUrl) {
        //       const res = await axios.get(videoUrl, { responseType: 'stream' });
        //       const filename = `${i + 1}-${item.id}.mp4`;
        //       if (path) {
        //         if (!fs.existsSync(`${prepath}${path}`)) {
        //           fs.mkdirSync(`${prepath}${path}`);
        //         }
        //         if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
        //           fs.unlinkSync(`${prepath}${path}\\${filename}`);
        //         }
        //         res.data.pipe(
        //           fs.createWriteStream(`${prepath}${path}\\${filename}`),
        //         );
        //       } else {
        //         if (!fs.existsSync(`${prepath}`)) {
        //           fs.mkdirSync(`${prepath}`);
        //         }
        //         if (fs.existsSync(`${prepath}${filename}`)) {
        //           fs.unlinkSync(`${prepath}${filename}`);
        //         }
        //         res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
        //       }
        //     } else {
        //       console.log(i + 1, item.id, '没有视频');
        //     }
        //   }),
        // );
        // await Promise.all(tasks);
      }
    }
    ctx.body = kjindex;
  }

  async downkjvedio() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let kjid = ctx.query.kjid || 2850564360;
    let path = ctx.query.path || 'kj';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    async function addvideo(id) {
      let cookie = await app.redis.get('fbcookie1');
      let headers = {
        Cookie: cookie,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      };

      const response = await axios.get(
        `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${id}&tiku_prefix=xingce&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let datalist = response.data.data;
      let x = '';
      let highestResolutionUrl = '';
      let maxResolution = 0;
      for (let item in datalist) {
        if (datalist[item] && datalist[item][0] && datalist[item][0].id) {
          const res = await axios.get(
            `https://ke.fenbi.com/api/gwy/v3/episodes/${datalist[item][0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers },
          );
          for (let item of res.data.datas) {
            if (item) {
              const resolution = item.height * item.width;
              if (resolution > maxResolution) {
                maxResolution = resolution;
                highestResolutionUrl = item.url;
              }
            }
          }
          x = highestResolutionUrl;
        }
      }
      return x;
    }

    const kjres = await axios.get(
      `https://tiku.fenbi.com/api/xingce/universal/auth/questions?type=0&id=${kjid}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let questions = kjres.data.questions;
    let i = 1;
    for (let item of questions) {
      console.log(i, item.id);
      let x = await addvideo(item.id);
      if (x) {
        const res = await axios.get(x, {
          responseType: 'stream',
        });
        const prepath = `E:\\0fenbi\\`;
        const filename = `${i}-${item.id}.mp4`;
        if (path) {
          if (!fs.existsSync(`${prepath}${path}`)) {
            fs.mkdirSync(`${prepath}${path}`);
          }
          if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
            fs.unlinkSync(`${prepath}${path}\\${filename}`);
          }
          res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
        } else {
          if (!fs.existsSync(`${prepath}`)) {
            fs.mkdirSync(`${prepath}`);
          }
          if (fs.existsSync(`${prepath}${filename}`)) {
            fs.unlinkSync(`${prepath}${filename}`);
          }
          res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
        }
      } else {
        console.log(i, item.id, '没有视频');
      }
      i++;
    }
    ctx.body = questions;
  }

  async downkjvedio1() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let kjid = ctx.query.kjid || 3118221913;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    async function addvideo(id) {
      let cookie = await app.redis.get('fbcookie1');
      let headers = {
        Cookie: cookie,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      };

      let x = '';
      let highestResolutionUrl = '';
      let maxResolution = 0;
      const res = await axios.get(
        `https://ke.fenbi.com/api/gwy/v3/episodes/${id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      for (let item of res.data.datas) {
        if (item) {
          const resolution = item.height * item.width;
          if (resolution > maxResolution) {
            maxResolution = resolution;
            highestResolutionUrl = item.url;
          }
        }
      }
      x = highestResolutionUrl;
      return x;
    }

    const kjres = await axios.get(
      `https://tiku.fenbi.com/api/xingce/universal/auth/questions?type=0&id=${kjid}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let questions = kjres.data.questions;
    let y = [];
    let ids = questions.map(function (item, index) {
      y.push({ id: item.id, sort: index + 1 });
      return item.id;
    });

    const response = await axios.get(
      `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${ids.join(',')}&tiku_prefix=xingce&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let datalist = response.data.data;
    for (let item in datalist) {
      if (datalist[item] && datalist[item][0] && datalist[item][0].id) {
        for (let item1 of y) {
          if (+item1.id === +item) {
            item1.videoid = datalist[item][0].id;
          }
        }
      }
    }

    async function fetchWithRetry(url, options, retries = 10) {
      let attempt = 0;
      while (attempt < retries) {
        try {
          return await axios.get(url, options); // 尝试请求
        } catch (error) {
          attempt++;
          await ctx.service.feishu.fs3(`${url} Attempt ${attempt} failed: ${error.message}`);
          console.error(`Attempt ${attempt} failed: ${error.message}`);
          if (attempt >= retries) throw error; // 达到最大重试次数
          await new Promise((resolve) => setTimeout(resolve, attempt * 2000)); // 重试前等待
        }
      }
    }

    let i = 1;
    for (let item of y) {
      if (item.videoid) {
        console.log(i, item.id, item.videoid, item.sort);
        let x = await addvideo(item.videoid);
        if (x) {
          const res = await fetchWithRetry(x, {
            responseType: 'stream',
          });
          // const prepath = `E:\\0fenbi\\`;
          const prepath = `H:\\0粉笔\\`;
          let path = '辽宁\\1';
          const filename = `${item.sort}-${item.id}.mp4`;
          if (path) {
            if (!fs.existsSync(`${prepath}${path}`)) {
              fs.mkdirSync(`${prepath}${path}`);
            }
            if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
              fs.unlinkSync(`${prepath}${path}\\${filename}`);
            }
            res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
          } else {
            if (!fs.existsSync(`${prepath}`)) {
              fs.mkdirSync(`${prepath}`);
            }
            if (fs.existsSync(`${prepath}${filename}`)) {
              fs.unlinkSync(`${prepath}${filename}`);
            }
            res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
          }
        }
      } else {
        console.log(i, item.id, item.videoid, item.sort, '没有视频');
      }
      i++;
    }
    ctx.body = y;
  }

  async updatetimu() {
    const { ctx, app } = this;
    let { biao, timuid } = ctx.query;
    let body = ctx.request.body;
    if (Object.keys(body).length > 0) {
      body.id = timuid;
      if (body?.materialKeys) {
        await ctx.service.xr['query'](`UPDATE ${biao}
                                       set extra = '${body.extra}'
                                       WHERE materialKeys = '${body.materialKeys}'`);
      }
      let x = await ctx.service.xr['update'](biao, body);
      ctx.body = { x: x };
      return;
    }

    let data = await ctx.service.xr['find'](biao, { id: timuid });
    ctx.body = { data: data.data };
  }

  async updateidsnull() {
    const { ctx, app } = this;
    let { ids } = ctx.query;

    let data = await ctx.service.xr['query'](`update fbsy
                                              set ds = '重新更新'
                                              where id in (${ids})`);
    ctx.body = { data: data };
  }

  async timu() {
    const { ctx, app } = this;
    let { id, per, page, type, z, b, t, gen, ids, kjid, f, o, q, biao, parentid, fast } = ctx.query;

    // 🚀 快速模式：只返回基本字段，跳过复杂处理
    const fastMode = fast === '1' || fast === 'true';
    per = Number(per) || 10;
    page = Number(page) || 1;
    id = +id;
    let offset = (page - 1) * per;
    let allcateid = id ? `allcateid like '%${id}%'` : `1=1`;
    let rk = `1 = 1`;
    if (+z === 1) {
      if (id === 656604) {
        biao = 'fbsyzlfx';
      } else if (id === 48644) {
        biao = 'fbgwyzlfx';
      }
      // 🚀 性能优化：使用更高效的查询方式
      let sqlquery;
      console.log(`🔍 查询参数: fastMode=${fastMode}, page=${page}, offset=${offset}`);

      if (fastMode && page > 1000) {
        console.log('🚀 使用超高页码优化查询');
        // 🔥 超高页码优化：使用子查询避免大offset
        sqlquery = `SELECT * FROM ${biao}
                    WHERE id <= (
                      SELECT id FROM ${biao}
                      WHERE allcateid like '%${id}%'
                      ORDER BY id DESC
                      LIMIT 1 OFFSET ${offset}
                    )
                    AND allcateid like '%${id}%'
                    ORDER BY id DESC
                    LIMIT ${per}`;
      } else {
        console.log('🔍 使用普通查询');
        // 普通查询
        sqlquery = `SELECT *
                    FROM ${biao}
                    where allcateid like '%${id}%'
                    order by id desc limit ${per}
                    offset ${offset}`;
      }
      if (+gen === 1) {
        let sqlx = await ctx.service.xr['query'](
          `SELECT *
           FROM fbkaojuan
           WHERE sid = ${kjid};`,
        );
        // sqlquery = `SELECT * FROM ${biao} WHERE id IN (${sqlx[0].questionIds})`;
        sqlquery = `SELECT *
                    FROM ${biao}
                    WHERE id IN (${sqlx[0].questionIds})
                    ORDER BY FIELD(id, ${sqlx[0].questionIds})`;
      }

      // console.log(sqlquery);

      let data = await ctx.service.xr['query'](sqlquery);

      // 🚀 性能优化：移除不必要的数据库查询，大幅提升性能
      // 🎯 计算正确的题目编号：考虑分页和kjid模式
      const page = Number(ctx.query.page) || 1;
      const per = Number(ctx.query.per) || 1;
      const kjid = ctx.query.kjid;

      let questions = data.map((item, index) => {
        // 🔥 计算正确的题目编号
        let questionNumber;
        if (kjid) {
          // 新模式：kjid存在时，题目编号就是当前页码
          questionNumber = page;
        } else {
          // 传统模式：分页模式，计算实际编号
          questionNumber = (page - 1) * per + index + 1;
        }

        // 🚀 性能优化：减少字符串处理开销
        const result = { ...item };

        // 只在需要时进行字符串替换
        if (item.content && item.content.includes('>')) {
          result.content = item.content.replace('>', `>${questionNumber}.`);
        }

        // 优化时间格式化
        if (item.createdTime) {
          result.createdTime = dateformat(item.createdTime);
        }

        // 🔧 使用统一的选项格式化函数（优化模式）
        formatAnswerOptions(result, true);

        return result;
      });
      // questions.sort((a, b) => a.originalIndex - b.originalIndex);
      //
      // // 如果不再需要 originalIndex 属性，可以将其移除
      // questions = questions.map(({ originalIndex, ...rest }) => rest);
      // 🚀 性能优化：快速模式下跳过COUNT查询
      let pagetotal;
      if (fastMode) {
        // 快速模式：使用估算值，避免慢查询
        pagetotal = [{ total: Math.max(page * per, 100000) }];
        console.log('🚀 快速模式：跳过COUNT查询，使用估算总数');
      } else {
        let options =
          +id === (48644 || 656604)
            ? `parentid is null and allcateid is null`
            : ` allcateid like '%${id}%'`;
        let sql = `select count(id) as total
                   from ${biao}
                   where ${options}`;
        pagetotal = await ctx.service.xr['query'](sql);
      }

      ctx.body = { pagetotal, data: questions };
      return;
    }
    let cuo = `1 = 1`;
    if (+b === 1) {
      cuo = `answer != choice`;
    }

    let orderby = +o === 1 ? 'asc' : 'desc';
    let ord = +f === 1 ? 'correctRatio' : +f === 2 ? 'sort' : 'id';
    if (q) {
      rk = ` (content like '%${q}%'
                       or answerone LIKE '%${q}%'
                       or answertwo LIKE '%${q}%'
                       or answerthree LIKE '%${q}%'
                       or answerfour LIKE '%${q}%')`;
      offset = 0;
    }

    if (ids) {
      allcateid = `id in (${ids})`;
      if (+ids.split(',').length === 1) {
        offset = 0;
      }
    }
    if (parentid) {
      allcateid = `parentid in (${parentid})`;
    }
    // 🚀 性能优化：针对高页码的查询优化
    let sqlquery;
    console.log(`🔍 正常流程查询参数: fastMode=${fastMode}, page=${page}, offset=${offset}`);

    if (fastMode && page > 1000 && ord === 'id' && orderby === 'desc') {
      console.log('🚀 使用正常流程超高页码优化查询');
      // 🔥 超高页码优化：使用子查询避免大offset
      sqlquery = `select * from ${biao}
                  where id <= (
                    select id from ${biao}
                    where ${cuo} and ${allcateid} and ${rk}
                    order by ${ord} ${orderby}
                    limit 1 offset ${offset}
                  )
                  and ${cuo} and ${allcateid} and ${rk}
                  order by ${ord} ${orderby}
                  limit ${per}`;
    } else {
      console.log('🔍 使用正常流程普通查询');
      sqlquery = `select *
                  from ${biao}
                  where ${cuo}
                    and ${allcateid}
                    and ${rk}
                  order by ${ord} ${orderby} limit ${per}
                  offset ${offset} `;
    }
    //huoqutimu

    console.log('🔍 SQL查询:', sqlquery.substring(0, 100) + '...');

    // 🚀 性能优化：快速模式下跳过COUNT查询
    let totalsql;
    if (!fastMode) {
      totalsql = `select count(id) as total
                  from ${biao}
                  where ${cuo}
                    and ${allcateid}`;
    }
    if (+t === 1) {
      sqlquery = `SELECT *
                  FROM fbsy
                  where allcateid like '%656602%'
                    and allcateid not like '%796885%'
                    and allcateid not like '%796962%'
                    and allcateid not like '%796963%'
                    and allcateid not like '%796964%'
                    and allcateid not like '%796965%'
                  ORDER BY source desc limit ${per}
                  offset ${offset}`;
      totalsql = `SELECT count(id) as total
                  FROM fbsy
                  where allcateid like '%656602%'
                    and allcateid not like '%796885%'
                    and allcateid not like '%796962%'
                    and allcateid not like '%796963%'
                    and allcateid not like '%796964%'
                    and allcateid not like '%796965%'`;
    }

    if (+gen === 1) {
      let sqlx = await ctx.service.xr['query'](
        `SELECT *
         FROM fbkaojuan
         WHERE sid = ${kjid};`,
      );
      // sqlquery = `SELECT * FROM ${biao} WHERE id IN (${sqlx[0].questionIds})`;
      sqlquery = `SELECT *
                  FROM ${biao}
                  WHERE id IN (${sqlx[0].questionIds})
                  ORDER BY FIELD(id, ${sqlx[0].questionIds})`;
    }

    // 🚀 性能优化：快速模式下跳过COUNT查询
    let pagetotal;
    if (fastMode) {
      console.log('🚀 快速模式：跳过COUNT查询，使用估算总数');
      pagetotal = [{ total: Math.max(page * per, 100000) }];
    } else {
      console.log('🔍 执行COUNT查询');
      pagetotal = await ctx.service.xr['query'](totalsql);
    }

    console.log('🔍 开始执行主查询');
    let data = await ctx.service.xr['query'](sqlquery);
    console.log('🔍 主查询完成，返回', data.length, '条记录');

    // 🚀 快速模式：返回所有字段，但跳过复杂的数据处理（parentContent、video等）
    if (fastMode) {
      console.log('🚀 timu快速模式：返回所有字段，跳过复杂数据处理');

      // 🎯 计算正确的题目编号：考虑分页和kjid模式
      const page = Number(ctx.query.page) || 1;
      const per = Number(ctx.query.per) || 1;
      const kjid = ctx.query.kjid;

      const fastResult = data.map((item, index) => {
        // 🔥 计算正确的题目编号
        let questionNumber;
        if (kjid) {
          // 新模式：kjid存在时，题目编号就是当前页码
          questionNumber = page;
        } else {
          // 传统模式：分页模式，计算实际编号
          questionNumber = (page - 1) * per + index + 1;
        }

        // 🚀 性能优化：减少字符串处理开销
        const result = { ...item };

        // 只在需要时进行字符串替换
        if (item.content && item.content.includes('>')) {
          result.content = item.content.replace(
            '>',
            `>${questionNumber}.(${item.correctRatio ? Math.round(item.correctRatio) : '0'}%)`,
          );
        }

        console.log(`(${item.correctRatio ? Math.round(item.correctRatio) : '0'}%)`);
        // 优化时间格式化
        if (item.createdTime) {
          result.createdTime = dateformat(item.createdTime);
        }

        // 🔧 使用统一的选项格式化函数（优化模式）
        formatAnswerOptions(result, true);

        return result;
      });
      ctx.body = { pagetotal, data: fastResult };
      return;
    }

    // console.log(data);

    function isString(value) {
      return typeof value === 'string';
    }

    function mapOrder(array, order, key) {
      const indexMap = order.reduce((m, id, idx) => {
        m[id] = idx;
        return m;
      }, {});
      return array.sort((a, b) => {
        const ia = indexMap[a[key]];
        const ib = indexMap[b[key]];
        // 若有 id 不在 order 中，可放在末尾
        return (ia !== undefined ? ia : Infinity) - (ib !== undefined ? ib : Infinity);
      });
    }

    // 🔧 统一的选项格式化函数，消除重复代码
    function formatAnswerOptions(item, useOptimizedCheck = false, returnNew = false) {
      const options = ['answerone', 'answertwo', 'answerthree', 'answerfour'];
      const labels = ['A', 'B', 'C', 'D'];

      // 如果需要返回新对象，创建一个副本
      const target = returnNew ? {} : item;

      options.forEach((option, index) => {
        if (item[option]) {
          // 使用优化的检查方式（快速模式）或传统的正则匹配
          const hasP = useOptimizedCheck
            ? item[option].startsWith('<p>')
            : item[option].match(/<p>/g);

          target[option] = hasP
            ? item[option].replace('<p>', `<p>${labels[index]}.`)
            : `${labels[index]}.` + item[option];
        }
      });

      return target;
    }

    // 🔥 移除了 addvideo 函数，前端不需要视频数据

    async function processItems(data) {
      try {
        for (let index = 0; index < data.length; index++) {
          let item = data[index];
          if (item?.ds) {
            if (item.ds.match(/```/g)) {
              await ctx.service.xr['update'](biao, {
                ds: item.ds.replace(/```/g, ''),
                id: item.id,
              });
            }
            // item.ds = item.ds.replace(/```/g, '');
          }
          if (
            isString(item.answerone) &&
            isString(item.answertwo) &&
            isString(item.answerthree) &&
            isString(item.answerfour) &&
            biao !== 'rk'
          ) {
            item.content = isString(item.content)
              ? item.content.match(/<p>/g)
                ? item.content.replace(
                    '<p>',
                    `<p>(${item.correctRatio ? Math.round(item.correctRatio) : '0'}%)`,
                  )
                : `<span style="color:${
                    item.choice !== null ? '#00BFFF' : '#3c464f'
                  }">${offset + index + 1}</span>.` + item.content
              : item.content;
            item.createdTime = dateformat(item.createdTime);
            // 🔧 使用统一的选项格式化函数（传统模式）
            formatAnswerOptions(item, false);
          } else {
            console.error('Error: One of the answer properties is not a string');
            // console.log(item);
          }
        }
        return data;
      } catch (error) {
        console.error('Error:', error);
      }
    }

    if (ids?.includes(',')) {
      data = mapOrder(data, ids.split(','), 'id');
    }
    data = await processItems(data);

    // console.log(ids.split(','));

    if (+b === 1) {
      // console.log(data);
      ctx.body = { pagetotal, data: data };
      return;
    }

    ctx.body = { pagetotal, data: data };
  }

  async gettimurank() {
    const { ctx, app, logger } = this;
    let { biao, id, allcateid, fast } = ctx.query;
    const fastMode = fast === '1' || fast === 'true';
    let cateids = allcateid.split(',');

    console.log(
      `🔍 gettimurank: biao=${biao}, id=${id}, cateids=${cateids.length}个, fastMode=${fastMode}`,
    );

    try {
      // 🚀 优化1：批量获取分类信息，减少数据库连接
      console.log('🔍 批量获取分类信息');
      const cateQuery = `SELECT id, name FROM fbsycate WHERE id IN (${cateids.join(',')})`;
      const cateResults = await ctx.service.xr['query'](cateQuery);

      const cateMap = {};
      cateResults.forEach((item) => {
        cateMap[item.id] = item.name;
      });

      // 🚀 优化2：限制并发数量，避免连接池耗尽
      console.log('🔍 开始排名查询，限制并发数');
      const results = [];

      // 分批处理，每批最多3个并发查询
      const batchSize = 3;
      for (let i = 0; i < cateids.length; i += batchSize) {
        const batch = cateids.slice(i, i + batchSize);

        const batchPromises = batch.map(async (item) => {
          try {
            const rankResult = await ctx.service.xr['query'](`
              SELECT COUNT(*) + 1 AS rank
              FROM ${biao}
              WHERE allcateid LIKE '%${item}%'
                AND id > ${id}
            `);

            return {
              rank: rankResult[0].rank,
              name: cateMap[item] || `分类${item}`,
              cateid: parseInt(item),
            };
          } catch (error) {
            console.error(`🚨 分类${item}排名查询失败:`, error);
            return {
              rank: 999,
              name: cateMap[item] || `分类${item}`,
              cateid: parseInt(item),
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // 批次间稍微延迟，减轻数据库压力
        if (i + batchSize < cateids.length) {
          await new Promise((resolve) => setTimeout(resolve, 10));
        }
      }

      console.log('🔍 排名查询完成:', results.length, '个分类');
      ctx.body = { res: results };
    } catch (error) {
      console.error('🚨 gettimurank 执行失败:', error);
      ctx.body = {
        res: cateids.map((item) => ({
          rank: 999,
          name: `分类${item}`,
          cateid: parseInt(item),
        })),
      };
    }
  }

  async getds() {
    const { ctx, app, logger } = this;
    let { biao } = ctx.query;
    let body = ctx.request.body;
    let data = body.data;
    for (let item of data) {
      let res = await ctx.service.xr['find'](biao, {
        id: item.id,
      });
      item.ds = res.data.ds;
    }
    ctx.body = data;
  }

  async cha() {
    const { ctx, app } = this;
    let { typeid, timu } = ctx.query;
    typeid = +typeid;
    let biao = 'fbgwy';
    if (+typeid === 656604) {
      biao = 'fbsy';
    } else if (+typeid === 48644) {
      biao = 'fbgwy';
    } else if (+typeid === 783922) {
      biao = 'fbgwygk';
    } else if (+typeid === 786412) {
      biao = 'fbjs';
    }

    let sqlquery = `SELECT *
                    FROM ${biao}
                    where content like '%${timu}%'
                       or answerone LIKE '%${timu}%'
                       or answertwo LIKE '%${timu}%'
                       or answerthree LIKE '%${timu}%'
                       or answerfour LIKE '%${timu}%' limit 6`;
    let data = await ctx.service.xr['query'](sqlquery);
    // console.log(data);

    data = await Promise.all(
      data.map(async (item, index) => ({
        ...item,
        content: item.content,
        //createdTime转换成24小时格式
        createdTime: dateformat(item.createdTime),
        // 🔧 使用统一的选项格式化函数（返回新对象模式）
        ...formatAnswerOptions(item, false, true),
        video: await this.addvideo(item.id),
      })),
    );
    ctx.body = { data: data };
  }

  async addvideo(id) {
    const { ctx, app } = this;

    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    const response = await axios.get(
      `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${id}&tiku_prefix=xingce&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );

    let datalist = response.data.data;
    let x = '';
    let highestResolutionUrl = '';
    let maxResolution = 0;
    for (let item in datalist) {
      if (datalist[item] && datalist[item][0] && datalist[item][0].id) {
        const res = await axios.get(
          `https://ke.fenbi.com/api/gwy/v3/episodes/${datalist[item][0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers },
        );

        for (let item of res.data.datas) {
          if (item) {
            const resolution = item.height * item.width;
            if (resolution > maxResolution) {
              maxResolution = resolution;
              highestResolutionUrl = item.url;
            }
          }
        }
        x = highestResolutionUrl;
      }
    }
    return x;
  }

  async chazlfx() {
    const { ctx, app } = this;
    let { typeid, timu } = ctx.query;
    typeid = +typeid;
    let biao = 'fbgwyzlfxjs';
    if (typeid === 656604) {
      biao = 'fbsyzlfx';
    } else if (typeid === 48644) {
      biao = 'fbgwyzlfx';
    }

    let sqlquery = `SELECT *
                    FROM ${biao}
                    where content like '%${timu}%'
                       or answerone LIKE '%${timu}%'
                       or answertwo LIKE '%${timu}%'
                       or answerthree LIKE '%${timu}%'
                       or answerfour LIKE '%${timu}%' limit 1`;
    let data = await ctx.service.xr['query'](sqlquery);
    let x = [];
    let data1 = await ctx.service.xr['query'](
      `select *
       from ${biao}
       where id = ${data[0] && data[0].parentid ? data[0].parentid : data[0].id}`,
    );
    let timux = data1[0];
    // console.log(timux);
    x.push(timux);
    let questions = await ctx.service.xr['query'](
      `select *
       from ${biao}
       where parentid = ${timux.parentid ? timux.parentid : timux.id} limit 5`,
    );
    questions = await Promise.all(
      questions.map(async (item, index) => ({
        ...item,
        content: item.content.replace('>', `>${index + 1}.`),
        //createdTime转换成24小时格式
        createdTime: dateformat(item.createdTime),
        // 🔧 使用统一的选项格式化函数（返回新对象模式）
        ...formatAnswerOptions(item, false, true),
        video: await this.addvideo(item.id),
      })),
    );

    x.push(questions);

    ctx.body = { data: x };
  }

  async recate() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let sql = `SELECT fbgwy.id
               FROM fbgwy
                      LEFT JOIN fbgwyfullcate ON fbgwy.id = fbgwyfullcate.questionid
               WHERE fbgwyfullcate.questionid IS NULL
               ORDER BY fbgwy.id DESC LIMIT 200;
    `;
    let questionList = await ctx.service.xr['query'](sql);

    // Extracting ids from the array of objects
    const ids = questionList.map((item) => item.id);

    // Joining ids with commas
    const commaSeparatedIds = ids.join(',');
    if (!ids) {
      let resc = await axios.get(
        `https://tiku.fenbi.com/api/xingce/solution/keypoints?ids=${commaSeparatedIds}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let rescate = resc.data;
      for (let item in questionList) {
        // console.log(questionList[item].id);
        for (const item1 of rescate[item]) {
          let sqlc = `SELECT *
                      FROM \`fbgwyfullcate\`
                      where questionId = ${questionList[item].id}
                        and cateid = ${item1.id}`;
          let res = await ctx.service.xr['query'](sqlc);
          if (!res[0]) {
            await ctx.service.xr['create']('fbgwyfullcate', {
              cateid: item1.id,
              name: item1.name,
              status: item.status,
              questionid: questionList[item].id,
            });
          } else {
            // console.log(res[0], '已经存在');
          }
        }
      }
      ctx.body = await ctx.service.xr['query'](
        'select count(DISTINCT questionId) as total from fbgwyfullcate',
      );
    } else {
      ctx.body = { info: '都有了' };
    }
  }

  async allcate() {
    const { ctx, app } = this;
    let sqlc = 'select id,allcateid from fbgwy where allcateid is null order by id desc';
    let res = await ctx.service.xr['query'](sqlc);
    for (let item in res) {
      let sql = `select cateid, questionid
                 from fbgwyfullcate
                 where questionid = ${res[item].id}`;
      let rescate = await ctx.service.xr['query'](sql);
      let textArray = [];
      for (let itemx in rescate) {
        let all = await this.getAllCategoryInfo('fbgwycate', rescate[itemx].cateid);
        const result = Object.values(all).join(',');
        textArray.push(result);
      }
      const text = textArray.join(',');
      const newTextArray = text.split(',');
      const uniqueTextArray = [...new Set(newTextArray)];
      const newTextArrayx = uniqueTextArray.join(',');
      // console.log(newTextArrayx);
      let sqla = `update fbgwy
                  set allcateid = '${newTextArrayx}'
                  where id = ${res[item].id}`;
      await ctx.service.xr['query'](sqla);
    }
  }

  async allcate1() {
    const { ctx, app } = this;
    let id = ctx.query.id;
    let res = await this.getAllCategoryInfo('fbgwycate', id);
    ctx.body = res;
  }

  async getAllCategoryInfo(biao, id) {
    const { ctx } = this;

    // Retrieve category information based on id
    let categoryInfo = await ctx.service.xr['find'](biao, { id });
    if (!categoryInfo.data) {
      // Handle the case where no category information is found
      return { error: id };
    }

    const { level, parentid } = categoryInfo.data;

    if (level === 0) {
      // Level 0 category
      return { catezero: categoryInfo.data.id };
    }

    if (level === 1) {
      // Level 1 category
      let parentCategory = await ctx.service.xr['find'](biao, {
        id: parentid,
      });
      return {
        catezero: parentCategory.data.id,
        cateone: categoryInfo.data.id,
      };
    }

    if (level === 2) {
      // Level 2 category
      let parentCategory = await ctx.service.xr['find'](biao, {
        id: parentid,
      });
      return {
        catezero: parentCategory.data.parentid,
        cateone: categoryInfo.data.parentid,
        catetwo: categoryInfo.data.id,
      };
    }

    if (level === 3) {
      // Level 3 category
      let parentCategory = await ctx.service.xr['find'](biao, {
        id: parentid,
      });
      let grandparentCategory = await ctx.service.xr['find'](biao, {
        id: parentCategory.data.parentid,
      });
      return {
        catezero: grandparentCategory.data.parentid,
        cateone: grandparentCategory.data.id,
        catetwo: categoryInfo.data.parentid,
        catethree: categoryInfo.data.id,
      };
    }
  }

  async tree() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let data = await axios
      .get('https://tiku.fenbi.com/api/syzc/categories', {
        params: {
          filter: 'keypoint',
          app: 'web',
          kav: 100,
          av: 100,
          hav: 100,
          version: '3.0.0.0',
        },
        headers: headers,
        withCredentials: true, // 等价于fetch中的credentials: 'include'
      })
      .then((response) => {
        return response.data;
      })
      .catch((error) => {
        console.error('请求失败:', error);
      });
    ctx.body = data;
  }

  async treejs() {
    const { ctx, app } = this;

    const query = 'SELECT * FROM fbjscate ORDER BY level ASC, parentid ASC, id ASC';
    let categories = await ctx.service.xr['query'](query);

    let discrepancies = []; // 全局数组来存储不一致的记录

    async function buildTree(categories, parentId = null) {
      return Promise.all(
        categories
          .filter((cat) => cat.parentid === parentId)
          .map(async (cat) => {
            const queryx = `SELECT count(id) as total
                            FROM fbjs
                            where allcateid like '%${cat.id}%'`;
            let categoriesx = await ctx.service.xr['query'](queryx);
            const querys = `SELECT total
                            FROM fbjscate
                            where id like '%${cat.id}%'`;
            let categoriess = await ctx.service.xr['query'](querys);

            cat.total = categoriesx[0].total;
            cat.totalsql = categoriess[0].total;
            if (cat.total !== cat.totalsql && cat.total !== 0) {
              let discrepancy = {
                id: cat.id,
                name: cat.name,
                total: cat.total,
                totalsql: cat.totalsql,
              };
              discrepancies.push(discrepancy); // 用全局数组来存储不一致的记录
            }
            cat.children = await buildTree(categories, cat.id);
            return cat;
          }),
      );
    }

    await buildTree(categories);
    discrepancies.sort((a, b) => a.total - b.total);
    ctx.body = discrepancies;
  }

  async tree1() {
    const { ctx, app } = this;
    let type = ctx.query.type || 'gwy';
    let biao = 'fbjs';
    let biaocate = 'fbjscate';
    let biaofullcate = 'fbjsfullcate';
    if (type === 'js') {
      biao = 'fbjs';
      biaocate = 'fbjscate';
      biaofullcate = 'fbjsfullcate';
    } else if (type === 'gwy') {
      biao = 'fbgwy';
      biaocate = 'fbgwycate';
      biaofullcate = 'fbgwyfullcate';
    } else if (type === 'zlfx') {
      biao = 'fbgwyzlfx';
      biaocate = 'fbgwycate';
      biaofullcate = 'fbgwyfullcate';
    } else if (type === 'sy') {
      biao = 'fbsy';
      biaocate = 'fbsycate';
      biaofullcate = 'fbsyfullcate';
    } else if (type === 'gj') {
      biao = 'fbsygj';
      biaocate = 'fbsygjcate';
      biaofullcate = 'fbsygjfullcate';
    } else if (type === 'gk') {
      biao = 'fbgwygk';
      biaocate = 'fbgwygkcate';
      biaofullcate = 'fbsygjfullcate';
    }
    const query = ` SELECT *
                    FROM ${biaocate}
                    ORDER BY level ASC, parentid ASC, id ASC`;
    let categories = await ctx.service.xr['query'](query);

    // console.log(categoriesx);
    const categoryTree = await buildTree(categories);

    async function buildTree(categories, parentId = null) {
      return Promise.all(
        categories
          .filter((cat) => cat.parentid === parentId)
          .map(async (cat) => {
            const queryx = `SELECT count(id) as total
                            FROM ${biao}
                            where allcateid like '%${cat.id}%'`;
            let categoriesx = await ctx.service.xr['query'](queryx);
            const querys = `SELECT total
                            FROM ${biaocate}
                            where id like '%${cat.id}%'`;
            let categoriess = await ctx.service.xr['query'](querys);
            cat.total = categoriesx[0].total;
            cat.totalsql = categoriess[0].total;

            cat.children = await buildTree(categories, cat.id); // 注意这里需要等待子树建立
            return cat;
          }),
      );
    }

    ctx.body = categoryTree;
  }

  async renewgen() {
    const { ctx, app } = this;
    const cookie = await app.redis.get('fbcookie');
    let response = await axios.get(`https://tiku.fenbi.com/api/syzc/category-exercises`, {
      params: {
        categoryId: 3,
        cursor: 0,
        count: 30,
        noCacheTag: 100,
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
      headers: {
        Cookie: cookie,
        'Content-Type': 'application/json;charset=UTF-8',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      },
    });
    ctx.body = response.data;
    return;
    const tableMapping = {
      js: { biao: 'fbjs', biaocate: 'fbjscate' },
      gwy: { biao: 'fbgwy', biaocate: 'fbgwycate' },
      zlfx: { biao: 'fbgwyzlfx', biaocate: 'fbgwycate' },
      sy: { biao: 'fbsy', biaocate: 'fbsycate' },
      gj: { biao: 'fbsygj', biaocate: 'fbsygjcate' },
      gk: { biao: 'fbgwygk', biaocate: 'fbgwygkcate' },
    };
    const query = `SELECT *
                   FROM fbkaojuan
                   ORDER BY id DESC LIMIT 10`;
    let categories = await ctx.service.xr['query'](query);
    const categoryQueries = categories.map(async (item) => {
      const { mode, cateid } = item;
      const { biaocate } = tableMapping[mode] || {};
      if (biaocate) {
        const queryx = `SELECT *
                        FROM ${biaocate}
                        WHERE id = ${cateid}`;
        let cate = await ctx.service.xr['query'](queryx);

        if (cate[0]) {
          item.name = cate[0].name || null;
          item.parent = cate[0].parent || null;
          item.mode = item.name.match(/资料分析/) ? 'zlfx' : item.mode;
        }
      }
    });

    await Promise.all(categoryQueries);

    ctx.body = categories;
  }

  async addhistory() {
    const { ctx, app } = this;
    const cookie = await app.redis.get('fbcookie1');
    let type = ctx.query.type || 'xingce';

    // 🚀 性能优化：添加超时和缓存
    const cacheKey = `fbhistory_${type}`;
    const forceRefresh = ctx.query.refresh === '1' || ctx.query.refresh === 'true';
    const cachedData = !forceRefresh ? await app.redis.get(cacheKey) : null;

    let response;
    if (cachedData) {
      // 如果有缓存且未过期（5分钟），直接使用缓存
      response = { data: JSON.parse(cachedData) };
      console.log('使用缓存数据');
    } else {
      // 添加请求超时设置
      console.log('请求外部API获取最新数据');
      response = await axios.get(`https://tiku.fenbi.com/api/${type}/category-exercises`, {
        params: {
          categoryId: 3,
          cursor: 0,
          count: 30,
          noCacheTag: Date.now(), // 使用时间戳避免缓存
          app: 'web',
          kav: 100,
          av: 100,
          hav: 100,
          version: '3.0.0.0',
        },
        headers: {
          Cookie: cookie,
          'Content-Type': 'application/json;charset=UTF-8',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        },
        timeout: 10000, // 10秒超时
      });

      // 🚀 智能缓存：检查数据是否有变化
      const newDataHash = require('crypto')
        .createHash('md5')
        .update(JSON.stringify(response.data.datas))
        .digest('hex');
      const oldDataHash = await app.redis.get(`${cacheKey}_hash`);

      if (newDataHash !== oldDataHash) {
        console.log('数据有更新，更新缓存');
        // 数据有变化，更新缓存
        await app.redis.setex(cacheKey, 300, JSON.stringify(response.data));
        await app.redis.setex(`${cacheKey}_hash`, 300, newDataHash);
      } else {
        console.log('数据无变化，延长缓存时间');
        // 数据无变化，延长缓存时间
        await app.redis.setex(cacheKey, 300, JSON.stringify(response.data));
      }
    }

    let data = response.data.datas;
    if (!data || !Array.isArray(data)) {
      ctx.body = [];
      return;
    }

    // 🚀 性能优化：批量查询现有记录
    const ids = data.map((item) => item.id);
    const existingRecords = await ctx.service.xr['query'](
      `SELECT id FROM fbkaojuan WHERE id IN (${ids.map(() => '?').join(',')})`,
      ids,
    );
    const existingIds = new Set(existingRecords.map((record) => record.id));

    // 🚀 性能优化：准备批量插入和更新的数据
    const insertData = [];
    const updateData = [];
    const x = [];

    for (let item of data) {
      const recordData = {
        userid: item.userId,
        id: item.id,
        sid: item.id,
        create_time: dateformat(item.createdTime),
        cateid: item.sheet.keypointId,
        questionIds: item.sheet.questionIds.join(','),
        mode: 'gk',
      };

      x.push(recordData);

      if (existingIds.has(item.id)) {
        updateData.push(recordData);
      } else {
        insertData.push(recordData);
      }
    }

    // 🚀 性能优化：批量执行数据库操作
    const promises = [];

    if (insertData.length > 0) {
      // 批量插入
      const insertSql = `INSERT INTO fbkaojuan (userid, id, sid, create_time, cateid, questionIds, mode) VALUES ${insertData
        .map(() => '(?, ?, ?, ?, ?, ?, ?)')
        .join(', ')}`;
      const insertParams = insertData.flatMap((item) => [
        item.userid,
        item.id,
        item.sid,
        item.create_time,
        item.cateid,
        item.questionIds,
        item.mode,
      ]);
      promises.push(ctx.service.xr['query'](insertSql, insertParams));
    }

    if (updateData.length > 0) {
      // 批量更新
      for (const item of updateData) {
        promises.push(ctx.service.xr['update']('fbkaojuan', item));
      }
    }

    await Promise.all(promises);

    console.log(
      `处理了 ${x.length} 条记录，插入 ${insertData.length} 条，更新 ${updateData.length} 条`,
    );
    ctx.body = x;
  }

  async sw() {
    const { ctx } = this;
    let sw = ctx.query.sw;
    const res = await ctx.service.xr['update'](
      'sw',
      {
        id: 1,
        sw: +sw,
      },
      {
        where: {
          name: 'timu',
        },
      },
    );
    // console.log(res);
    ctx.body = await ctx.service.xr['find']('sw', { name: 'timu' });
  }

  async updatetag() {
    const { ctx } = this;
    const { id, action = false, type } = ctx.query;

    let biao;
    if (type === 'gwy') {
      biao = 'fbgwy';
    } else if (type === 'sy') {
      biao = 'fbsy';
    }

    const find = await ctx.service.xr['find'](biao, { id });

    // console.log(find);
    if (action && action === 'update') {
      const res = await ctx.service.xr['update'](biao, {
        id,
        tag: find.data.tag + 1,
      });
      ctx.body = res || { data: 'ok' };
    } else if (action && action === 'zero') {
      const res = await ctx.service.xr['update'](biao, {
        id,
        tag: 0,
      });
      ctx.body = res || { data: 'ok' };
    } else {
      ctx.body = find;
    }
  }

  async update5000() {
    const { ctx } = this;
    let { id } = ctx.query;
    id = +id;
    let biao = 'yanyu5000';

    const find = await ctx.service.xr['find'](biao, { id });

    // console.log(find);
    const res = await ctx.service.xr['create'](biao, {
      id: id,
    });
    ctx.body = res || { data: 'ok' };
  }

  async updatezlfx5000() {
    const { ctx } = this;
    let { id, sort } = ctx.query;
    id = +id;
    sort = +sort;
    let biao = ctx.query.biao || 'yanyu5000';

    const find = await ctx.service.xr['find'](biao, { id });
    console.log(biao, id);
    console.log(find);

    if (find.data && find.data.id) {
      ctx.body = {
        message: '已经存在',
        code: 1,
      };
    } else {
      const res = await ctx.service.xr['create'](biao, {
        id: id,
      });
      ctx.body = { message: 'ok', code: 0 };
    }
  }

  async pushtimu() {
    const { ctx } = this;
    let body = ctx.request.body;
    // console.log(body);
    // ctx.body = body;
    let { id, sort } = ctx.query;
    id = +id;
    sort = +sort;
    let biao = ctx.query.biao || 'gzslcdb';

    const find = await ctx.service.xr['find'](biao, { id: body.id });
    let max = await ctx.service.xr['query'](`SELECT max(sort) as max
                                             from ${biao}`);
    console.log(max);
    body.sort = +max[0]?.max + 1 || 1;
    if (find.data && find.data.id) {
      ctx.body = {
        message: '已经存在',
        code: 1,
      };
    } else {
      const res = await ctx.service.xr['create'](biao, body);
      console.log(res.affectedRows);
      ctx.body = { message: 'ok', code: 0 };
    }
  }

  async downtimu() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let biao = ctx.query.biao || 'gzslcdb';

    let sqlquery = `select *
                    from ${biao}
                    order by sort asc`;
    let info5k = await ctx.service.xr['query'](sqlquery);
    // console.log(info5k);
    for (let item of info5k) {
      let x = [];
      // let biao1 = biao === `fb5000m` ? `fb5000zlfx` : `fbgwyzlfx`;
      // let zlfx = await ctx.service.xr['query'](
      //   `select * from ${biao1} where parentid = ${item.oid}`,
      // );

      // for (let r of zlfx) {
      //   x.push(r.id);
      // }
      // let ids = x.join(',');
      // console.log(item.sort, item.id, ids);

      let i = 0;
      let j = 0;

      let xurl = `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${item.id}&tiku_prefix=${item.type}&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`;
      console.log(xurl);
      const response = await axios.get(xurl, { headers });
      let datalist = response.data.data;
      let video_url = '';
      let highestResolutionUrl = '';
      let maxResolution = 0;
      let osrot = 0;
      for (let [key, value] of Object.entries(datalist)) {
        osrot++;
        if (value && value[0] && value[0].id) {
          const res = await axios.get(
            `https://ke.fenbi.com/api/gwy/v3/episodes/${value[0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers },
          );
          // console.log(res.data.datas);

          for (let item of res.data.datas) {
            if (item) {
              const resolution = item.height * item.width;
              if (resolution > maxResolution) {
                maxResolution = resolution;
                highestResolutionUrl = item.url;
              }
            }
            video_url = item.url;
          }
          // console.log(video_url);
          // const zlfxx = zlfx;
          if (video_url) {
            let m = {};
            // console.log(zlfxx);
            // for (let c of zlfxx) {
            //   // console.log(+c.id, +key);
            //   if (+c.id === +key) {
            //     // console.log(+c.id, video_url);
            //     m = c;
            //   }
            // }
            async function axiosretry(url, retries = 10) {
              let attempt = 0;
              while (attempt < retries) {
                try {
                  // const proxy = 'http://127.0.0.1:7897'; // 代理地址
                  const proxy = 'http://192.168.3.33:7893'; // 代理地址
                  const agent = new HttpsProxyAgent(proxy); // 创建代理实例
                  return await axios.get(url, {
                    responseType: 'stream',
                    httpAgent: agent,
                    httpsAgent: agent,
                    timeout: 10000,
                  }); // 尝试请求
                } catch (error) {
                  attempt++;
                  await ctx.service.feishu.fs3(`$Attempt ${attempt} failed: ${error.message}`);
                  console.error(`Attempt ${attempt} failed: ${error.message}`);
                  if (attempt >= retries) throw error; // 达到最大重试次数
                  await new Promise((resolve) => setTimeout(resolve, 2000)); // 重试前等待
                }
              }
            }

            const prepath = `E:\\0粉笔视频\\`;
            const zlfx = m.parentid ? m.parentid + `-` : '';
            const correctRatio = m.correctRatio ? Math.round(m.correctRatio) + `-` : '';
            const filename = `${item.mo}-${item.sort}-${item.id}-${item.correctRatio}.mp4`;
            console.log(filename);

            const path = biao;
            if (path) {
              if (!fs.existsSync(`${prepath}${path}`)) {
                fs.mkdirSync(`${prepath}${path}`);
              }
              if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
                continue;
              } else {
                const res = await axiosretry(video_url);
                res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
              }
            } else {
              if (!fs.existsSync(`${prepath}`)) {
                fs.mkdirSync(`${prepath}`);
              }
              if (fs.existsSync(`${prepath}${filename}`)) {
                continue;
              }
              res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
            }
            i++;
          }
          j++;
        }
      }
      console.log('下载完成', i, j);
    }
    ctx.body = info5k;
  }

  async dellastdata() {
    const { ctx } = this;
    let biao = ctx.query.biao;
    console.log(
      `DELETE
       FROM ${biao}
       WHERE sort = (SELECT MAX(sort) FROM ${biao});`,
    );
    const find = await ctx.service.xr['query'](
      `DELETE
      ${biao}
FROM
      ${biao}
      JOIN
      (
      SELECT
      MAX
      (
      sort
      )
      AS
      max_sort
      FROM
      ${biao}
      )
      AS
      max_record
      ON
      ${biao}
      .
      sort
      =
      max_record
      .
      max_sort;`,
    );
    if (find.affectedRows === 1) {
      ctx.body = { code: 0, message: '删除成功' };
    } else {
      ctx.body = { code: 1, message: '删除失败' };
    }
  }

  async jscuo() {
    const { ctx } = this;

    const find = await ctx.service.xr['query'](
      `select *
       from yanyu5000
       where 1 = 1
       order by sort asc`,
    );
    ctx.body = find;
  }

  async fix5000() {
    const { ctx } = this;
    let sql = `SELECT *
               FROM zlfx5000
               order by sort asc `;
    const res = await ctx.service.xr['query'](sql);
    // console.log(res);
    for (let item in res) {
      await ctx.service.xr.find('fbgwyzlfxjs', { id: res[item].id }).then((data) => {
        if (data.data.parentid) {
          ctx.service.xr['update']('zlfx5000', {
            id: res[item].id,
            parentid: data.data.parentid,
          });
        } else {
          ctx.service.xr['update']('zlfx5000', {
            id: res[item].id,
            parentid: res[item].id,
          });
        }
      });
    }
  }

  async zlfx5000jstogwyzlfx() {
    const { ctx } = this;
    let sql = `SELECT *
               FROM zlfx5000
               order by sort asc `;
    const res = await ctx.service.xr['query'](sql);
    // console.log(res);
    for (let item in res) {
      await ctx.service.xr.find('fbgwyzlfxjs', { id: res[item].id }).then((data) => {
        if (data.data.parentid) {
          ctx.service.xr['update']('zlfx5000', {
            id: res[item].id,
            parentid: data.data.parentid,
          });
        } else {
          ctx.service.xr['update']('zlfx5000', {
            id: res[item].id,
            parentid: res[item].id,
          });
        }
      });
    }
  }

  async copysql() {
    const { ctx } = this;

    // Step 1: Fetch data from the first table (fbgwy) in batches
    let sql = `SELECT *
               FROM fbgwy
               WHERE allcateid LIKE '%48641%'
               ORDER BY id DESC LIMIT 6000`;
    const res = await ctx.service.xr['query'](sql);

    // Extract ids from the first result set
    const ids = res.map((item) => item.id);

    // Step 2: Fetch data from the second table (fbgwy) using a single query with the IN clause
    if (ids.length > 0) {
      let sql1 = `SELECT *
                  FROM fbgwy
                  WHERE id IN (${ids.join(',')})`;
      const res1 = await ctx.service.xr['query'](sql1);

      // Create a map for quick lookup based on id
      const idMap = new Map(res1.map((item) => [item.id, item]));

      // Step 3: Iterate over the first result set and check existence in the second result set
      for (let item of res) {
        if (idMap.has(item.id)) {
          // console.log(item.id, '存在');
        } else {
          // console.log(item.id, '不存在');
          ctx.service.xr['create']('fbgwy', item);
        }
      }
    }

    // Step 4: Set the response body
    ctx.body = {
      fbgwy: res,
    };
  }

  async zhiliao() {
    const { ctx } = this;
    let per = ctx.query.per || 1;
    let page = ctx.query.page || 1;
    const offset = (page - 1) * per;

    let sql = `SELECT id, content
               FROM fbgwyzlfx
               where parentid is null
                 and answerone is null
               order by id desc limit ${per}
               offset ${offset} `;

    const res = await ctx.service.xr['query'](sql);

    function isString(value) {
      return typeof value === 'string';
    }

    let x = [];
    let index = 0;
    let j = 1;
    let total = await ctx.service.xr['query'](
      `select count(id) as count
       from fbgwyzlfx
       where parentid is null`,
    );
    for (let item in res) {
      let sql1 = `SELECT *
                  FROM fbgwyzlfx
                  where parentid = ${res[item].parentid ? res[item].parentid : res[item].id}`;
      // console.log(sql1);

      res[item].children = await ctx.service.xr['query'](sql1);
      if (res[item].children.length >= 1) {
        res[item].total = total[0].count;
        res[item].sort = j;
        res[item].content = isString(res[item].content)
          ? res[item].content.match(/<p>/g)
            ? res[item].content.replace('<p>', `<p>${offset + index + 1}.`)
            : `${offset + index + 1}.` + res[item].content
          : item.content;
        x.push(res[item]);
        j++;
      }
      index++;
    }
    ctx.body = x;
  }

  async zhiliaoall() {
    const { ctx, app } = this;

    async function addvideo(id) {
      let cookie = await app.redis.get('fbcookie1');
      let headers = {
        Cookie: cookie,
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      };

      const response = await axios.get(
        `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${id}&tiku_prefix=xingce&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );

      let datalist = response.data.data;
      let x = '';
      let highestResolutionUrl = '';
      let maxResolution = 0;
      for (let item in datalist) {
        if (datalist[item] && datalist[item][0] && datalist[item][0].id) {
          const res = await axios.get(
            `https://ke.fenbi.com/api/gwy/v3/episodes/${datalist[item][0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers },
          );

          for (let item of res.data.datas) {
            if (item) {
              const resolution = item.height * item.width;
              if (resolution > maxResolution) {
                maxResolution = resolution;
                highestResolutionUrl = item.url;
              }
            }
          }
          x = highestResolutionUrl;
        }
      }
      return x;
    }

    let per = ctx.query.per || 19;
    let page = ctx.query.page || 1;
    let biao = ctx.query.biao || 'zlfx5000';
    const offset = (page - 1) * per;
    let sql = `SELECT ${biao}.*, fbgwyzlfx.*
               FROM ${biao}
                      JOIN fbgwyzlfx ON ${biao}.id = fbgwyzlfx.id
               ORDER BY ${biao}.sort ASC LIMIT ${per}
               OFFSET ${offset};`;

    const res = await ctx.service.xr['query'](sql);

    //合并res和res1

    function isString(value) {
      return typeof value === 'string';
    }

    let x = [];
    let index = 0;
    let j = 1;
    for (let item in res) {
      let sql1 = `SELECT *
                  FROM fbgwyzlfx
                  where parentid = ${
                    res[item].parentid ? res[item].parentid : res[item].id
                  } limit 5`;
      // console.log(sql1);
      res[item].children = await ctx.service.xr['query'](sql1);
      if (res[item].children.length >= 1) {
        for (let child of res[item].children) {
          // let video = await addvideo(child.id);
          // console.log(video);
          // child.video = video;
        }
        res[item].sort = j;
        res[item].content = isString(res[item].content)
          ? res[item].content.match(/<p>/g)
            ? res[item].content.replace('<p>', `<p>${offset + index + 1}.`)
            : `${offset + index + 1}.` + res[item].content
          : item.content;
        x.push(res[item]);
        j++;
      }
      index++;
    }
    ctx.body = x;
  }

  async zlfx5000() {
    const { ctx, app } = this;
    let per = ctx.query.per || 8;
    let page = ctx.query.page || 1;
    let biao = ctx.query.biao || 'fb5000m';
    biao = biao === 'fb5000m' ? 'fb5000m' : 'gzzlfxm';
    let biao1 = biao === 'fb5000m' ? 'fb5000zlfx' : 'gzzlfx';
    const offset = (page - 1) * per;
    let sql = `SELECT *
               FROM ${biao}
               ORDER BY ${biao}.sort ASC LIMIT ${per}
               OFFSET ${offset};`;
    const res = await ctx.service.xr['query'](sql);
    for (let item of res) {
      let sql = `SELECT *
                 FROM ${biao1}
                 where parentid = ${item.oid}
                 ORDER BY id ASC `;
      item.timu = await ctx.service.xr['query'](sql);
    }
    ctx.body = res;
  }

  async yyall() {
    const { ctx } = this;
    // let sql1 = `select * from fbgwy`;
    // const res1 = await ctx.service.xr['query'](sql1);
    // for (let item in res1) {
    //   console.log(res1[item].id);
    //   let sql2 = `select * from fbgwygk where id = ${res1[item].id}`;
    //   const res2 = await ctx.service.xr['query'](sql2);
    //   if (res2.length === 0) {
    //     const res3 = await ctx.service.xr['create']('fbgwygk', res1[item]);
    //     console.log(res3);
    //     if (res3.affectedRows === 1) {
    //       console.log(res1[item].id, '插入成功');
    //     }
    //   }
    // }
    //
    // return;

    let per = ctx.query.per || 19;
    let page = ctx.query.page || 1;
    let biao = ctx.query.biao || 'yanyu';
    const offset = (page - 1) * per;
    // let sql = `SELECT yygk.*, fbgwy.*
    // FROM yygk
    // LEFT JOIN fbgwy ON yygk.id = fbgwy.id
    // ORDER BY yygk.sort ASC
    // LIMIT ${per} OFFSET ${offset};`;
    let sql = `SELECT *
               FROM ${biao}
               ORDER BY ${biao}.sort ASC
                 LIMIT ${per}
               OFFSET ${offset};`;
    const res = await ctx.service.xr['query'](sql);
    let list = [];
    for (let item in res) {
      let sql1 = `SELECT *
                  FROM fbgwy
                  where id = ${res[item].id} limit 1`;
      const res1 = await ctx.service.xr['query'](sql1);
      if (res1.length === 0) {
        let sql2 = `SELECT *
                    FROM fbgwygk
                    where id = ${res[item].id} limit 1`;
        const res2 = await ctx.service.xr['query'](sql2);
        if (res2.length === 0) {
          let sql3 = `SELECT *
                      FROM fbsy
                      where id = ${res[item].id} limit 1`;
          const res3 = await ctx.service.xr['query'](sql3);
          list.push(res3[0]);
        } else {
          list.push(res2[0]);
        }
      } else {
        list.push(res1[0]);
      }
    }

    ctx.body = list;
  }

  async catesy() {
    const { ctx } = this;
    let cateid = ctx.query.cateid || 656598;
    let per = ctx.query.per || 19;
    let page = ctx.query.page || 1;
    const offset = (page - 1) * per;
    let sql = `SELECT *
               FROM fbsy
               where allcateid like '%${cateid}%'
                 and source like '%福建省事业%'
                 and (source like '%2023%' or source like '%2022%' or source like '%2021%' or source like '%2020%' or
                      source like '%2019%' or source like '%2018%' or source like '%2017%' or source like '%2016%' or
                      source like '%2015%' or source like '%2014%')
               ORDER BY source desc LIMIT ${per}
               OFFSET ${offset};`;
    ctx.body = await ctx.service.xr['query'](sql);
  }

  async choice() {
    const ctx = this.ctx;
    let x = ctx.request.body;
    let biao = '';
    if (x.type === 'sy' || x.type === 'syzc') {
      biao = 'fbsy';
    } else if (x.type === 'gwy') {
      biao = 'fbgwy';
    } else if (x.type === 'gk') {
      biao = 'fbgwygk';
    } else if (x.type === 'js') {
      biao = 'fbjs';
    } else if (x.type === 'rk') {
      biao = 'rk';
    } else if (x.type === 'zlfx') {
      biao = 'fbgwyzlfx';
    }
    ctx.body = await ctx.service.xr['update'](biao, {
      id: x.id,
      choice: x.choice,
    });
  }

  async mncy() {
    const { ctx, app } = this;
    let biao = `fbmnkj`;
    let data = await ctx.service.xr['query'](
      `SELECT *
       FROM ${biao}
       where type = 202
       ORDER BY id asc`,
    );
    let x = [];
    for (let item of data) {
      let ana = item.A.includes('\t') ? item.A.split('\t') : item.A.split(' ');
      let anb = item.B.includes('\t') ? item.B.split('\t') : item.B.split(' ');
      let anc = item.C.includes('\t') ? item.C.split('\t') : item.C.split(' ');
      let and = item.D.includes('\t') ? item.D.split('\t') : item.D.split(' ');
      let timuid = item.id;
      let choice = item.choice;
      let oid = item.oid;
      for (let item of ana) {
        if (item.length === 4 && choice === '0') {
          x.push({ name: item, timu: timuid, oid: oid });
        }
      }
      for (let item of anb) {
        if (item.length === 4 && choice === '1') {
          x.push({ name: item, timu: timuid, oid: oid });
        }
      }
      for (let item of anc) {
        if (item.length === 4 && choice === '2') {
          x.push({ name: item, timu: timuid, oid: oid });
        }
      }
      for (let item of and) {
        if (item.length === 4 && choice === '3') {
          x.push({ name: item, timu: timuid, oid: oid });
        }
      }
    }
    // 统计每个name的出现次数，并记录每个name对应的所有timu
    let countMap = {};
    for (let item of x) {
      if (!countMap[item.name]) {
        countMap[item.name] = { count: 0, timus: [], oid: [] };
      }
      countMap[item.name].count++;
      countMap[item.name].timus.push(item.timu);
      countMap[item.name].oid.push(item.oid);
    }

    // 将统计结果转换为数组并排序，同时将timus列表用逗号连接
    let result = Object.keys(countMap)
      .map((name) => {
        return {
          name: name,
          count: countMap[name].count,
          timus: countMap[name].timus.join(','),
          oid: countMap[name].oid.join(','),
        };
      })
      .sort((a, b) => b.count - a.count);
    //输出x有几个name
    console.log(result.length);
    let cybiao = 'fbmncy1';
    for (let item of result) {
      let res = await ctx.service.xr['find'](cybiao, { name: item.name });
      if (!res.data) {
        await ctx.service.xr['create'](cybiao, item);
      } else {
        let x = {
          id: res.data.id,
          count: item.count,
        };
        await ctx.service.xr['update'](cybiao, x);
      }
    }
    ctx.body = result;
  }

  async getmncyjs() {
    const { ctx, app } = this;
    let chengyu = '兼容并蓄';
    let data = await ctx.service.xr['select'](`fbskcy`);
    data = data.data;
    for (let item of data) {
      if (!item.jieshi1) {
        let response = await axios.get(`https://www.zdic.net/hans/${item.name}`, {
          params: {},
          headers: {
            accept:
              'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
          },
          referrerPolicy: 'strict-origin-when-cross-origin',
        });
        const $ = cheerio.load(response.data);
        // console.log($.text());
        $('p').each(async function () {
          if ($(this).text().match('【解释】')) {
            console.log($(this).text());
            await ctx.service.xr['update'](`fbskcy`, {
              id: item.id,
              jieshi: $(this).text(),
            });
          }
        });
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }
  }

  async getmncy() {
    const { ctx, app } = this;
    let biao = ctx.query.biao || 'fbmncy';
    let type = ctx.query.type || '';
    let timus = ctx.query.timus || '1';
    let jiexi = ctx.query.jiexi || '1';
    let text = ctx.query.text || '1';
    let id = ctx.query.id || '1';
    let per = ctx.query.per || 30;
    let page = ctx.query.page || 1;
    let off = +page === 1 ? 0 : (page - 1) * per;
    let data;
    if (type === 'mnkj') {
      let sql = `select *
                 from fbmnkj
                 where id in (${timus}) `;
      data = await ctx.service.xr['query'](sql);
      let i = 1;
      for (let item of data) {
        item.content = item.content.replace('>', '>' + i + '.');
        item.A = `A.${item.A}`;
        item.B = `B.${item.B}`;
        item.C = `C.${item.C}`;
        item.D = `D.${item.D}`;
        item.solution = item.solution
          .replace(/A项/g, '<br/>A项')
          .replace(/B项/g, '<br/>B项')
          .replace(/C项/g, '<br/>C项')
          .replace(/D项/g, '<br/>D项');
        i++;
      }
    } else if (type === 'skkj') {
      let sql = `select *
                 from fbgwy
                 where id in (${timus}) `;
      data = await ctx.service.xr['query'](sql);
      console.log(timus);
      let i = 1;
      for (let item of data) {
        item.content = item.content.replace('>', '>' + i + '.');
        item.A = `A.${item.answerone}`;
        item.B = `B.${item.answertwo}`;
        item.C = `C.${item.answerthree}`;
        item.D = `D.${item.answerfour}`;
        item.solution = item.solution
          .replace(/A项/g, '<br/>A项')
          .replace(/B项/g, '<br/>B项')
          .replace(/C项/g, '<br/>C项')
          .replace(/D项/g, '<br/>D项');
        i++;
      }
    } else {
      data = await ctx.service.xr['select'](biao, {
        limit: +per,
        offset: +off,
      });
      console.log({
        limit: +per,
        offset: +off,
      });
      data = data.data;
    }
    if (jiexi !== '1') {
      await ctx.service.xr['update'](biao, {
        id: id,
        jieshi: text,
      });
    }
    // console.log(data);
    ctx.body = data;
  }

  async skcy() {
    const { ctx, app } = this;
    let biao = `fbgwy`;
    let data = await ctx.service.xr['query'](
      `SELECT *
       FROM ${biao}
       where allcateid like '%48641%'
       ORDER BY id desc`,
    );
    let x = [];
    for (let item of data) {
      let ana = item.answerone.includes('\t')
        ? item.answerone.split('\t')
        : item.answerone.split(' ');
      let anb = item.answertwo.includes('\t')
        ? item.answertwo.split('\t')
        : item.answertwo.split(' ');
      let anc = item.answerthree.includes('\t')
        ? item.answerthree.split('\t')
        : item.answerthree.split(' ');
      let and = item.answerfour.includes('\t')
        ? item.answerfour.split('\t')
        : item.answerfour.split(' ');
      let timuid = item.id;
      let anwser = item.answer;
      for (let item of ana) {
        if (item.length === 4 && anwser === 'A') {
          x.push({ name: item, timu: timuid });
        }
      }
      for (let item of anb) {
        if (item.length === 4 && anwser === 'B') {
          x.push({ name: item, timu: timuid });
        }
      }
      for (let item of anc) {
        if (item.length === 4 && anwser === 'C') {
          x.push({ name: item, timu: timuid });
        }
      }
      for (let item of and) {
        if (item.length === 4 && anwser === 'D') {
          x.push({ name: item, timu: timuid });
        }
      }
    }
    // 统计每个name的出现次数
    // 统计每个name的出现次数，并记录每个name对应的所有timu
    let countMap = {};
    for (let item of x) {
      if (!countMap[item.name]) {
        countMap[item.name] = { count: 0, timus: [] };
      }
      countMap[item.name].count++;
      countMap[item.name].timus.push(item.timu);
    }

    // 将统计结果转换为数组并排序，同时将timus列表用逗号连接
    let result = Object.keys(countMap)
      .map((name) => {
        return {
          name: name,
          count: countMap[name].count,
          timus: countMap[name].timus.join(','),
        };
      })
      .sort((a, b) => b.count - a.count);
    //输出x有几个name
    console.log(result.length);
    let cybiao = 'fbskcy1';
    for (let item of result) {
      let res = await ctx.service.xr['find'](cybiao, { name: item.name });
      if (!res.data) {
        await ctx.service.xr['create'](cybiao, item);
      } else {
        let x = {
          id: res.data.id,
          count: item.count,
        };
        await ctx.service.xr['update'](cybiao, x);
      }
    }
    ctx.body = result;
  }

  async choice1() {
    const ctx = this.ctx;
    let biao = 'fbchengyuzq';
    //计算up1的执行时间
    let start = new Date().getTime();
    let up1 = await ctx.service.xr['query'](
      `SELECT *
       FROM fbgwy
       where allcateid like '%48641%'
       ORDER BY id desc limit 20000`,
    );
    let end = new Date().getTime();
    console.log(end - start);
    for (let item in up1) {
      const ana = typeof up1[item].answerone === 'string' ? up1[item].answerone.split(' ') : [];
      const anb = typeof up1[item].answertwo === 'string' ? up1[item].answertwo.split(' ') : [];
      const anc = typeof up1[item].answerthree === 'string' ? up1[item].answerthree.split(' ') : [];
      const and = typeof up1[item].answerfour === 'string' ? up1[item].answerfour.split(' ') : [];

      async function loopchengyu(ana) {
        for (const item1 of ana) {
          if (item1.length < 5 && item1.length > 0) {
            let chaname = await ctx.service.xr['find'](
              biao,
              {
                name: item1,
              },
              {
                limit: 1,
              },
            );
            // console.log('什么意思', chaname);
            // console.log('什么意思' + item + '/' + up1.length, item1);

            if (chaname.data !== null) {
              await ctx.service.xr['update'](biao, {
                id: chaname.data.id,
                count: +chaname.data.count + 1,
                name: item1,
                jieshi: chaname.data.jieshi + ',' + up1[item].id,
              });
            } else {
              await ctx.service.xr['create'](biao, {
                name: item1,
                count: 1,
                jieshi: '' + up1[item].id,
              });
            }
          }
        }
      }

      if (Array.isArray(ana) && up1[item].answer === 'A') {
        await loopchengyu(ana);
      }
      if (Array.isArray(anb) && up1[item].answer === 'B') {
        await loopchengyu(anb);
      }
      if (Array.isArray(anc) && up1[item].answer === 'C') {
        await loopchengyu(anc);
      }
      if (Array.isArray(and) && up1[item].answer === 'D') {
        await loopchengyu(and);
      }

      // let x = [];
      // const chengyu1Promise = (item) => {
      //   return new Promise((resolve, reject) => {
      //     this.chengyu1(item)
      //       .then((res1) => {
      //         resolve({
      //           name: item,
      //           jieshi: res1,
      //         });
      //       })
      //       .catch((error) => {
      //         reject(error);
      //       });
      //   });
      // };
      //
      // for (let i = 0; i < res.length; i++) {
      //   try {
      //     let result = await chengyu1Promise(res[i]);
      //     x.push(result);
      //   } catch (error) {
      //     console.error(error);
      //   }
      // }

      //把count全部加起来
      // let total = 0;
      // for (let i = 0; i < counts.length; i++) {
      //   total += counts[i].count;
      // }
      // console.log(total);

      ctx.body = await ctx.service.xr['query'](
        `SELECT SUM(count) AS total_count
         FROM ${biao}`,
      );
    }
  }

  async chengyuupdate() {
    const ctx = this.ctx;
    let biao = 'fbchengyuzq';
    let up1 = await ctx.service.xr['select'](biao, {
      limit: 10000,
      orders: [['count', 'DESC']],
    });
    let data = up1.data;
    for (let i = 0; i < data.length; i++) {
      try {
        let result = await ctx.service.xr['find'](`fbchengyuall`, {
          name: data[i].name,
        });
        if (result.data && result.data.name && result.data.jieshi1) {
          await ctx.service.xr['update'](biao, {
            id: data[i].id,
            jieshi1: result.data.jieshi1,
          });
          // console.log(
          //   data[i].id,
          //   data[i].name,
          //   result.data.jieshi1,
          //   `${i} / ${data.length}`,
          // );
        } else {
        }
      } catch (error) {
        console.error(error);
      }
    }
  }

  async chengyuall() {
    const ctx = this.ctx;
    let up1 = await ctx.service.xr['select'](`fbchengyugwy`, {
      limit: 10000,
      orders: [['count', 'DESC']],
    });
    let data = up1.data;
    for (let i = 0; i < data.length; i++) {
      try {
        let result = await ctx.service.xr['find'](`fbchengyuall`, {
          name: data[i].name,
        });
        if (result.data && result.data.name) {
          await ctx.service.xr['query'](
            `update fbchengyuall
             set count = count + '${+data[i].count}'
             where name = '${result.data.name}'`,
          );
        } else {
          // console.log('不存在', data[i].name);
          await ctx.service.xr['create']('fbchengyuall', {
            name: data[i].name,
            count: data[i].count,
            jieshi1: data[i].jieshi1,
          });
        }
        // console.log(`${i} / ${data.length}`);
      } catch (error) {
        console.error(error);
      }
    }
  }

  async chengyu3(item) {
    const ctx = this.ctx;
    let biao = ctx.query.biao || `fbchengyuall`;
    let up1 = await ctx.service.xr['select'](biao, {
      limit: 10000,
      orders: [['count', 'DESC']],
    });
    ctx.body = up1.data;
  }

  async chengyu2(item) {
    const ctx = this.ctx;
    let up1 = await ctx.service.xr['select'](`fbchengyugwy`, {
      limit: 10000,
      orders: [['count', 'DESC']],
    });
    let data = up1.data;
    let x = [];
    const chengyu1Promise = (item) => {
      return new Promise((resolve, reject) => {
        this.chengyu1(item)
          .then((res1) => {
            // console.log(res1);
            resolve({
              name: item,
              jieshi: res1,
            });
          })
          .catch((error) => {
            reject(error);
          });
      });
    };

    for (let i = 0; i < data.length; i++) {
      try {
        let result = await chengyu1Promise(data[i].name);
        x.push(result);
        if (data[i].jieshi1 !== null) {
          // await ctx.service.xr['update']('fbchengyu', {
          //   id: chaname.data.id,
          //   count: +chaname.data.count + 1,
          //   name: item1,
          //   jieshi: chaname.data.jieshi + ',' + up1[item].id,
          // });
        } else {
          await ctx.service.xr['update']('fbchengyugwy', {
            id: data[i].id,
            jieshi1: result.jieshi,
          });
        }
      } catch (error) {
        console.error(error);
      }
    }

    ctx.body = x;
  }

  async chengyu() {
    let chengyu = '兼容并蓄';
    let response = await axios.get(`https://www.gushiju.net/chengyu/${chengyu}`, {
      params: {},
      headers: {
        accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'max-age=0',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
      },
      referrerPolicy: 'strict-origin-when-cross-origin',
    });
    const $ = cheerio.load(response.data);
    // console.log($.text());
    $('.item-chengyu .d-text').each(function () {
      // console.log($(this).text());
    });
    // console.log(`https://www.gushiju.net/chengyu/${chengyu}`);
  }

  async chengyu1(cy) {
    let response = await axios.get(`https://hanyu.baidu.com/zici/s`, {
      params: {
        from: 'aladdin',
        query: '成语字典',
        smpid: '',
        srcid: '51368',
        wd: cy,
      },
      headers: {
        accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'max-age=0',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
      },
      referrerPolicy: 'strict-origin-when-cross-origin',
    });
    const $ = cheerio.load(response.data);
    // console.log($.text());
    let res = '';
    $('#basicmean-wrapper p').each(function () {
      // console.log($(this).text().trim());
      res = $(this).text().trim();
    });
    return res;
  }

  async findp() {
    try {
      const { ctx } = this;
      let sql = `SELECT *
                 FROM fbgwy
                 ORDER BY id DESC`;
      let x = [];
      const res = await ctx.service.xr['query'](sql);
      for (let item of res) {
        let content = item.content;
        if (content.includes('<p>') && !content.includes('</p>')) {
          // console.log(item.id);
          x.push({
            id: item.id,
            content: item.content,
            source: item.source,
          });
        }
      }
      ctx.body = x;
    } catch (error) {
      console.error('An error occurred:', error);
    }
  }

  async remeberpage() {
    const { ctx } = this;
    const x = ctx.request.body;

    let kjid = x?.kjid || null;
    let cateid = x?.cateid || null;
    let page = x?.page;
    let type = x?.type;
    let biao = 'fbremeber';

    // 拼接 where 条件
    let whereClause = '';
    if (kjid) {
      whereClause = `where kjid = '${kjid}'`;
    } else if (cateid !== null && cateid !== undefined) {
      whereClause = `where cateid = ${cateid}`;
    }

    // 查询
    const findSql = `select *
                     from ${biao} ${whereClause}`;
    const find = await ctx.service.xr['query'](findSql);

    if (find && find.length > 0) {
      // 更新
      let updateSql = '';
      if (kjid) {
        updateSql = `update ${biao}
                     set page = ${page},
                         type = '${type}'
                     where kjid = '${kjid}'`;
      } else {
        updateSql = `update ${biao}
                     set page = ${page},
                         type = '${type}'
                     where cateid = ${cateid}`;
      }
      const res = await ctx.service.xr['query'](updateSql);
      ctx.body = res || { data: 'ok' };
    } else {
      // 插入
      const insertSql = `insert into ${biao} (cateid, kjid, catesid, page, type)
                         values (${cateid ? cateid : 'null'}, ${kjid ? `'${kjid}'` : 'null'},
                                 ${cateid ? `'${cateid}'` : 'null'}, ${page}, '${type}')`;
      const res = await ctx.service.xr['query'](insertSql);
      ctx.body = res || { data: 'ok' };
    }
  }

  async recoverpage() {
    const { ctx } = this;
    const x = ctx.query;

    let cateid = x.cateid || null;
    let kjid = x.kjid || null;
    let biao = 'fbremeber';

    let whereClause = '';
    if (kjid) {
      whereClause = `where kjid = '${kjid}'`;
    } else if (cateid !== null && cateid !== undefined) {
      whereClause = `where cateid = ${cateid}`;
    }

    const findSql = `select *
                     from ${biao} ${whereClause}`;
    const find = await ctx.service.xr['query'](findSql);
    ctx.body = find && find[0] ? { page: find[0].page } : { page: 1 };
  }

  async compleveltwo1() {
    const { ctx } = this;

    let level2 = `SELECT *
                  FROM fbsycate
                  where level = 2 `;
    let level3 = `SELECT *
                  FROM fbsycate
                  where level = 3`;
    const find = await ctx.service.xr['query'](level2);

    // console.log(find);
    for (let item of find) {
      const count_sql = await ctx.service.xr['query'](
        `SELECT count(id) as count
         FROM fbsy
         WHERE allcateid like '%${item.id}%'`,
      );
      if (item.total === count_sql[0].count) {
        // console.log(
        //   item.id,
        //   item.parentid,
        //   item.name,
        //   item.parent,
        //   item.total,
        //   count_sql[0].count,
        // );
        const SELECT_sql = await ctx.service.xr['query'](
          `SELECT *
           FROM fbsy
           WHERE allcateid like '%${item.id}%'`,
        );
        for (let itemx of SELECT_sql) {
          // console.log(itemx.allcateid);
          if (
            itemx.allcateid.match(item.id.toString()) &&
            !itemx.allcateid.match(item.parentid.toString())
          ) {
            // console.log(itemx.allcateid);
            await ctx.service.xr['update']('fbsy', {
              id: itemx.id,
              allcateid: itemx.allcateid.replace(
                item.id.toString(),
                item.id.toString() + ',' + item.parentid.toString(),
              ),
            });
          }
        }
        // console.log(SELECT_sql);
      }
    }
    ctx.body = find;
  }

  async compleveltwo() {
    const { ctx } = this;

    let level2 = `SELECT *
                  FROM fbsycate
                  where level = 2 `;
    let level3 = `SELECT *
                  FROM fbsycate
                  where level = 3
                    and parent = '中心理解题'`;
    const find = await ctx.service.xr['query'](level3);

    async function recateid(id) {}

    let text = '';
    let i = 0;
    let total = 0;
    for (let l3 of find) {
      const count_sql = await ctx.service.xr['query'](
        `SELECT count(id) as count
         FROM fbsy
         WHERE allcateid like '%${l3.id}%'`,
      );
      // console.log(l3.id, l3.parentid);
      text = text + `allcateid like '%${l3.id}%' or `;
      const SELECT_sql = await ctx.service.xr['query'](
        `SELECT *
         FROM fbsy
         WHERE allcateid like '%${l3.id}%'`,
      );

      if (l3.total === count_sql[0].count) {
        // console.log(
        //   l3.id,
        //   l3.parentid,
        //   l3.name,
        //   l3.parent,
        //   l3.total,
        //   count_sql[0].count,
        // );
      }
      for (let itemx of SELECT_sql) {
        total++;
        let level2 = `SELECT *
                      FROM fbsycate
                      where id = ${l3.parentid}`;
        const find2 = await ctx.service.xr['query'](level2);
        let level1 = `SELECT *
                      FROM fbsycate
                      where id = ${find2[0].parentid}`;
        const find1 = await ctx.service.xr['query'](level1);
        let newid = `${l3.id},${l3.parentid},${find2[0].parentid},${find1[0].parentid}`;
        // console.log(i + '/' + SELECT_sql.length);
        i++;
        newid = newid + ',' + itemx.allcateid;
        const arr = newid.split(',');
        const uniqueArr = [...new Set(arr)];
        let uniqueStr = uniqueArr.join(',');
        // console.log('================================');
        // console.log(itemx.allcateid);
        uniqueStr =
          uniqueStr + ',' + `${l3.id},${l3.parentid},${find2[0].parentid},${find1[0].parentid}`;
        // console.log(uniqueStr);
        // let x = await ctx.service.xr['update']('fbsy', {
        //   id: itemx.id,
        //   allcateid: uniqueStr,
        // });

        let x = await ctx.service.xr['query'](`
          update fbsy
          set allcateid = '${uniqueStr}'
          where id = ${itemx.id}`);
        // console.log(x);
        // console.log('================================');
      }
    }
    // console.log(text);
    // console.log(i);
    // console.log(total);
    ctx.body = find;
  }

  async setrkxjcookie() {
    const { ctx, app } = this;
    let cookies = ctx.request.body;
    app.redis.set('rkxjcookie', cookies.cookie);
    ctx.body = await app.redis.get('rkxjcookie');
  }

  async rkxj() {
    const { ctx, app } = this;
    const currentTime = Date.now();
    const randomPart = Math.floor(Math.random() * 1000000);
    const csRandomTime = `${currentTime}.${randomPart}`;
    let cookie = await app.redis.get('rkxjcookie');
    let xid = ctx.query.id;
    let biao = 'rk';
    let cateone = ctx.query.cateone;
    let cateonename = ctx.query.cateonename || 'xingce';
    let catetwo = ctx.query.catetwo || 'fbgwy2';
    let catetwoname = ctx.query.catetwoname || 'fbgwycate';
    const params = {
      recordId: xid,
      csRandomTime: csRandomTime,
    };
    let headers = {
      Authorization: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const data = await axios
        .get('https://xkxxkx.cn/chick/examWeb/getQuestionByRecordId', {
          params: params,
          headers: headers,
        })
        .then((res) => {
          return res.data;
        });
      const questions = data.data.weChatDoExamQuestionVOs;
      // console.log(questions);
      let index = 1;
      for (let item = 0; item < questions.length; item++) {
        let x = {};
        x.uuid = questions[item].id;
        x.timuno = questions[item].no / 1;
        x.content = questions[item].content;
        x.solution = questions[item].parse;

        if (questions[item] && questions[item].weChatDoExamAnswerVOs[0].content) {
          let options = questions[item].weChatDoExamAnswerVOs;
          x.answerone = options[0].content;
          x.answertwo = options[1].content;
          x.answerthree = options[2].content;
          x.answerfour = options[3].content;
          if (options[0].yesOrNo === true) {
            x.answer = 'A';
          }
          if (options[1].yesOrNo === true) {
            x.answer = 'B';
          }
          if (options[2].yesOrNo === true) {
            x.answer = 'C';
          }
          if (options[3].yesOrNo === true) {
            x.answer = 'D';
          }
        }

        x.cateone = cateone;
        x.cateonename = cateonename;
        x.catetwo = catetwo;
        x.catetwoname = catetwoname;
        x.createdTime = dateNow();

        let res = await ctx.service.xr['find'](biao, { uuid: x.uuid });
        // console.log(res);
        if (!res.data) {
          let w = await ctx.service.xr['create'](biao, x);
          // console.log(w);
        } else {
          await ctx.service.xr['update'](biao, x);
        }
        // console.log(index);
        index++;
      }

      let sqlm = `SELECT count(id) as total
                  FROM ${biao}`;
      let info = await ctx.service.xr['query'](sqlm);
      // console.log(info);
      // console.log(dateNow());
      ctx.body = { num: index - 1 };
    } catch (error) {
      if (error.response && error.response.data) {
        console.error(error.response.data);
        ctx.body = error.response.data;
      }
    }
  }

  async yanyu5000() {
    const { ctx, app } = this;
    let data = await ctx.service.xr['select']('zlfx5000');
    ctx.body = data.data;
  }

  async jskaojuanshumu() {
    const { ctx, app } = this;
    let type = ctx.query.type || '201,203';
    let q = ctx.query.q || '1=1';
    let per = ctx.query.per || '1';
    let page = ctx.query.page || '1';
    const offset = (page - 1) * per;
    let biao = ctx.query.biao || 'fbjskaojuan';
    let likesql =
      q === '1=1'
        ? '1=1'
        : `(content like '%${q}%' or A like '%${q}%' or B like '%${q}%' or C like '%${q}%' or D like '%${q}%' )`;
    let sql = `SELECT *
               FROM ${biao}
               where type in (${type})
                 and ${likesql} limit ${per}
               offset ${offset}`;
    // console.log(sql);
    ctx.body = await ctx.service.xr['query'](sql);
  }

  async getxxkaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let biao = 'fbjsxx';
    let kaojuanid = ctx.query.id;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/jszgxxjsz/papers/?toPage=0&pageSize=20&labelId=6491&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      const kaojuanlist = kaojuanpage.data.list;
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        console.log(paperMeta);
        let paperName = item.name;
        console.log(`获取第${j}份考卷`);

        const exercise = await axios.post(
          'https://tiku.fenbi.com/api/jszgxxjsz/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0',
          `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
              'content-type': 'application/x-www-form-urlencoded',
              priority: 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://www.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        let exerciseinfo = exercise.data;
        console.log(exerciseinfo.id);
        //等待3秒用promise
        console.log(`等待3秒提交考卷`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        await axios.post(
          `https://tiku.fenbi.com/api/jszgxxjsz/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          'status=1',
          { headers },
        );
        console.log(`等待3秒获取解析`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        const response = await axios.get(
          `https://tiku.fenbi.com/api/jszgxxjsz/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers: headers },
        );
        const data = response.data;
        let i = 0;
        let sortinfo = await ctx.service.xr['query'](
          `select min(sort) as sort
           from ` + biao,
        );

        let sortby = sortinfo[0].sort + 1;

        for (let item of data.solutions) {
          let obj = {};
          let material = '';
          obj.type = item.correctAnswer.type;

          if (i === 18 || i === 19 || i === 20) {
            obj.type = 204;
            if (!data.materials[0]) {
              data.materials[0] = { content: '' };
            } else if (!data.materials[1]) {
              data.materials[1] = { content: '' };
            } else if (!data.materials[2]) {
              data.materials[2] = { content: '' };
            }
            material = data.materials[i === 18 ? 0 : i === 19 ? 1 : 2].content;
          }
          obj.content = material + item.content;
          if (!item.accessories[0] === false && item.accessories[0].options) {
            let options = item.accessories[0].options;
            obj.A = options[0];
            obj.B = options[1];
            obj.C = options[2];
            obj.D = options[3];
          }
          obj.oid = item.id;
          obj.solution = i <= 14 ? item.solution : item.correctAnswer.answer;
          obj.choice = item.correctAnswer.choice;

          obj.sort = j;
          obj.source = item.source;
          obj.name = paperName;
          obj.createdTime = dateformat(item.createdTime);
          i++;
          await ctx.service.xr['create'](biao, obj);

          // let res = await ctx.service.xr['find'](biao, {
          //   oid: item.id,
          // });
          // if (!res.data) {
          //   if (j === 12) {
          //     console.log(obj, res);
          //   }
          // } else {
          // }
        }
        if (i !== 21) {
          console.log(`第${j}份不到21题`);
        }
        console.log(`第${j}份写到数据库完成`);
        j++;
        console.log(`等待3秒获取下一套`);
        let total = await ctx.service.xr['query'](
          `select count(id) as total
           from ` + biao,
        );
        total = total[0].total;
        console.log(`一共${total}题，${total / 21}套`);
        await new Promise((resolve) => setTimeout(resolve, 4000));
      }
      console.log(`获取结束`);
      ctx.body = 1;
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getjskaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let biao = 'fbjskaojuan';
    let kaojuanid = ctx.query.id;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/jszgjy/papers/?toPage=0&pageSize=26&labelId=6451&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      const kaojuanlist = kaojuanpage.data.list;
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        let paperName = item.name;
        console.log(`获取第${j}份考卷`);

        const exercise = await axios.post(
          'https://tiku.fenbi.com/api/jszgjy/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0',
          `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
              'content-type': 'application/x-www-form-urlencoded',
              priority: 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://www.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        let exerciseinfo = exercise.data;
        console.log(exerciseinfo.id);
        //等待3秒用promise
        console.log(`等待3秒提交考卷`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        await axios.post(
          `https://tiku.fenbi.com/api/jszgjy/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          'status=1',
          { headers },
        );
        console.log(`等待3秒获取解析`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        const response = await axios.get(
          `https://tiku.fenbi.com/api/jszgjy/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers: headers },
        );
        const data = response.data;
        let i = 0;
        let sortinfo = await ctx.service.xr['query'](
          `select min(sort) as sort
           from ` + biao,
        );

        let sortby = sortinfo[0].sort + 1;

        for (let item of data.solutions) {
          let obj = {};
          let material = '';
          if (i === 29 || i === 30) {
            material = data.materials[i === 29 ? 0 : 1].content;
          }
          obj.content = material + item.content;
          if (!item.accessories[0] === false) {
            let options = item.accessories[0].options;
            obj.A = options[0];
            obj.B = options[1];
            obj.C = options[2];
            obj.D = options[3];
          }
          obj.oid = item.id;
          obj.type = item.correctAnswer.type;
          obj.solution = i <= 20 ? item.solution : item.correctAnswer.answer;
          obj.choice = item.correctAnswer.choice;

          obj.sort = j;
          obj.source = item.source;
          obj.name = paperName;
          obj.createdTime = dateformat(item.createdTime);
          i++;
          await ctx.service.xr['create'](biao, obj);

          // let res = await ctx.service.xr['find'](biao, {
          //   oid: item.id,
          // });
          // if (!res.data) {
          //   if (j === 12) {
          //     console.log(obj, res);
          //   }
          // } else {
          // }
        }
        if (i !== 31) {
          console.log(`第${j}份不到31题`);
        }
        console.log(`第${j}份写到数据库完成`);
        j++;
        console.log(`等待3秒获取下一套`);
        let total = await ctx.service.xr['query'](
          `select count(id) as total
           from ` + biao,
        );
        total = total[0].total;
        console.log(`一共${total}题，${total / 31}套`);
        await new Promise((resolve) => setTimeout(resolve, 4000));
      }
      console.log(`获取结束`);
      ctx.body = 1;
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getmnkaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let biao = 'fbmnkj';
    let kaojuanid = ctx.query.id;
    let kjtype = 'xingce';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/${kjtype}/papers/?toPage=0&pageSize=15&labelId=3421&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      const kaojuanlist = kaojuanpage.data.list;
      kaojuanlist.reverse();
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        let paperName = item.name;

        let isexsit = await ctx.service.xr['find'](biao, { name: paperName });

        if (paperName.match('2025') && isexsit && isexsit.data === null) {
          console.log(`获取第${j}份考卷`);
          console.log(`${paperName}`);
          const exercise = await axios.post(
            `https://tiku.fenbi.com/api/${kjtype}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
            {
              headers: {
                accept: 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
                'content-type': 'application/x-www-form-urlencoded',
                priority: 'u=1, i',
                'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                cookie: cookie,
                Referer: 'https://www.fenbi.com/',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
              },
            },
          );
          let exerciseinfo = exercise.data;
          console.log(exerciseinfo.id);
          //等待3秒用promise
          console.log(`等待3秒提交考卷`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
          await axios.post(
            `https://tiku.fenbi.com/api/${kjtype}/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            'status=1',
            { headers },
          );
          console.log(`等待3秒获取解析`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
          const response = await axios.get(
            `https://tiku.fenbi.com/api/${kjtype}/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers: headers },
          );
          const data = response.data;
          let i = 0;
          let sortinfo = await ctx.service.xr['query'](
            `select min(sort) as sort
             from ` + biao,
          );

          let sortby = sortinfo[0].sort + 1;

          for (let item of data.solutions) {
            let obj = {};
            let material = '';
            if (i === 110 || i === 115 || i === 120 || (i === 125 && data.materials.length === 4)) {
              material = data.materials[i === 110 ? 0 : i === 115 ? 1 : i === 120 ? 2 : 3].content;
            } else if (
              i === 110 ||
              i === 115 ||
              i === 120 ||
              (i === 125 && data.materials.length === 5)
            ) {
              data.materials[i === 110 ? 1 : i === 115 ? 2 : i === 120 ? 3 : 4].content;
            }
            obj.content = material + item.content;
            if (!item.accessories[0] === false) {
              let options = item.accessories[0].options;
              obj.A = options[0];
              obj.B = options[1];
              obj.C = options[2];
              obj.D = options[3];
            }
            obj.oid = item.id;
            obj.type =
              i <= 19 ? 201 : i <= 59 ? 202 : i <= 69 ? 203 : i <= 79 ? 206 : i <= 109 ? 204 : 205;
            obj.solution = item.solution;
            obj.choice = item.correctAnswer.choice;

            obj.sort = j;
            obj.source = item.source;
            obj.name = paperName;
            obj.createdTime = dateformat(item.createdTime);
            i++;
            await ctx.service.xr['create'](biao, obj);

            // let res = await ctx.service.xr['find'](biao, {
            //   oid: item.id,
            // });
            // if (!res.data) {
            //   if (j === 12) {
            //     console.log(obj, res);
            //   }
            // } else {
            // }
          }
          if (i !== 130) {
            console.log(`第${j} ${paperName}份不到130题`);
          }
          console.log(`第${j}份写到数据库完成`);
          j++;
          console.log(`等待3秒获取下一套`);
          let total = await ctx.service.xr['query'](
            `select count(id) as total
             from ` + biao,
          );
          total = total[0].total;
          console.log(`一共${total}题，${total / 130}套`);
          await new Promise((resolve) => setTimeout(resolve, 3300));
        }
        console.log(`获取结束`);
        ctx.body = 1;
      }
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getgkkaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let biao = 'fbgkkj';
    let kaojuanid = ctx.query.id;
    let kjtype = 'xingce';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/${kjtype}/papers/?toPage=0&pageSize=100&labelId=1&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      let allname = '';
      const kaojuanlist = kaojuanpage.data.list;
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        let paperName = item.name;
        allname = allname + paperName + `\n`;
        let isexsit = await ctx.service.xr['find'](biao, { name: paperName });

        if (
          (paperName.match('2024') ||
            paperName.match('2023') ||
            paperName.match('2022') ||
            paperName.match('2021') ||
            paperName.match('2020') ||
            paperName.match('2019') ||
            paperName.match('2018') ||
            paperName.match('2017')) &&
          !paperName.match('省') &&
          isexsit &&
          isexsit.data === null
        ) {
          console.log(`获取第${j}份考卷`);
          console.log(`${paperName}`);
          const exercise = await axios.post(
            `https://tiku.fenbi.com/api/${kjtype}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
            {
              headers: {
                accept: 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
                'content-type': 'application/x-www-form-urlencoded',
                priority: 'u=1, i',
                'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                cookie: cookie,
                Referer: 'https://www.fenbi.com/',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
              },
            },
          );
          let exerciseinfo = exercise.data;
          console.log(exerciseinfo.id);
          //等待3秒用promise
          console.log(`等待3秒提交考卷`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
          await axios.post(
            `https://tiku.fenbi.com/api/${kjtype}/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            'status=1',
            { headers },
          );
          console.log(`等待3秒获取解析`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
          const response = await axios.get(
            `https://tiku.fenbi.com/api/${kjtype}/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers: headers },
          );
          const data = response.data;
          let i = 0;
          // let sortinfo = await ctx.service.xr['query'](
          //   `select min(sort) as sort
          //    from ` + biao,
          // );
          //
          // let sortby = sortinfo[0].sort + 1;
          // let materials = data.materials;

          for (let item of data.solutions) {
            let obj = {};
            let material = '';
            if (paperName.match('地市级') || paperName.match('行政执法')) {
              if (i === 105 || i === 110 || i === 115 || i === 120 || i === 125) {
                material =
                  data.materials[i === 105 ? 0 : i === 110 ? 1 : i === 115 ? 2 : i === 120 ? 3 : 4]
                    .content;
              }
              obj.type =
                i <= 19
                  ? 201
                  : i <= 59
                    ? 202
                    : i <= 69
                      ? 203
                      : i <= 79
                        ? 206
                        : i <= 109
                          ? 204
                          : 205;
            } else {
              if (i === 50 || i === 55 || i === 110 || i === 115 || i === 120 || i === 125) {
                material =
                  data.materials[
                    i === 50 ? 0 : i === 55 ? 1 : i === 110 ? 2 : i === 125 ? 3 : i === 120 ? 4 : 5
                  ].content;
              }
              obj.type =
                i <= 19
                  ? 201
                  : i <= 59
                    ? 202
                    : i <= 74
                      ? 203
                      : i <= 84
                        ? 206
                        : i <= 114
                          ? 204
                          : 205;
            }

            obj.content = material + item.content;
            if (!item.accessories[0] === false) {
              let options = item.accessories[0].options;
              obj.A = options[0];
              obj.B = options[1];
              obj.C = options[2];
              obj.D = options[3];
            }
            obj.oid = item.id;

            obj.solution = item.solution;
            obj.choice = item.correctAnswer.choice;

            obj.sort = j;
            obj.source = item.source;
            obj.name = paperName;
            obj.createdTime = dateformat(item.createdTime);
            i++;
            await ctx.service.xr['create'](biao, obj);

            // let res = await ctx.service.xr['find'](biao, {
            //   oid: item.id,
            // });
            // if (!res.data) {
            //   if (j === 12) {
            //     console.log(obj, res);
            //   }
            // } else {
            // }
          }
          if (i !== 130 || i !== 135) {
            console.log(`第${j} ${paperName}份不到130||135题`);
          }
          console.log(`第${j}份写到数据库完成`);
          j++;
          console.log(`等待3秒获取下一套`);
          let total = await ctx.service.xr['query'](
            `select count(id) as total
             from ` + biao,
          );
          total = total[0].total;
          console.log(`一共${total}题，${total / 130}套`);
          await new Promise((resolve) => setTimeout(resolve, 3300));
        }
        console.log(`获取结束`);
      }
      ctx.body = { allname: allname };
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getskkaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let biao = 'fbskkj1';
    let kaojuanid = ctx.query.id;
    let kjtype = 'xingce';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/${kjtype}/papers/?toPage=0&pageSize=15&labelId=4&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      const kaojuanlist = kaojuanpage.data.list;
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        let paperName = item.name;

        let isexsit = await ctx.service.xr['find'](biao, { name: paperName });

        if (
          (paperName.match('2025') ||
            paperName.match('2024') ||
            paperName.match('2023') ||
            paperName.match('2022') ||
            paperName.match('2021') ||
            paperName.match('2020') ||
            paperName.match('2019') ||
            paperName.match('2018') ||
            paperName.match('2017') ||
            paperName.match('2016') ||
            paperName.match('2015') ||
            paperName.match('2014')) &&
          !paperName.match('选调生') &&
          isexsit &&
          isexsit.data === null
        ) {
          console.log(`获取第${j}份考卷`);
          console.log(`${paperName}`);
          const exercise = await axios.post(
            `https://tiku.fenbi.com/api/${kjtype}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
            {
              headers: {
                accept: 'application/json, text/plain, */*',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
                'content-type': 'application/x-www-form-urlencoded',
                priority: 'u=1, i',
                'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-site',
                cookie: cookie,
                Referer: 'https://www.fenbi.com/',
                'Referrer-Policy': 'strict-origin-when-cross-origin',
              },
            },
          );
          let exerciseinfo = exercise.data;
          console.log(exerciseinfo.id);
          //等待3秒用promise
          console.log(`等待3秒提交考卷`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
          await axios.post(
            `https://tiku.fenbi.com/api/${kjtype}/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            'status=1',
            { headers },
          );
          console.log(`等待3秒获取解析`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
          const response = await axios.get(
            `https://tiku.fenbi.com/api/${kjtype}/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers: headers },
          );
          const data = response.data;
          let i = 0;
          let sortinfo = await ctx.service.xr['query'](
            `select min(sort) as sort
             from ` + biao,
          );

          let sortby = sortinfo[0].sort + 1;

          for (let item of data.solutions) {
            let obj = {};
            let material = '';
            if (i === 105 || i === 110 || i === 115) {
              material = data.materials[i === 105 ? 0 : i === 110 ? 1 : 2].content;
            }
            obj.content = material + item.content;
            if (!item.accessories[0] === false) {
              let options = item.accessories[0].options;
              obj.answerone = options[0];
              obj.answertwo = options[1];
              obj.answerthree = options[2];
              obj.answerfour = options[3];
            }
            obj.oid = item.id;
            obj.type = i <= 19 ? 201 : i <= 59 ? 202 : i <= 69 ? 203 : i <= 104 ? 204 : 205;
            obj.solution = item.solution;
            obj.choice = item.correctAnswer.choice;
            obj.answer =
              +item.correctAnswer.choice === 0
                ? 'A'
                : +item.correctAnswer.choice === 1
                  ? 'B'
                  : +item.correctAnswer.choice === 2
                    ? 'C'
                    : 'D';

            obj.sort = j;
            obj.source = item.source;
            obj.name = paperName;
            obj.createdTime = dateformat(item.createdTime);
            i++;
            await ctx.service.xr['create'](biao, obj);

            // let res = await ctx.service.xr['find'](biao, {
            //   oid: item.id,
            // });
            // if (!res.data) {
            //   if (j === 12) {
            //     console.log(obj, res);
            //   }
            // } else {
            // }
          }
          if (i !== 120) {
            console.log(`第${j} ${paperName}份不到120题`);
          }
          console.log(`第${j}份写到数据库完成`);
          j++;
          console.log(`等待3秒获取下一套`);
          let total = await ctx.service.xr['query'](
            `select count(id) as total
             from ` + biao,
          );
          total = total[0].total;
          console.log(`一共${total}题，${total / 120}套`);
          await new Promise((resolve) => setTimeout(resolve, 3300));
        }
        console.log(`获取结束`);
        ctx.body = 1;
      }
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getvideo() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let id = ctx.query.id;
    let type = ctx.query.type || 'syzc';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    const response = await axios.get(
      `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${id}&tiku_prefix=${type}&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let datalist = response.data.data;
    let x = '';
    let highestResolutionUrl = '';
    let maxResolution = 0;
    for (let item in datalist) {
      if (datalist[item] && datalist[item][0] && datalist[item][0].id) {
        const res = await axios.get(
          `https://ke.fenbi.com/api/gwy/v3/episodes/${datalist[item][0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers },
        );
        for (let item of res.data.datas) {
          if (item) {
            const resolution = item.height * item.width;
            if (resolution > maxResolution) {
              maxResolution = resolution;
              highestResolutionUrl = item.url;
            }
          }
        }
        x = highestResolutionUrl;
      }
    }
    ctx.body = x;
  }

  async getkj() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let kjid = ctx.query.kjid;
    let type = ctx.query.type || 'xingce';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    const response = await axios.get(
      `https://tiku.fenbi.com/api/${type}/universal/auth/solutions?type=0&id=${kjid}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let datalist = response.data;
    ctx.body = datalist;
  }

  async updatecorrectRatio() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let type = ctx.query.type || 'xingce';
    let biao = ctx.query.biao || 'fbgwy';

    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let sql = `select id
               from ${biao}
               where correctRatio is null
               order by id desc limit 100 `;
    let data = await ctx.service.xr['query'](sql);
    let shengyu = await ctx.service.xr['query'](
      `select count(id) as count
       from ${biao}
       where correctRatio is null
       order by id desc limit 100`,
    );
    console.log(+shengyu[0].count);
    if (+shengyu[0].count === 0) {
      ctx.body = { ok: 'ok' };
      return;
    }
    let ids = [];
    data.map((item) => {
      ids.push(item.id);
    });

    let id = ids.join(',');
    const response = await axios.get(
      `https://tiku.fenbi.com/api/${type}/question/meta?ids=${id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let res = response.data;
    for (let item of res) {
      // console.log(item);
      let num = item.correctRatio;
      let rounded = num.toFixed(2); // "56.95"，注意返回的是字符串
      let roundedNum = Number(rounded); // 变回数字
      if (+item.correctRatio !== 0) {
        let x = {
          id: item.id,
          correctRatio: roundedNum,
          mostWrongAnswer:
            item.mostWrongAnswer && item.mostWrongAnswer.choice ? item.mostWrongAnswer.choice : '',
        };
        await ctx.service.xr['update'](biao, x);
      }
    }
    ctx.body = res;
  }

  async updatecorrectRatio1() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let type = ctx.query.type || 'xingce';
    let biao = ctx.query.biao || 'fbskkj';

    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let sql = `select oid
               from ${biao}
               where correctRatio is null
               order by oid desc limit 100 `;
    let data = await ctx.service.xr['query'](sql);
    let shengyu = await ctx.service.xr['query'](
      `select count(oid) as count
       from ${biao}
       where correctRatio is null
       order by oid desc limit 100`,
    );
    console.log(+shengyu[0].count);
    if (+shengyu[0].count === 0) {
      ctx.body = { ok: 'ok' };
      return;
    }
    let ids = [];
    data.map((item) => {
      ids.push(item.oid);
    });

    let id = ids.join(',');
    const response = await axios.get(
      `https://tiku.fenbi.com/api/${type}/question/meta?ids=${id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let res = response.data;
    for (let item of res) {
      if (+item.correctRatio !== 0) {
        // console.log(item);
        let num = item.correctRatio;
        let rounded = num.toFixed(2); // "56.95"，注意返回的是字符串
        let roundedNum = Number(rounded); // 变回数字
        let upsql = `update ${biao}
                     set correctRatio   = ${roundedNum},
                         mostWrongAnswer=${item.mostWrongAnswer.choice}
                     where oid = ${item.id}`;
        // let x = {
        //   oid: item.id,
        //   correctRatio: item.correctRatio,
        //   mostWrongAnswer: item.mostWrongAnswer.choice,
        // };
        // await ctx.service.xr['update'](biao, x);
        await ctx.service.xr['query'](upsql);
      }
    }
    ctx.body = res;
  }

  async getcy() {
    const { ctx, app } = this;
    let { id, per, page, biao, z, b, t, gen, ids, cy } = ctx.query;
    const offset = (page - 1) * per;
    let sqlquery = `SELECT *
                    FROM ${biao}
                    where answerone like '%${cy}%'
                       or answertwo like '%${cy}%'
                       or answerthree like '%${cy}%'
                       or answerfour like '%${cy}%'
                    order by id desc limit ${per}
                    offset ${offset}`;
    console.log(sqlquery);
    let data = await ctx.service.xr['query'](sqlquery);
    // console.log(data);
    ctx.body = data;
  }

  async down5000() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let biao = `gzzlfxm`;
    let sqlquery = `select *
                    from ${biao}`;
    let info5k = await ctx.service.xr['query'](sqlquery);

    for (let item of info5k) {
      let x = [];
      let biao1 = biao === `gzzlfxm` ? `gzzlfx` : `fbgwyzlfx`;
      let zlfx = await ctx.service.xr['query'](
        `select *
         from ${biao1}
         where parentid = ${item.oid}`,
      );

      for (let r of zlfx) {
        x.push(r.id);
      }
      let ids = x.join(',');
      console.log(item.sort, item.id, ids);

      let i = 0;
      let j = 0;

      const response = await axios.get(
        `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${ids}&tiku_prefix=xingce&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let datalist = response.data.data;
      let video_url = '';
      let highestResolutionUrl = '';
      let maxResolution = 0;
      let osrot = 0;
      for (let [key, value] of Object.entries(datalist)) {
        osrot++;
        if (value && value[0] && value[0].id) {
          const res = await axios.get(
            `https://ke.fenbi.com/api/gwy/v3/episodes/${value[0].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
            { headers },
          );
          // console.log(res.data.datas);

          for (let item of res.data.datas) {
            if (item) {
              const resolution = item.height * item.width;
              if (resolution > maxResolution) {
                maxResolution = resolution;
                highestResolutionUrl = item.url;
              }
            }
            video_url = item.url;
          }

          // console.log(video_url);
          async function axiosretry(url, retries = 10) {
            let attempt = 0;
            while (attempt < retries) {
              try {
                // const proxy = 'http://127.0.0.1:7897'; // 代理地址
                const proxy = 'http://192.168.3.33:7893'; // 代理地址
                // const proxy = 'http://192.168.111.224:8080'; // 代理地址
                const agent = new HttpsProxyAgent(proxy); // 创建代理实例
                return await axios.get(url, {
                  responseType: 'stream',
                  httpAgent: agent,
                  httpsAgent: agent,
                  timeout: 10000,
                }); // 尝试请求
              } catch (error) {
                attempt++;
                await ctx.service.feishu.fs3(`$Attempt ${attempt} failed: ${error.message}`);
                console.error(`Attempt ${attempt} failed: ${error.message}`);
                if (attempt >= retries) throw error; // 达到最大重试次数
                await new Promise((resolve) => setTimeout(resolve, 2000)); // 重试前等待
              }
            }
          }

          const zlfxx = zlfx;
          if (video_url) {
            await new Promise((resolve) => setTimeout(resolve, 1000));

            let m = {};
            // console.log(zlfxx);
            for (let c of zlfxx) {
              // console.log(+c.id, +key);
              if (+c.id === +key) {
                // console.log(+c.id, video_url);
                m = c;
              }
            }
            const prepath = `E:\\0粉笔视频\\`;
            const zlfx = m.parentid ? m.parentid + `-` : '';
            const correctRatio = m.correctRatio ? Math.round(m.correctRatio) + `-` : '';
            const filename = `${item.sort}-${osrot}-${m.id}-${correctRatio}${zlfx}${
              m.id
            }${m.allcateid ? '-' + m.allcateid.replace(/,/g, '-') : ``}.mp4`;
            console.log(filename, video_url);
            const res = await axiosretry(video_url);
            const path = 'gzzlfx-2';
            if (path) {
              if (!fs.existsSync(`${prepath}${path}`)) {
                fs.mkdirSync(`${prepath}${path}`);
              }
              if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
                continue;
              }
              res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
            } else {
              if (!fs.existsSync(`${prepath}`)) {
                fs.mkdirSync(`${prepath}`);
              }
              if (fs.existsSync(`${prepath}${filename}`)) {
                continue;
              }
              res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
            }
            i++;
          }
          j++;
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
      console.log('下载完成', i, j);
    }
    ctx.body = info5k;
  }

  async fixtimu() {
    const { ctx, app } = this;
    let { url } = ctx.query;
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    const response = await axios.get(url, { headers });

    for (let item of response.data.solutions) {
      let options = item.accessories[0].options;
      let answerone = options[0];
      let answertwo = options[1];
      let answerthree = options[2];
      let answerfour = options[3];
      // console.log(item.id, item.content);
      // console.log(answerone, answertwo, answerthree, answerfour);
      let resinfo = await ctx.service.xr['update']('fbgwyzlfx', {
        id: item.id,
        content: item.content,
        answerone,
        answertwo,
        answerthree,
        answerfour,
      });
      console.log(resinfo);
    }
    ctx.body = response.data;
  }

  async pinglun() {
    const { ctx, app } = this;
    // 获取查询参数
    const id = ctx.query.id || '5911268';
    const type = ctx.query.type || 'xingce';
    // console.log(type);
    // 设置请求信息
    const cookie = await app.redis.get('fbcookie');
    const headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    // 请求问题集信息
    const infourl = 'https://ke.fenbi.com/api/gwy/v3/episodes/question_episodes_with_multi_type';
    try {
      const infoResponse = await axios.get(infourl, {
        headers,
        params: { question_ids: id, tiku_prefix: type },
      });
      const plid = infoResponse.data?.data?.[id]?.[0]?.id;
      if (plid) {
        // 请求评论信息
        const commentUrl = `https://ke.fenbi.com/api/gwy/v3/comments/episodes/${plid}`;
        const commentResponse = await axios.get(commentUrl, {
          headers,
          params: { len: 300, start: 0 },
        });
        // 返回评论数据
        ctx.body = commentResponse.data;
      } else {
        ctx.status = 200;
        ctx.body = { success: false, message: '未找到对应题目或评论', code: 200 };
      }
    } catch (error) {
      ctx.status = 200;
      ctx.body = { success: false, message: '请求失败', code: 200, error: error.message };
    }
  }

  async getquestion() {
    const { ctx, app } = this;
    let { q, type } = ctx.query;
    type = +type === 48644 ? 'xingce' : 'syzc';
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0',
    };

    const params = {
      q: q,
      coursePrefix: type,
      offset: 0,
      length: 15,
      userId: 127904606,
      format: 'html',
      sourceType: 1,
      app: 'web',
      kav: 100,
      av: 100,
      hav: 100,
      version: '3.0.0.0',
    };
    const response = await axios.get(`https://algo.fenbi.com/api/fenbi-question-search/question`, {
      headers,
      params,
    });
    // ctx.body = response.data;
    // return;

    let data = response.data.data.items[0];
    console.log(data);
    const res = await axios.get(`https://tiku.fenbi.com/api/${type}/universal/auth/solutions`, {
      headers,
      params: {
        type: 8,
        id: data.materialId,
        checkId: data.encodeCheckInfo,
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
    });
    res.data.dataid = data.materialId;
    res.data.checkId = data.encodeCheckInfo;
    ctx.body = res.data;
  }

  async getchaxunquestion() {
    const { ctx, app } = this;
    let { q, type } = ctx.query;
    type = +type === 48644 ? 'xingce' : 'syzc';
    let biao = +type === 48644 ? 'fbgwy' : 'fbsy';
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0',
    };

    const params = {
      q: q,
      coursePrefix: type,
      offset: 0,
      length: 15,
      userId: 127904606,
      format: 'html',
      sourceType: 1,
      app: 'web',
      kav: 100,
      av: 100,
      hav: 100,
      version: '3.0.0.0',
    };
    const response = await axios.get(`https://algo.fenbi.com/api/fenbi-question-search/question`, {
      headers,
      params,
    });
    // console.log(response.data);
    let data = response?.data?.data?.items[0];
    // console.log(data.encodeCheckInfo, type);
    // ctx.body = response.data;
    // return;
    const res = await axios.get(`https://tiku.fenbi.com/api/${type}/universal/auth/solutions`, {
      headers,
      params: {
        type: 6,
        questionIds: data.questionId,
        checkId: data.encodeCheckInfo,
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
    });
    res.data.checkId = data.encodeCheckInfo;
    let x = {};
    let y = res.data.solutions;

    for (let item of y) {
      // console.log(item);
      const correct = await axios.get(
        `https://tiku.fenbi.com/api/${type}/question/meta?ids=${item.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      let correctRatio = correct.data[0].correctRatio;
      let mostWrongAnswer = correct.data[0].mostWrongAnswer.choice;
      const ds = await ctx.service.xr['find'](biao, { id: item.id });
      x.ds = ds?.data?.ds || '';

      x.id = item.id;
      x.content = item.content;
      x.createdTime = dayformat(item.createdTime);
      x.source = item.source;
      let options = item.accessories[0].options;
      x.answerone = `A.` + options[0];
      x.answertwo = `B.` + options[1];
      x.answerthree = `C.` + options[2];
      x.answerfour = `D.` + options[3];
      x.answer =
        +item.correctAnswer.choice === 0
          ? `A`
          : +item.correctAnswer.choice === 1
            ? `B`
            : +item.correctAnswer.choice === 2
              ? `C`
              : `D`;
      x.solution = item.solution;
      x.correctRatio = Math.round(correctRatio);
      x.mostWrongAnswer = mostWrongAnswer;
    }
    ctx.body = [x];
  }

  async getchaxunzlfxquestion() {
    const { ctx, app } = this;
    let { q, type } = ctx.query;
    type = +type === 48644 ? 'xingce' : 'syzc';
    let biao = +type === 48644 ? 'fbgwy' : 'fbsy';
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0',
      Accept: 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      'Cache-Control': 'no-cache',
      Pragma: 'no-cache',
      'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'Sec-CH-UA-Mobile': '?0',
      'Sec-CH-UA-Platform': '"Windows"',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-site',
      Referer: 'https://www.fenbi.com/',
    };

    const params = {
      q: q,
      coursePrefix: type,
      offset: 0,
      length: 15,
      userId: 127904606,
      format: 'html',
      sourceType: 1,
      app: 'web',
      kav: 100,
      av: 100,
      hav: 100,
      version: '3.0.0.0',
    };
    const response = await axios.get(`https://algo.fenbi.com/api/fenbi-question-search/question`, {
      headers,
      params,
    });
    // console.log(response.data);
    let data = response?.data?.data?.items[0];
    // console.log(data);
    // ctx.body = response.data;
    // return;
    const res = await axios.get(`https://tiku.fenbi.com/api/${type}/universal/auth/solutions`, {
      headers,
      params: {
        type: 8,
        id: data.materialId,
        checkId: data.encodeCheckInfo,
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
    });
    res.data.checkId = data.encodeCheckInfo;
    let y = res.data.solutions;
    const ids = y.map((item) => item.id).join(',');
    const star = await axios.get(
      `https://tiku.fenbi.com/api/${type}/question/meta?ids=${ids}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    res.data.solutions = y.map((item) => {
      const s = star.data.find((s) => s.id === item.id);
      if (s) {
        return {
          ...item,
          correctRatio: Math.round((s.correctRatio + Number.EPSILON) * 100) / 100,
          mostWrongAnswer: s.mostWrongAnswer.choice,
        };
      }
      return item;
    });

    ctx.body = [res.data];
  }

  async update5000zlfx() {
    const { ctx, app } = this;
    const fbm = 'gzzlfxm';
    const fbzlfx = 'gzzlfx';
    let cookie = await app.redis.get('fbcookie');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let { dataid, checkId, type } = ctx.query;
    type = +type === 48644 ? 'xingce' : 'syzc';
    const res = await axios.get(`https://tiku.fenbi.com/api/${type}/universal/auth/solutions`, {
      headers,
      params: {
        type: 8,
        id: dataid,
        checkId: checkId,
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
    });
    let materials = res.data.materials[0];
    let solutions = res.data.solutions;
    let sortinfo = await ctx.service.xr['query'](
      `SELECT max(sort) as sort
       from ${fbm}`,
    );
    let sort;

    console.log(+sortinfo[0].sort, +sortinfo[0].sort + 1);
    sort = sortinfo[0].sort ? +sortinfo[0].sort + 1 : 1;

    let message = await ctx.service.xr['create'](fbm, {
      content: materials.content,
      oid: materials.id,
      sort: sort,
    });
    let ids = [];
    for (let item of solutions) {
      let x = {};
      ids.push(item.id);
      x.id = item.id;
      x.content = item.content;
      x.answerone = item.accessories[0].options[0];
      x.answertwo = item.accessories[0].options[1];
      x.answerthree = item.accessories[0].options[2];
      x.answerfour = item.accessories[0].options[3];
      x.answer = item.correctAnswer.choice;
      x.source = item.source;
      x.solution = item.solution;
      x.createdTime = dateformat(item.createdTime);
      x.parentid = materials.id;
      let res = await ctx.service.xr['find'](fbzlfx, { id: x.id });
      if (!res.data) {
        await ctx.service.xr['create'](fbzlfx, x);
      }
    }

    let id = ids.join(',');
    const response = await axios.get(
      `https://tiku.fenbi.com/api/${type}/question/meta?ids=${id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    for (let item of response.data) {
      if (+item.correctRatio !== 0) {
        let x = {
          id: item.id,
          correctRatio: item.correctRatio,
          mostWrongAnswer: item.mostWrongAnswer.choice,
        };
        await ctx.service.xr['update'](fbzlfx, x);
      }
    }

    ctx.body = { message: message };
  }

  async jszgms() {
    const { ctx, app } = this;
    const cookie = await app.redis.get('fbcookie1');
    const biao = 'fbjsms';
    const headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let { labelid } = ctx.query;
    console.log(labelid);
    const pageinfo = await axios.get(`https://tiku.fenbi.com/api/jszgzmsgzjsj/papers/`, {
      headers,
      params: {
        toPage: 0,
        pageSize: 50,
        labelId: labelid ? labelid : '',
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
    });
    for (let item of pageinfo.data.list) {
      let id = item.id;
      let encodeCheckInfo = item.encodeCheckInfo;
      let name = item.name;
      let metainfo = await axios.get(
        `https://tiku.fenbi.com/api/jszgzmsgzjsj/universal/auth/solutions?type=7&id=${id}&checkId=${encodeCheckInfo}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers },
      );
      metainfo = metainfo.data.solutions;
      let solutionAccessories = metainfo[0].solutionAccessories;
      let { kcnl, swdt, sfdt } = ``;
      for (let key of solutionAccessories) {
        if (key.label === 'swdt') {
          swdt = key.content;
        }
        if (key.label === 'sfdt') {
          sfdt = key.content;
        }
        if (key.label === 'kcnl') {
          kcnl = key.content;
        }
      }
      if (metainfo[3]) {
        console.log(metainfo[3].content);
      }
      let res = await ctx.service.xr['find'](biao, {
        oid: id,
      });
      if (!res.data) {
        await ctx.service.xr['create'](biao, {
          oid: id,
          name: name,
          timuid: metainfo[0].id,
          timu: metainfo[0].content,
          kcnl: kcnl,
          swdt: swdt,
          sfdt: sfdt,
          db1id: metainfo[1].id,
          db1: metainfo[1].content,
          db1a: metainfo[1].correctAnswer.answer,
          db2id: metainfo[2].id,
          db2: metainfo[2].content,
          db2a: metainfo[2].correctAnswer.answer,
          source: name,
          createdTime: dateformat(metainfo[0].createdTime),
        });
      }

      // console.log(metainfo.data.solutions);
    }
    let total = await ctx.service.xr['query'](
      `select count(oid) as total
       from ${biao}`,
    );
    ctx.body = { total: total[0].total };
  }

  async downloadjsms() {
    const { ctx, app } = this;
    let page = 6;
    let i = 0;
    let j = 0;
    let path = 'jsms';
    let cookie = await app.redis.get('fbcookie1');
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    // let per = 50;
    // const offset = (page - 1) * per;
    let sqlquery = `SELECT *
                    FROM fbjsms
                    order by id desc limit 48
    `;
    let data = await ctx.service.xr['query'](sqlquery);
    let temp_ids = [];
    for (let item of data) {
      temp_ids.push(item.timuid);
    }
    let ids = temp_ids.join(',');
    // console.log(data);
    console.log(ids);
    const response = await axios.get(
      `https://ke.fenbi.com/api/gwy/v3/episodes/tiku_episodes_with_multi_type?tiku_ids=${ids}&tiku_prefix=jszgzmsgzjsj&tiku_type=5&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );
    let datalist = response.data.data;
    // console.log('datalist', datalist);
    let video_url = '';
    let highestResolutionUrl = '';
    let maxResolution = 0;
    let n = 0;
    for (let [key, value] of Object.entries(datalist)) {
      // console.log('1', value[0]);
      n++;
      if (+n === 4) {
        console.log(n + `.`, value);
      }
      for (let item in value) {
        console.log(n + `.`, value[item].id, value[item].title, video_url);
        const res = await axios.get(
          `https://ke.fenbi.com/api/gwy/v3/episodes/${value[item].id}/mediafile/meta?content_id=0&biz_type=100&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers },
        );
        // console.log(res.data.datas);
        for (let item of res.data.datas) {
          if (item) {
            const resolution = item.height * item.width;
            if (resolution > maxResolution) {
              maxResolution = resolution;
              highestResolutionUrl = item.url;
            }
          }
          video_url = item.url;
        }

        // return;

        if (video_url) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          const res = await axios.get(video_url, {
            responseType: 'stream',
          });
          let m = {};
          for (let c of data) {
            if (+c.id === +key) {
              console.log(n + `.`, +c.id, video_url);
              m = c;
            }
          }
          const prepath = `E:\\0粉笔视频\\`;
          let filename = `${n}-${value[item].title}.mp4`;
          filename = filename.replace(/\//g, ' ');

          if (path) {
            if (!fs.existsSync(`${prepath}${path}`)) {
              fs.mkdirSync(`${prepath}${path}`);
            }
            if (fs.existsSync(`${prepath}${path}\\${filename}`)) {
              continue;
            }
            res.data.pipe(fs.createWriteStream(`${prepath}${path}\\${filename}`));
          } else {
            if (!fs.existsSync(`${prepath}`)) {
              fs.mkdirSync(`${prepath}`);
            }
            if (fs.existsSync(`${prepath}${filename}`)) {
              continue;
            }
            res.data.pipe(fs.createWriteStream(`${prepath}${filename}`));
          }
          i++;
        } else {
          console.log('!video_url', value[item].id, value[item].title);
        }

        j++;
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
    console.log('下载完成', i, j);
    return { page: page, total: i };
  }

  async fbjsms() {
    const { ctx, app } = this;
    let type = ctx.query.type || '201,203';
    let q = ctx.query.q || '1=1';
    let per = ctx.query.per || '2';
    let page = ctx.query.page || '1';
    const offset = (page - 1) * per;
    let biao = 'fbjsms';
    let likesql =
      q === '1=1'
        ? '1=1'
        : `(content like '%${q}%' or A like '%${q}%' or B like '%${q}%' or C like '%${q}%' or D like '%${q}%' )`;
    let sql = `SELECT *
               FROM ${biao}
               order by createdTime desc limit ${per}
               offset ${offset}`;
    let data = await ctx.service.xr['query'](sql);
    for (let item of data) {
      item.timu = item.timu.replace(/fenbike/g, 'fbstatic');
    }
    ctx.body = data;
  }

  async uppic() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let coursePrefix = ctx.request.body.coursePrefix || 48644;
    let type = +coursePrefix === 48644 ? 'xingce' : 'syzc';
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    // 定义图片的本地路径
    const picPath = ctx.request.files[0].filepath;
    // console.log('uppic');
    // console.log(picPath);
    // 确保文件存在
    if (!fs.existsSync(picPath)) {
      ctx.body = { success: false, message: '图片文件不存在' };
      return;
    }

    // 生成 UUID 文件名
    const uuid = '17442204-6a38-4d0e-879a-13a14757e4db';
    const filename = `${uuid}.png`;

    try {
      // 创建 FormData 对象
      const form = new FormData();
      form.append('file', fs.createReadStream(picPath), {
        filename, // 使用 UUID 作为文件名
        contentType: 'image/png',
      });

      // 添加其他字段
      form.append('coursePrefix', type);
      form.append('format', 'ubb');

      // 发送 POST 请求
      const response = await axios.post(
        'https://algo.fenbi.com/android/picSearch/search?version=6.17.19&vendor=fenbi&app=gwy&av=107&kav=102&hav=107&apcid=2&deviceId=QTBl//wKmYT2diNURAXftw%3D%3D&client_context_id=E43FCF7F239F52CA4FC6',
        form,
        {
          headers: {
            ...form.getHeaders(), // 将 form-data 的头部信息合并到请求头
            'User-Agent': 'fenbi-android',
            Connection: 'Keep-Alive',
            'Accept-Encoding': 'gzip',
            traceId: '30170d48-**************-f3577271b03e',
            Cookie: cookie,
          },
          maxBodyLength: Infinity, // 避免上传大文件时报错
        },
      );

      // 返回上传结果
      // ctx.body = { success: true, data: response.data };
      let data;
      for (let item in response.data.data.items) {
        if (!response.data.data.items[item].source.match('模考')) {
          data = response.data.data.items[item];
          break;
        }
      }

      let coursePrefix = data.coursePrefix;
      // console.log(data, coursePrefix);
      // ctx.body = response.data;
      // return;
      const res = await axios.get(
        `https://tiku.fenbi.com/api/${coursePrefix}/universal/auth/solutions`,
        {
          headers,
          params: {
            type: 6,
            questionIds: data.questionId,
            checkId: data.encodeCheckInfo,
            app: 'web',
            kav: 100,
            av: 100,
            hav: 100,
            version: '3.0.0.0',
          },
        },
      );
      res.data.checkId = data.encodeCheckInfo;
      let x = {};
      let y = res.data.solutions;

      for (let item of y) {
        // console.log(item);
        const correct = await axios.get(
          `https://tiku.fenbi.com/api/${coursePrefix}/question/meta?ids=${item.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers },
        );
        let correctRatio = correct.data[0].correctRatio;
        x.correctRatio = Math.round(correctRatio);

        x.id = item.id;
        x.content = item.content;
        x.createdTime = dayformat(item.createdTime);
        x.source = item.source;
        let options = item.accessories[0].options;
        x.answerone = `A.` + options[0];
        x.answertwo = `B.` + options[1];
        x.answerthree = `C.` + options[2];
        x.answerfour = `D.` + options[3];
        x.answer =
          +item.correctAnswer.choice === 0
            ? `A`
            : +item.correctAnswer.choice === 1
              ? `B`
              : +item.correctAnswer.choice === 2
                ? `C`
                : `D`;
        x.solution = item.solution;
      }
      ctx.body = [x];
    } catch (error) {
      // console.log(error);
    }
  }

  async jzkj() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let type = ctx.query.type || 'jsjyzhzz';
    let kjid = ctx.query.kjid || 1719582862;
    let biao = `fbjzkj`;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    const res = await axios.get(`https://tiku.fenbi.com/api/${type}/universal/auth/solutions`, {
      params: {
        type: 0,
        id: kjid,
        app: 'web',
        kav: 100,
        av: 100,
        hav: 100,
        version: '3.0.0.0',
      },
      headers: headers,
    });
    for (let item of res.data.solutions) {
      // console.log(`第${i}题：`, item.correctAnswer.type, item.type);
      // i++;
      await ctx.service.xr['create'](biao, {
        data: JSON.stringify(item),
      });
    }
    ctx.body = res.data;
  }

  async getjzkaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie');
    let type = ctx.query.type || 'jsjyzhzz';
    let kjid = ctx.query.kjid || 1719582862;
    let biao = `fbjzkj`;
    let kaojuanid = ctx.query.id;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/jsjyzhzz/papers/?toPage=0&pageSize=15&labelId=2782&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      const kaojuanlist = kaojuanpage.data.list;
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        let paperName = item.name;
        console.log(`获取第${j}份考卷`, paperName);

        const exercise = await axios.post(
          `https://tiku.fenbi.com/api/${type}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
              'content-type': 'application/x-www-form-urlencoded',
              priority: 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://www.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        let exerciseinfo = exercise.data;
        console.log(exerciseinfo.id);
        //等待3秒用promise
        console.log(`等待3秒提交考卷`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        await axios.post(
          `https://tiku.fenbi.com/api/${type}/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          'status=1',
          { headers },
        );
        console.log(`等待3秒获取解析`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        const response = await axios.get(
          `https://tiku.fenbi.com/api/${type}/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          { headers: headers },
        );
        const data = response.data;
        let i = 0;
        let sortinfo = await ctx.service.xr['query'](
          `select min(sort) as sort
           from ` + biao,
        );

        let sortby = sortinfo[0].sort + 1;

        //单项选择题	201	1
        // 多项选择题	201	2
        // 判断题	201	5
        // 填空题	202	61
        // 辨析题	203	12
        // 材料分析题	203	15
        for (let item of data.solutions) {
          let obj = {};
          let material = '';
          if (+item.correctAnswer.type === 203 && +item.type === 15) {
            let n = item.materialIndexes[0];
            material = data.materials[n].content;
          }
          obj.content = material + item.content;
          if (!item.accessories[0] === false && +item.correctAnswer.type === 201) {
            let options = item.accessories[0].options;
            obj.A = options[0];
            obj.B = options[1];
            obj.C = options[2];
            obj.D = options[3];
          }
          obj.oid = item.id;
          obj.type = item.correctAnswer.type;
          obj.type2 = item.type;
          obj.solution =
            +item.correctAnswer.type === 201 || +item.correctAnswer.type === 202
              ? item.solution
              : item.correctAnswer.answer;
          if (+item.correctAnswer.type === 202) {
            obj.choice =
              item.correctAnswer.blanks.length === 1
                ? item.correctAnswer.blanks
                : item.correctAnswer.blanks.join('，');
          } else {
            obj.choice = item.correctAnswer.choice;
          }

          obj.sort = j;
          obj.source = item.source;
          obj.name = paperName;
          obj.createdTime = dateformat(item.createdTime);
          i++;
          await ctx.service.xr['create'](biao, obj);

          // let res = await ctx.service.xr['find'](biao, {
          //   oid: item.id,
          // });
          // if (!res.data) {
          //   if (j === 12) {
          //     console.log(obj, res);
          //   }
          // } else {
          // }
        }
        j++;
        await new Promise((resolve) => setTimeout(resolve, 4000));
      }
      console.log(`获取结束`);
      ctx.body = 1;
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getsykaojuan() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let type = ctx.query.type || 'syzc';
    let biao = `fbsykj`;
    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };

    try {
      const kaojuanpage = await axios.get(
        `https://tiku.fenbi.com/api/${type}/papers/?toPage=0&pageSize=100&labelId=3715&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        { headers: headers },
      );
      console.log(`获取考卷页面id`);
      const kaojuanlist = kaojuanpage.data.list;
      let j = 1;
      for (let item of kaojuanlist) {
        let paperMeta = item.id;
        let paperName = item.name;
        console.log(`获取第${j}份考卷`, paperName);
        if (paperName.match('学院')) {
          continue;
        }

        const exercise = await axios.post(
          `https://tiku.fenbi.com/api/${type}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
          `type=1&paperId=${paperMeta}&exerciseTimeMode=2`,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
              'content-type': 'application/x-www-form-urlencoded',
              priority: 'u=1, i',
              'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://www.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        let exerciseinfo = exercise.data;
        let exerciseinfokey = exerciseinfo.key;
        const getExercise = await axios.get(
          `https://tiku.fenbi.com/combine/exercise/getExercise?format=html&key=${exerciseinfokey}&routecs=${type}&kav=120&av=120&hav=120&app=web`,
          { headers: headers },
        );
        let requestKey = getExercise.data.data.switchVO.requestKey;

        //等待3秒用promise
        console.log(`等待3秒提交考卷`);
        await new Promise((resolve) => setTimeout(resolve, 3000));
        let url = `https://tiku.fenbi.com/combine/exercise/submit?key=${exerciseinfokey}&routecs=${type}&kav=120&av=120&hav=120&app=web`;
        let url1 = `https://tiku.fenbi.com/api/${type}/async/exercises/${exerciseinfo.id}/submit?app=web&kav=100&av=100&hav=100&version=3.0.0.0`;
        await axios.post(
          url,
          {},
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
              'content-type': 'application/json',
              priority: 'u=1, i',
              'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-site',
              cookie: cookie,
              Referer: 'https://spa.fenbi.com/',
              'Referrer-Policy': 'strict-origin-when-cross-origin',
            },
          },
        );
        console.log(`等待3秒获取解析`);
        await new Promise((resolve) => setTimeout(resolve, 3000));

        let surl = `https://tiku.fenbi.com/combine/static/solution?key=${requestKey}&routecs=${type}&type=1&kav=120&av=120&hav=120&app=web`;
        let surl1 = `https://tiku.fenbi.com/api/${type}/universal/auth/solutions?type=0&id=${exerciseinfo.id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`;
        const response = await axios.get(surl, { headers: headers });
        const data = response.data;
        let i = 0;
        let sortinfo = await ctx.service.xr['query'](
          `select min(sort) as sort
           from ` + biao,
        );

        let sortby = sortinfo[0].sort + 1;

        let datacard = data.card.children;
        let datacard0 = +datacard[0].questionCount - 1;
        let datacard1 = +datacard[1].questionCount;
        let datacard2 = +datacard[2].questionCount;
        let datacard3 = +datacard[3].questionCount;
        let datacard4 = +datacard[4].questionCount;
        let zlcard = datacard0 + datacard1 + datacard2 + datacard3;
        let pdtotal = 0;
        let sltotal = 0;
        for (let item of datacard) {
          if (item.name.match('推理')) {
            pdtotal = item.questionCount;
          }
          if (item.name.match('数量')) {
            sltotal = item.questionCount;
          }
        }
        console.log(pdtotal);
        console.log(sltotal);
        let cardname = '';
        let pdindex = 1;
        let slindex = 1;

        for (let item of data.solutions) {
          if (i <= datacard0) {
            cardname = data.card.children[0].name;
          } else if (i <= datacard0 + datacard1) {
            cardname = data.card.children[1].name;
          } else if (i <= datacard0 + datacard1 + datacard2) {
            cardname = data.card.children[2].name;
          } else if (i <= datacard0 + datacard1 + datacard2 + datacard3) {
            cardname = data.card.children[3].name;
          } else {
            cardname = data.card.children[4].name;
          }
          let obj = {};
          let material = '';
          if (i === zlcard + 1 || i === zlcard + 6) {
            material = data.materials[i === zlcard + 1 ? 0 : 1].content;
          }
          obj.content = material + item.content;
          if (!item.accessories[0] === false) {
            let options = item.accessories[0].options;
            obj.A = options[0];
            obj.B = options[1];
            obj.C = options[2];
            obj.D = options[3];
          }
          obj.oid = item.id;
          if (cardname === '公共基础知识' || cardname === '常识判断') {
            obj.type = 201;
          } else if (cardname === '言语理解与表达') {
            obj.type = 202;
          } else if (cardname === '判断推理') {
            if (pdindex <= 5) {
              obj.type = 206;
            } else if (pdindex > pdtotal - 10) {
              obj.type = 207;
            } else {
              obj.type = 203;
            }
            pdindex++;
          } else if (cardname === '数量关系') {
            if (slindex <= 5 && +sltotal === 15) {
              obj.type = 208;
            } else {
              obj.type = 204;
            }
            slindex++;
          } else if (cardname === '资料分析') {
            obj.type = 205;
          }
          // obj.type =
          //   i <= datacard0
          //     ? 201
          //     : i <= datacard0 + datacard1
          //       ? 202
          //       : i <= datacard0 + datacard1 + datacard2
          //         ? obj.A === 'A'
          //           ? 206
          //           : 203
          //         : i <= datacard0 + datacard1 + datacard2 + datacard3
          //           ? 204
          //           : 205;
          obj.solution = item.solution;
          obj.choice = item.correctAnswer.choice;

          obj.sort = j;
          obj.source = item.source;
          obj.name = paperName;
          obj.createdTime = dateformat(item.createdTime);
          i++;
          await ctx.service.xr['create'](biao, obj);
        }
        if (i !== 110) {
          console.log(`第${j} ${paperName}份不到110题`);
        }
        console.log(`第${j}份写到数据库完成`);
        j++;
        console.log(`等待3秒获取下一套`);
        let total = await ctx.service.xr['query'](
          `select count(id) as total
           from ` + biao,
        );
        total = total[0].total;
        console.log(`一共${total}题，${total / 110}套`);
        await new Promise((resolve) => setTimeout(resolve, 4000));
      }
      console.log(`获取结束`);
      ctx.body = 1;
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async getzlfx() {
    const { ctx, app } = this;
    let cookie = await app.redis.get('fbcookie1');
    let type = ctx.query.type || 'syzc';
    let biao = ctx.query.biao || `fbsy`;
    let limit = ctx.query.limit || 1;
    let xid = ctx.query.id || 656604;
    let headers = {
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
        'content-type': 'application/json',
        priority: 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        cookie: cookie,
        Referer: 'https://spa.fenbi.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
      },
    };

    try {
      console.log(`获取题目`);

      const exercise = await axios.post(
        `https://tiku.fenbi.com/api/${type}/exercises?app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        `type=3&keypointId=${xid}&limit=${limit}&exerciseTimeMode=2&yearScope=0&correctRatioLow=0&correctRatioHigh=1`,
        {
          headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded',
            pragma: 'no-cache',
            priority: 'u=1, i',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            cookie: cookie,
            Referer: 'https://www.fenbi.com/',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
          },
        },
      );

      let exerciseinfo = exercise.data;
      let exerciseinfokey = exerciseinfo.key;
      const getExercise = await axios.get(
        `https://tiku.fenbi.com/combine/exercise/getExercise?format=html&key=${exerciseinfokey}&routecs=${type}&kav=120&av=120&hav=120&app=web`,
        headers,
      );
      let requestKey = getExercise.data.data.switchVO.requestKey;
      let eurl = `https://tiku.fenbi.com/combine/static/exercise?key=${requestKey}&routecs=${type}&type=1&kav=120&av=120&hav=120&app=web`;
      let eres = await axios.get(eurl, headers);
      let elist = [];
      let ids = [];
      for (let item of eres.data.questions) {
        ids.push(item.id);
        elist.push({
          key: item.globalId,
          time: Math.floor(Math.random() * 60) + 1,
          answer: {
            choice: `0`,
            type: 201,
          },
        });
      }

      let tijiao = await axios.post(
        `https://tiku.fenbi.com/combine/exercise/incrUpdate?key=${exerciseinfokey}&routecs=${type}&kav=120&av=120&hav=120&app=web`,
        elist,
        headers,
      );
      //等待3秒用promise
      console.log(`等待3秒提交考卷`);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      let url = `https://tiku.fenbi.com/combine/exercise/submit?key=${exerciseinfokey}&routecs=${type}&kav=120&av=120&hav=120&app=web`;
      await axios.post(url, {}, headers);
      console.log(`等待3秒获取解析`);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      let surl = `https://tiku.fenbi.com/combine/static/solution?key=${requestKey}&routecs=${type}&type=1&kav=120&av=120&hav=120&app=web`;
      const response = await axios.get(surl, headers);
      const data = response.data;
      let i = 0;
      ids = ids.join(',');
      console.log(ids);
      let resc = await axios.get(
        `https://tiku.fenbi.com/api/${type}/solution/keypoints?ids=${ids}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
        headers,
      );
      for (let item of data.solutions) {
        const obj = {};
        obj.id = item.id;

        // console.log('Current item globalId:', item.globalId);

        // 从 card.children 中找到与当前 solution 对应的卡片
        let matchedCard = null;
        for (let child of data.card.children) {
          for (let subChild of child.children) {
            if (subChild.key === item.globalId) {
              matchedCard = subChild;
              break;
            }
          }
          if (matchedCard) break;
        }

        if (!matchedCard) {
          // console.log('No matching card found for globalId:', item.globalId);
        }

        // 从 materials 中找到该卡片所关联的材料
        const materialKey = matchedCard?.materialKeys?.[0];
        const matchedMaterial = materialKey
          ? data.materials?.find((m) => m.globalId === materialKey)
          : null;

        // console.log('Matched material:', matchedMaterial);

        if (!matchedMaterial) {
          // console.log(
          //   'No matching material found for globalId:',
          //   item.globalId,
          // );
        }

        obj.material = matchedMaterial?.content.replace(/fenbike/g, 'fbstatic') || '';
        obj.materialKeys = matchedMaterial?.globalId || '';
        obj.parentid = matchedMaterial?.id || null; // 材料 id 存入 parentid

        obj.content = item.content.replace(/fenbike/g, 'fbstatic') || '';

        if (item.accessories?.[0]?.options) {
          const options = item.accessories[0].options;
          obj.answerone = options[0] || '';
          obj.answertwo = options[1] || '';
          obj.answerthree = options[2] || '';
          obj.answerfour = options[3] || '';
        }

        obj.answer =
          +item.correctAnswer.choice === 0
            ? 'A'
            : +item.correctAnswer.choice === 1
              ? 'B'
              : +item.correctAnswer.choice === 2
                ? 'C'
                : 'D';
        obj.solution = item.solution.replace(/fenbike/g, 'fbstatic') || '';
        obj.source = item.source || '';
        obj.createdTime = dateformat(item.createdTime || Date.now());

        // obj.ds = null;
        obj.ds = '重新更新';
        obj.allcateid = '';
        obj.tag = null;
        obj.correctRatio = null;
        obj.mostWrongAnswer = '';
        let textArray = [];
        textArray.push(xid);

        for (let itemx in resc.data[i]) {
          let all = await this.getAllCategoryInfo('fbsycate', resc.data[i][itemx].id);
          const result = Object.values(all).join(',');
          console.log('result', result);
          textArray.push(result);
        }
        const text = textArray.join(',');
        const newTextArray = text.split(',');
        const uniqueTextArray = [...new Set(newTextArray)];
        const newTextArrayx = uniqueTextArray.join(',');
        obj.allcateid = newTextArrayx;

        i++;
        let res = await ctx.service.xr['find'](biao, { id: item.id });

        if (!res.data) {
          await ctx.service.xr['create'](biao, obj);
        } else {
          await ctx.service.xr['update'](biao, obj);
        }
      }

      let total = await ctx.service.xr['query'](
        `select count(id) as total
         from ${biao}
         where allcateid like '%${xid}%'`,
      );
      total = total[0].total;
      console.log(`一共${total}题`);
      console.log(`获取结束`);
      ctx.body = { message: `一共${total}题` };
    } catch (error) {
      console.error(error);
      ctx.body = error;
    }
  }

  async upfbsykjallceteid() {
    const { ctx, app, logger } = this;
    let cookie = await app.redis.get('fbcookie1');
    let type = ctx.query.type || 'syzc';
    let biao = ctx.query.biao || 'fbsykj';

    let headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    };
    let sql = `select oid, content
               from ${biao}
               where allcateid = 1
               order by oid desc limit 100 `;
    let data = await ctx.service.xr['query'](sql);
    let shengyu = await ctx.service.xr['query'](
      `select count(oid) as count
       from ${biao}
       where allcateid = 1
       order by oid desc limit 100`,
    );
    console.log(+shengyu[0].count);
    if (+shengyu[0].count === 0) {
      ctx.body = { ok: 'ok' };
      return;
    }
    let ids = [];
    data.map((item) => {
      ids.push(item.oid);
    });

    let id = ids.join(',');
    logger.info('id', id);

    const resc = await axios.get(
      `https://tiku.fenbi.com/api/${type}/solution/keypoints?ids=${id}&app=web&kav=100&av=100&hav=100&version=3.0.0.0`,
      { headers },
    );

    for (let i = 0; i < 100; i++) {
      logger.warn(`新一题`, i);
      logger.warn(`新一题`, resc.data[i]);

      if (resc.data[i].length === 0) {
        continue;
      }
      let textArray = [];
      for (let itemx in resc.data[i]) {
        logger.info(`当前id：`, resc.data[i][itemx].id);
        let all = await this.getAllCategoryInfo('fbsycate', resc.data[i][itemx].id);
        const result = Object.values(all).join(',');
        console.log('all', all);
        console.log('result', result);
        textArray.push(result);
      }
      const text = textArray.join(',');
      const newTextArray = text.split(',');
      const uniqueTextArray = [...new Set(newTextArray)];
      let allcateid = uniqueTextArray.join(',');
      logger.info(`uniqueTextArray`, uniqueTextArray);
      logger.info('这次id：', ids[0]);
      // 先把字符串 id 组成统一结构
      const uniqueTextArray1 = uniqueTextArray.map((id) => ({ id }));

      // Promise.all 要传一个数组作为参数
      const rankstr = await Promise.all(
        uniqueTextArray1.map(async (item) => {
          const sqlcate = `SELECT *
                           FROM fbsycate
                           WHERE id = ${item.id}`;
          const rescate = await ctx.service.xr['query'](sqlcate);

          if (!rescate || rescate.length === 0) {
            logger.warn(`No result for id=${item.id}`);
            // 返回一个标记字符串而不是 undefined，避免 Promise.all 报错
            return `?(${item.id})`;
          }

          const name = rescate[0].name;
          return `${name}(${item.id})`;
        }),
      ).then((arr) => arr.join('-'));

      logger.info('rankstr=', rankstr);

      let upsql = `update ${biao}
                   set allcateid = '${allcateid}',
                       cate      = '${rankstr}'
                   where oid = ${+ids[i]}`;

      let d = await ctx.service.xr['query'](upsql);
      logger.warn('更新：', upsql);
    }

    let x = await ctx.service.xr['query'](`select *
                                           from ${biao}
                                           where oid in (${id})`);
    ctx.body = { data: x };
  }

  async canvas() {
    const { ctx, app, logger } = this;
    let biao = 'fbsy';

    try {
      const method = ctx.request.method;

      if (method === 'POST') {
        // 保存canvas数据
        const { id, canvas_data, canvas_width, canvas_height, has_canvas } = ctx.request.body;

        // 详细的参数验证
        if (!id) {
          logger.warn('Canvas保存失败: 缺少题目ID');
          ctx.status = 400;
          ctx.body = { error: '缺少题目ID' };
          return;
        }

        // 验证ID格式
        const numericId = parseInt(id);
        if (isNaN(numericId) || numericId <= 0) {
          logger.warn(`Canvas保存失败: 无效的题目ID: ${id}`);
          ctx.status = 400;
          ctx.body = { error: '无效的题目ID' };
          return;
        }

        // 检查数据大小，防止过大
        if (canvas_data && canvas_data.length > 16777215) {
          // 16MB限制
          logger.warn(`Canvas保存失败: 数据过大 (${canvas_data.length} bytes)`);
          ctx.status = 413;
          ctx.body = { error: 'Canvas数据过大，请减少绘制内容' };
          return;
        }

        // 构建更新数据
        const updateData = {};
        if (canvas_data !== undefined) updateData.canvas_data = canvas_data;
        if (canvas_width !== undefined) updateData.canvas_width = parseInt(canvas_width) || 0;
        if (canvas_height !== undefined) updateData.canvas_height = parseInt(canvas_height) || 0;
        if (has_canvas !== undefined) updateData.has_canvas = parseInt(has_canvas) || 0;
        updateData.canvas_updated_time = new Date(); // 设置画布更新时间

        // logger.info(
        //   `开始保存Canvas数据: ID=${numericId}, 数据大小=${canvas_data ? canvas_data.length : 0}`,
        // ); // 减少日志输出

        // 检查记录是否存在
        const existingRecord = await ctx.service.xr['find'](biao, {
          id: numericId,
        });

        if (existingRecord.data) {
          // 更新现有记录
          updateData.id = numericId;
          const updateResult = await ctx.service.xr['update'](biao, updateData);
          // logger.info(`更新题目 ${numericId} 的canvas数据成功`); // 减少日志输出
        } else {
          // 创建新记录（如果题目不存在）
          const newRecord = {
            id: numericId,
            ...updateData,
            content: '', // 默认空内容
            ds: '',
            choice: '',
            answerone: '',
            answertwo: '',
            answerthree: '',
            answerfour: '',
            answer: '',
            solution: '',
            source: '',
            createdTime: new Date().toISOString(),
            allcateid: '',
            tag: null,
            correctRatio: null,
            mostWrongAnswer: '',
            materialKeys: null,
            parentid: null,
            material: '',
            extra: '',
          };
          const createResult = await ctx.service.xr['create'](biao, newRecord);
          // logger.info(`创建题目 ${numericId} 的canvas数据成功`); // 减少日志输出
        }

        ctx.body = {
          success: true,
          message: 'Canvas数据保存成功',
          id: numericId,
        };
      } else if (method === 'GET') {
        // 获取canvas数据
        const { id } = ctx.query;

        if (!id) {
          ctx.status = 400;
          ctx.body = { error: '缺少题目ID' };
          return;
        }

        const record = await ctx.service.xr['find'](biao, { id: parseInt(id) });

        if (!record.data) {
          ctx.body = {
            has_canvas: 0,
            canvas_data: null,
            canvas_width: null,
            canvas_height: null,
          };
          return;
        }

        const data = record.data;
        ctx.body = {
          has_canvas: data.has_canvas || 0,
          canvas_data: data.canvas_data || null,
          canvas_width: data.canvas_width || null,
          canvas_height: data.canvas_height || null,
          canvas_updated_time: data.canvas_updated_time || null,
        };
      } else {
        ctx.status = 405;
        ctx.body = { error: '不支持的请求方法' };
      }
    } catch (error) {
      logger.error('Canvas操作失败:', {
        error: error.message,
        stack: error.stack,
        method: ctx.request.method,
        url: ctx.url,
        // body: ctx.request.body, // 移除body输出，防止画布数据泄露
        query: ctx.query,
      });

      ctx.status = 500;
      ctx.body = {
        success: false,
        error: '服务器内部错误',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}

module.exports = FbController;

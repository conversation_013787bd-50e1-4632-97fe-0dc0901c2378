[Unit]
Description=Egg.js Application
Documentation=https://eggjs.org/
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=forking
User=root
Group=root
WorkingDirectory=/path/to/your/egg/project
Environment=NODE_ENV=production
Environment=EGG_SERVER_ENV=prod

# 启动命令 (使用pnpm直接管理)
ExecStart=/usr/local/bin/pnpm start
ExecStop=/usr/local/bin/pnpm stop
ExecReload=/bin/kill -USR2 $MAINPID

# 进程管理
PIDFile=/var/run/egg-server.pid
TimeoutStartSec=60
TimeoutStopSec=30

# 自动重启配置
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 内存限制 (可选)
MemoryLimit=2G

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=eggjs

# 安全配置
NoNewPrivileges=true
PrivateTmp=true

# 环境变量
Environment=NODE_OPTIONS="--max-old-space-size=1536"

[Install]
WantedBy=multi-user.target

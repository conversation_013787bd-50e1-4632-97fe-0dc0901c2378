const axios = require('axios');
const { dateNow } = require('../extend/helper');
/**
 * @param {Egg.Application} app - egg application
 */
module.exports = {
  schedule: {
    interval: '20s', // 1 分钟间隔
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['local'],
    immediate: true,
    enabled: false,
  },
  async task(ctx) {
    // let res = await ctx.service.xr.query(`select *
    //                                       from sw
    //                                       where name = 'ailocal'`);
    // const sw = Number(res[0]?.sw || 0);
    // if (+sw !== 0) {
    //   const data = [
    //     'updatefbsyzc?per=1&type=656600&model=5',
    //     'updatefbsyzc?per=1&type=656600&model=3',
    //     'updatefbsyzc?per=1&type=656600&model=4',
    //     'updatefbsyzc?per=1&type=656600&model=7',
    //     'updatefbsyzc?per=1&type=656600&model=8',
    //     'updatefbsyzc?per=1&type=656600&model=9',
    //     // 'updatefbsyzc?per=1&type=656618&model=10',
    //     'updatefbsyzc?per=1&type=656600&model=2&up=1',
    //     // 'updatefbsyzc?per=1&type=796963&model=2',
    //     '',
    //   ];
    //   for (let i in data) {
    //     // await ctx.service.feishu['fs3'](data[i] + '开始get');
    //     // await ctx.logger.info(data[i] + '开始get');
    //     await axios
    //       .get('http://127.0.0.1:7001/' + data[i])
    //       .then((res) => {
    //         if (res) {
    //           // console.log(res.data);
    //         }
    //       })
    //       .catch((e) => {
    //         if (e) {
    //           // ctx.service.feishu.fs3(data[i] + '异常');
    //         }
    //       });
    //   }
    // }
    // console.log(sw);
    // await axios
    //   .get(
    //     'http://127.0.0.1:7001/getjstimu?id=656596&limit=1&mode=syzc&biao=fbsy&biaocate=fbsycate&biaofullcate=fbsyfullcate',
    //   )
    //   .then((res) => {
    //     // console.log(res.data);
    //     // console.log(dateNow());
    //   })
    //   .catch((e) => {
    //     if (e) {
    //     }
    //   });
    // await axios
    //   .get('http://127.0.0.1:7001/fbupdatecorrectRatio?biao=fbsy&type=syzc')
    //   .then((res) => {
    //     // console.log(res.data);
    //     // console.log(dateNow());
    //   })
    //   .catch((e) => {
    //     if (e) {
    //       console.log(e.message);
    //     }
    //   });
    // await axios
    //   .get('http://127.0.0.1:7001/upfbsykjallceteid?model=4')
    //   .then((res) => {
    //     // console.log(res.data);
    //     // console.log(dateNow());
    //   })
    //   .catch((e) => {
    //     if (e) {
    //       console.log(e.message);
    //     }
    //   });
  },
};

# 服务器配置示例

## 配置说明

现在 `gz.js` 定时任务改为从配置文件读取服务器信息，而不是通过公网IP判断。

## 配置结构

```javascript
config.server = {
  location: 'gz',        // 服务器位置标识
  enableSchedule: true,  // 是否启用定时任务
  features: {
    aiTask: true,        // 是否启用AI相关任务
    dataSync: true,      // 是否启用数据同步
    monitoring: true,    // 是否启用监控
  },
};
```

## 不同环境配置示例

### 1. 广州生产服务器 (config.prod.js)
```javascript
config.server = {
  location: 'gz',
  enableSchedule: true,
  features: {
    aiTask: true,
    dataSync: true,
    monitoring: true,
  },
};
```

### 2. 北京服务器配置
```javascript
config.server = {
  location: 'bj',
  enableSchedule: true,
  features: {
    aiTask: false,      // 北京服务器不执行AI任务
    dataSync: true,
    monitoring: true,
  },
};
```

### 3. 本地开发环境 (config.local.js)
```javascript
config.server = {
  location: 'local',
  enableSchedule: false,  // 本地开发不启用定时任务
  features: {
    aiTask: false,
    dataSync: false,
    monitoring: false,
  },
};
```

## 使用方法

1. 在对应的配置文件中设置 `config.server`
2. `gz.js` 定时任务会自动读取配置
3. 只有当 `location === 'gz'` 且 `features.aiTask === true` 时才执行任务

## 优势

1. **配置化管理**: 不再依赖网络请求获取IP
2. **环境隔离**: 不同环境有不同的行为
3. **功能开关**: 可以灵活控制各种功能的启用
4. **易于维护**: 配置集中管理，修改方便
5. **性能提升**: 避免了每次执行时的网络请求

## 迁移说明

### 修改前 (通过IP判断)
```javascript
let ip = await axios.get('https://ip.wcy9.com/cnip.php');
ip = ip.data.toString('utf8');
if (ip === '************') {
  // 执行任务
}
```

### 修改后 (通过配置判断)
```javascript
const config = ctx.app.config;
const serverConfig = config.server || {};
const isGzServer = serverConfig.location === 'gz';
const aiTaskEnabled = serverConfig.features?.aiTask === true;

if (isGzServer && aiTaskEnabled) {
  // 执行任务
}
```

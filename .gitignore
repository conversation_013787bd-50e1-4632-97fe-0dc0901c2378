logs/
npm-debug.log
yarn-error.log
node_modules/
package-lock.json
yarn.lock
coverage/
.idea/
.yarn/
run/
.DS_Store
*.sw*
*.un~
typings/
.nyc_output/
app/public/img/uploadfile
config/config.prod.js
/15342214250228321885210141942212290252552.ico
pnpm-lock.yaml
.vs/
changes.diff
commit_msg.txt
status.txt
# 临时文件和文档草稿
*.tmp
*.temp
*.bak
*_GUIDE.md
*_DRAFT.md
TEMP_*.md
egg-project.code-workspace
# 系统文件
Thumbs.db
.Spotlight-V100
.Trashes
vue
fs-go
# 自动生成的提交相关文件
commit_debug.json
commit_error.json
commit_msg.txt
nextjs/
# VSCode便携版文件夹
vscode-portable/
.roo
# Fastify项目 (忽略整个目录)
fastify/

# Go项目 (忽略整个目录)
fs-go/
Makefile

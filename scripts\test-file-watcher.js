#!/usr/bin/env node

/**
 * 文件监听性能测试脚本
 * 测试chokidar优化后的文件监听响应速度
 */

const fs = require('fs');
const path = require('path');

// 测试文件路径
const TEST_FILE = path.join(__dirname, '..', 'app', 'controller', 'test-watcher.js');
const HOT_RELOAD_LOG = path.join(__dirname, '..', 'run', 'hot-reload-log.json');

/**
 * 创建测试文件
 */
function createTestFile() {
  const content = `'use strict';

/**
 * 文件监听测试控制器 - 自动生成
 * 生成时间: ${new Date().toISOString()}
 */

const Controller = require('egg').Controller;

class TestWatcherController extends Controller {
  async index() {
    const { ctx } = this;
    ctx.body = {
      message: '文件监听测试',
      timestamp: new Date().toISOString(),
      testId: '${Math.random().toString(36).substr(2, 9)}',
    };
  }
}

module.exports = TestWatcherController;
`;

  fs.writeFileSync(TEST_FILE, content);
  console.log(`📝 创建测试文件: ${TEST_FILE}`);
}

/**
 * 修改测试文件
 */
function modifyTestFile(iteration) {
  const content = `'use strict';

/**
 * 文件监听测试控制器 - 修改 #${iteration}
 * 修改时间: ${new Date().toISOString()}
 */

const Controller = require('egg').Controller;

class TestWatcherController extends Controller {
  async index() {
    const { ctx } = this;
    ctx.body = {
      message: '文件监听测试 - 修改 #${iteration}',
      timestamp: new Date().toISOString(),
      testId: '${Math.random().toString(36).substr(2, 9)}',
      iteration: ${iteration},
    };
  }

  // 测试方法 #${iteration}
  async test${iteration}() {
    const { ctx } = this;
    ctx.body = {
      test: 'iteration_${iteration}',
      timestamp: Date.now(),
    };
  }
}

module.exports = TestWatcherController;
`;

  fs.writeFileSync(TEST_FILE, content);
  console.log(`✏️ 修改测试文件 #${iteration}: ${TEST_FILE}`);
}

/**
 * 删除测试文件
 */
function deleteTestFile() {
  if (fs.existsSync(TEST_FILE)) {
    fs.unlinkSync(TEST_FILE);
    console.log(`🗑️ 删除测试文件: ${TEST_FILE}`);
  }
}

/**
 * 读取热重载日志
 */
function readHotReloadLog() {
  if (!fs.existsSync(HOT_RELOAD_LOG)) {
    return [];
  }

  try {
    return JSON.parse(fs.readFileSync(HOT_RELOAD_LOG, 'utf8'));
  } catch (error) {
    console.error('❌ 读取热重载日志失败:', error.message);
    return [];
  }
}

/**
 * 分析热重载性能
 */
function analyzeHotReloadPerformance() {
  const logs = readHotReloadLog();
  
  if (logs.length === 0) {
    console.log('📊 暂无热重载数据');
    return;
  }

  console.log('\n📈 热重载性能分析');
  console.log('='.repeat(50));

  // 最近的重载记录
  const recent = logs.slice(-10);
  const reloadTimes = recent.map(log => log.reloadTime);

  if (reloadTimes.length > 0) {
    const avgReloadTime = Math.round(reloadTimes.reduce((sum, time) => sum + time, 0) / reloadTimes.length);
    const minReloadTime = Math.min(...reloadTimes);
    const maxReloadTime = Math.max(...reloadTimes);

    console.log(`🔄 热重载次数: ${logs.length}`);
    console.log(`⚡ 平均响应时间: ${avgReloadTime}ms`);
    console.log(`🚀 最快响应时间: ${minReloadTime}ms`);
    console.log(`🐌 最慢响应时间: ${maxReloadTime}ms`);

    // 最近的重载记录
    console.log('\n📋 最近10次热重载:');
    recent.forEach((log, index) => {
      const time = new Date(log.timestamp).toLocaleTimeString('zh-CN');
      console.log(`   ${index + 1}. ${log.reloadTime}ms - ${time} (${log.type})`);
      if (log.changedFiles && log.changedFiles.length > 0) {
        console.log(`      文件: ${log.changedFiles.join(', ')}`);
      }
    });

    // 性能评估
    console.log('\n💡 性能评估:');
    if (avgReloadTime < 200) {
      console.log('✅ 热重载性能优秀 (< 200ms)');
    } else if (avgReloadTime < 500) {
      console.log('🟡 热重载性能良好 (200-500ms)');
    } else {
      console.log('🔴 热重载性能需要优化 (> 500ms)');
    }
  }
}

/**
 * 执行文件监听测试
 */
async function runWatcherTest() {
  console.log('🧪 开始文件监听性能测试');
  console.log('='.repeat(50));

  // 清理之前的测试文件
  deleteTestFile();

  console.log('⏳ 等待3秒，确保应用已启动...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  try {
    // 测试1: 创建文件
    console.log('\n📝 测试1: 创建文件');
    createTestFile();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试2: 修改文件 (5次)
    console.log('\n✏️ 测试2: 修改文件 (5次)');
    for (let i = 1; i <= 5; i++) {
      modifyTestFile(i);
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    // 测试3: 删除文件
    console.log('\n🗑️ 测试3: 删除文件');
    deleteTestFile();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 分析结果
    console.log('\n📊 分析测试结果...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    analyzeHotReloadPerformance();

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 清理测试文件
    deleteTestFile();
    console.log('\n✅ 文件监听测试完成');
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--analyze')) {
    // 仅分析现有日志
    analyzeHotReloadPerformance();
  } else if (args.includes('--test')) {
    // 执行完整测试
    await runWatcherTest();
  } else {
    console.log('📖 文件监听性能测试工具');
    console.log('');
    console.log('用法:');
    console.log('  node scripts/test-file-watcher.js --test     执行完整测试');
    console.log('  node scripts/test-file-watcher.js --analyze  分析现有日志');
    console.log('');
    console.log('注意: 执行测试前请确保应用已启动');
  }
}

// 如果直接运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  createTestFile,
  modifyTestFile,
  deleteTestFile,
  analyzeHotReloadPerformance,
  runWatcherTest,
};

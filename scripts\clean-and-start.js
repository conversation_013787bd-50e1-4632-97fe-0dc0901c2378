#!/usr/bin/env node

/**
 * Windows兼容的清理和启动脚本
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🧹 清理缓存文件...');

// 清理函数 - Windows兼容
function cleanDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      const files = fs.readdirSync(dirPath);
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          cleanDirectory(filePath); // 递归清理子目录
          try {
            fs.rmdirSync(filePath);
          } catch (e) {
            // 忽略删除目录失败的错误
          }
        } else {
          try {
            fs.unlinkSync(filePath);
          } catch (e) {
            // 忽略删除文件失败的错误
          }
        }
      });
      console.log(`✅ 已清理: ${dirPath}`);
    } catch (error) {
      console.log(`⚠️  清理失败: ${dirPath} - ${error.message}`);
    }
  }
}

// 清理目录
const rootDir = path.join(__dirname, '..');
const logsDir = path.join(rootDir, 'logs');
const runDir = path.join(rootDir, 'run');

cleanDirectory(logsDir);
cleanDirectory(runDir);

console.log('🚀 启动开发服务器...');

// Windows兼容的启动命令
const isWindows = process.platform === 'win32';
const npmCmd = isWindows ? 'npm.cmd' : 'npm';

const startProcess = spawn(npmCmd, ['run', 'dev:fast'], {
  cwd: rootDir,
  stdio: 'inherit',
  shell: isWindows,
  env: {
    ...process.env,
    NODE_ENV: 'development',
    EGG_SERVER_ENV: 'local'
  }
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  startProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  startProcess.kill('SIGTERM');
});

startProcess.on('close', (code) => {
  console.log(`\n✅ 服务器已关闭，退出码: ${code}`);
  process.exit(code);
});

startProcess.on('error', (err) => {
  console.error('❌ 启动失败:', err);
  process.exit(1);
});

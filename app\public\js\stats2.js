function strB(b) {
  if (b < 1000) return b.toString() + 'B';
  if (b < 1000000) return (b / 1000).toFixed(2) + 'KB';
  if (b < 1000000000) return (b / 1000000).toFixed(2) + 'MB';
  if (b < 1000000000000) {
    return (b / 1000000000).toFixed(2) + 'GB';
  } else {
    return (b / 1000000000000).toFixed(2) + 'TB';
  }
}
var mem_tooltips = {},
  host_tooltips = {};
setInterval(async () => {
  var stats = await fetch('/stats/data')
    .then(res => res.json());
  for (var i = 0; i < stats.length; i++) {
    var { cpu, mem, net, host } = stats[i].data;
    let cur=$('tbody tr:eq('+i+')');
    if (!(host instanceof Array)) {
      var content =
          `系统: ${host.os}
平台: ${host.platform}
内核版本: ${host.kernelVersion}
内核架构: ${host.kernelArch}
启动: ${new Date(host.bootTime * 1000).toLocaleString()}
在线: ${(host.uptime / 86400).toFixed(2)}天`;
      if (!host_tooltips[sid]) host_tooltips[sid] = new mdui.Tooltip(`#host`, {});
      host_tooltips[sid].$element[0].innerText = content;
    }
    if (!(host instanceof Array)) {
      cur.children('#uptime').text((host.uptime / 86400).toFixed(2) + '天');
    }
    if (cpu instanceof Array) {
      var cpus = [],
        cpu_p = 0;
      for (var x of cpu) cpus.push((x.multi * 100).toFixed(2) + '%'), cpu_p += x.multi;
      cpu_p /= cpu.length;
      E(`cpux`).innerText = cpus.join(' | ');
      E(`cpu_progress`).style.width = `${cpu_p * 100}%`;
    } else {
      // E(`cpux`).innerText = (cpu.multi * 100).toFixed(2) + '%';
      let cpuxx = (cpu.multi * 100).toFixed(2) + '%';
      cur.find('#cpux').text(cpuxx);
      let wd = `${cpu.multi * 100}%`;
      cur.find('#cpu_progress').css('width',wd);
    }
    if (mem instanceof Array) {
      var MEM = [],
        MEM_P = 0;
      for (var x of mem) {
        var { used, total } = x.virtual,
          usage = used / total;
        MEM.push((usage * 100).toFixed(2) + '%'), MEM_P += usage;
      }
      MEM_P /= mem.length;
      E(`mem`).innerText = MEM.join(' | ');
      console.log(MEM.join(' | '));
      E(`${sid}_MEM_progress`).style.width = `${MEM_P * 100}%`;
    } else {
      var { used, total } = mem.virtual,
        usage = used / total;
      // E(`mem`).innerText = (usage * 100).toFixed(2) + '%';
      let memm = (usage * 100).toFixed(2) + '%';
      cur.find('#memx').text(memm);
      let wd =`${usage*100}%`;
      cur.find('#mem_progress').css('width',wd);
      // E(`${sid}_MEM_progress`).style.width=`${usage*100}%`;
      var content = `${strB(used)}/${strB(total)}`;
      if (mem_tooltips[sid]) {
        mem_tooltips[sid].$element[0].innerText = content;
      } else {
        mem_tooltips[sid] = new mdui.Tooltip(`#${sid}_MEM_item`, { content });
      }
    }
    if (net instanceof Array) {
      var IN = [],
        OUT = [];
      for (var { delta } of net) IN.push(delta.in), OUT.push(delta.out);
      // E(`downnet`).innerText = strB(Math.min(...IN));
      cur.children('#downnet').text(strB(Math.min(...IN)));
      // E(`upnet`).innerText = strB(Math.min(...OUT));
      cur.children('#upnet').text(strB(Math.min(...OUT)));

      var IN = [],
        OUT = [];
      for (var { total } of net) IN.push(total.in), OUT.push(total.out);
      // E(`downtotal`).innerText = strB(Math.min(...IN));
      cur.find('#downtotal').text(strB(Math.min(...IN)));
      // E(`uptotal`).innerText = strB(Math.min(...OUT));
      cur.find('#uptotal').text(strB(Math.min(...OUT)));
    } else {

      // E(`downnet`).innerText = strB(net.delta.in);
      cur.find('#downnet').text(strB(net.delta.in));
      // E(`upnet`).innerText = strB(net.delta.out);
      cur.find('#upnet').text(strB(net.delta.out));

      // E(`downtotal`).innerText = strB(net.total.in);
      cur.find('#downtotal').text(strB(net.total.in));
      // E(`uptotal`).innerText = strB(net.total.out);
      cur.find('#uptotal').text(strB(net.total.out));
    }
  }

  return;

  for (var [ sid, node ] of Object.entries(stats)) {
    if (node.stat && node.stat != -1) {
      var { cpu, mem, net, host } = node.stat;
      // console.log(sid,node);

      if (cpu instanceof Array) {
        var cpus = [],
          cpu_p = 0;
        for (var x of cpu) cpus.push((x.multi * 100).toFixed(2) + '%'), cpu_p += x.multi;
        cpu_p /= cpu.length;
        E(`${sid}_CPU`).innerText = cpus.join(' | ');
        E(`${sid}_CPU_progress`).style.width = `${cpu_p * 100}%`;
      } else {
        E(`${sid}_CPU`).innerText = (cpu.multi * 100).toFixed(2) + '%';
        E(`${sid}_CPU_progress`).style.width = `${cpu.multi * 100}%`;
      }

      if (mem instanceof Array) {
        var MEM = [],
          MEM_P = 0;
        for (var x of mem) {
          var { used, total } = x.virtual,
            usage = used / total;
          MEM.push((usage * 100).toFixed(2) + '%'), MEM_P += usage;
        }
        MEM_P /= mem.length;
        E(`${sid}_MEM`).innerText = MEM.join(' | ');
        E(`${sid}_MEM_progress`).style.width = `${MEM_P * 100}%`;
      } else {
        var { used, total } = mem.virtual,
          usage = used / total;
        E(`${sid}_MEM`).innerText = (usage * 100).toFixed(2) + '%';
        E(`${sid}_MEM_progress`).style.width = `${usage * 100}%`;
        var content = `${strB(used)}/${strB(total)}`;
        if (mem_tooltips[sid]) {
          mem_tooltips[sid].$element[0].innerText = content;
        } else {
          mem_tooltips[sid] = new mdui.Tooltip(`#${sid}_MEM_item`, { content });
        }
      }

      if (net instanceof Array) {
        var IN = [],
          OUT = [];
        for (var { delta } of net) IN.push(delta.in), OUT.push(delta.out);
        E(`${sid}_NET_IN`).innerText = strB(Math.min(...IN));
        E(`${sid}_NET_OUT`).innerText = strB(Math.min(...OUT));

        var IN = [],
          OUT = [];
        for (var { total } of net) IN.push(total.in), OUT.push(total.out);
        E(`${sid}_NET_IN_TOTAL`).innerText = strB(Math.min(...IN));
        E(`${sid}_NET_OUT_TOTAL`).innerText = strB(Math.min(...OUT));
      } else {
        E(`${sid}_NET_IN`).innerText = strB(net.delta.in);
        E(`${sid}_NET_OUT`).innerText = strB(net.delta.out);

        E(`${sid}_NET_IN_TOTAL`).innerText = strB(net.total.in);
        E(`${sid}_NET_OUT_TOTAL`).innerText = strB(net.total.out);
      }

      if (!(host instanceof Array)) {
        var content =
          `系统: ${host.os}
平台: ${host.platform}
内核版本: ${host.kernelVersion}
内核架构: ${host.kernelArch}
启动: ${new Date(host.bootTime * 1000).toLocaleString()}
在线: ${(host.uptime / 86400).toFixed(2)}天`;
        console.log(host_tooltips[sid]);
        console.log(host_tooltips);
        if (!host_tooltips[sid]) host_tooltips[sid] = new mdui.Tooltip(`#host`, {});
        host_tooltips[sid].$element[0].innerText = content;
      }
    }
  }
  mdui.mutation();
}, 1000);

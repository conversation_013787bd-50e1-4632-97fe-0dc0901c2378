#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🧪 简单测试 wkhtmltoimage...\n');

// 创建测试HTML
const testHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body { 
      font-family: Arial, sans-serif; 
      padding: 20px; 
      margin: 0;
    }
    h1 { color: #333; }
    .content { 
      background: #f5f5f5; 
      padding: 15px; 
      border-radius: 5px; 
    }
  </style>
</head>
<body>
  <h1>wkhtmltoimage 测试页面</h1>
  <div class="content">
    <p>这是一个测试页面，用于验证 wkhtmltoimage 功能。</p>
    <p>当前时间: ${new Date().toLocaleString()}</p>
    <p>如果你能看到这个图片，说明 wkhtmltoimage 工作正常！</p>
  </div>
</body>
</html>`;

const timestamp = Date.now();
const htmlFile = `/tmp/test_simple_${timestamp}.html`;
const imageFile = `/tmp/test_simple_${timestamp}.png`;

// 写入HTML文件
fs.writeFileSync(htmlFile, testHtml);
console.log('📝 HTML文件已创建:', htmlFile);

// 执行 wkhtmltoimage（使用兼容参数）
const args = [
  '--width', '1200',
  '--height', '0',
  '--format', 'png', 
  '--quality', '95',
  '--quiet',
  htmlFile,
  imageFile
];

console.log('🚀 执行命令: wkhtmltoimage', args.join(' '));

const child = spawn('wkhtmltoimage', args);
let stderr = '';

child.stderr.on('data', (data) => {
  stderr += data.toString();
});

child.on('close', (code) => {
  console.log('\n📊 执行结果:');
  console.log('退出代码:', code);
  
  if (stderr) {
    console.log('错误信息:', stderr);
  }
  
  if (code === 0 && fs.existsSync(imageFile)) {
    const stats = fs.statSync(imageFile);
    console.log('✅ 渲染成功!');
    console.log('📊 图片大小:', (stats.size / 1024).toFixed(2), 'KB');
    console.log('📁 图片路径:', imageFile);
    
    // 显示图片信息
    console.log('\n💡 你可以查看生成的图片:');
    console.log(`   ls -la ${imageFile}`);
    console.log(`   file ${imageFile}`);
  } else {
    console.log('❌ 渲染失败');
  }
  
  // 清理HTML文件
  try {
    fs.unlinkSync(htmlFile);
    console.log('🧹 HTML文件已清理');
  } catch (e) {
    console.log('⚠️ 清理HTML文件失败:', e.message);
  }
});

child.on('error', (error) => {
  console.log('❌ 启动失败:', error.message);
  
  // 清理HTML文件
  try {
    fs.unlinkSync(htmlFile);
  } catch (e) {
    // 忽略
  }
});

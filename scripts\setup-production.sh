#!/bin/bash

# Egg.js 生产环境部署和监控设置脚本

set -e

echo "🚀 设置 Egg.js 生产环境监控..."

# 1. 创建必要的目录
echo "📁 创建监控目录..."
sudo mkdir -p /var/log/eggjs-monitor
sudo mkdir -p /var/log/eggjs-analysis
sudo mkdir -p /var/run

# 2. 设置日志轮转
echo "📋 配置日志轮转..."
sudo tee /etc/logrotate.d/eggjs > /dev/null <<EOF
/root/eggjslogs/egg-server-example/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        # 重新加载应用以使用新的日志文件
        if [ -f /var/run/egg-server.pid ]; then
            kill -USR1 \$(cat /var/run/egg-server.pid)
        fi
    endscript
}

/var/log/eggjs-monitor/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# 3. 设置系统服务
echo "⚙️  配置系统服务..."
PROJECT_DIR=$(pwd)
sed "s|/path/to/your/egg/project|$PROJECT_DIR|g" scripts/eggjs.service > /tmp/eggjs.service
sudo mv /tmp/eggjs.service /etc/systemd/system/
sudo systemctl daemon-reload

# 4. 设置监控定时任务
echo "⏰ 配置监控定时任务..."
(crontab -l 2>/dev/null; echo "*/5 * * * * $PROJECT_DIR/scripts/production-monitor.sh >> /var/log/eggjs-monitor/cron.log 2>&1") | crontab -

# 5. 设置系统限制
echo "🔧 优化系统限制..."
sudo tee -a /etc/security/limits.conf > /dev/null <<EOF
# Egg.js 应用优化
root soft nofile 65536
root hard nofile 65536
root soft nproc 32768
root hard nproc 32768
EOF

# 6. 优化内核参数
echo "🔧 优化内核参数..."
sudo tee -a /etc/sysctl.conf > /dev/null <<EOF
# Egg.js 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_tw_recycle = 1
net.ipv4.ip_local_port_range = 1024 65000
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
vm.swappiness = 10
EOF

sudo sysctl -p

# 7. 安装必要的工具
echo "🛠️  安装监控工具..."
if ! command -v jq &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y jq
fi

if ! command -v htop &> /dev/null; then
    sudo apt-get install -y htop
fi

# 8. 设置权限
echo "🔐 设置权限..."
chmod +x scripts/*.sh
sudo chown -R root:root /var/log/eggjs-monitor
sudo chown -R root:root /var/log/eggjs-analysis

# 9. 创建健康检查脚本
echo "🏥 创建健康检查脚本..."
tee scripts/health-check.sh > /dev/null <<'EOF'
#!/bin/bash
# 简单的健康检查脚本

HEALTH_URL="http://localhost:7001/health"
TIMEOUT=10

response=$(curl -s --max-time $TIMEOUT "$HEALTH_URL")
status=$(echo "$response" | jq -r '.status' 2>/dev/null)

if [ "$status" = "ok" ]; then
    echo "✅ 应用健康状态正常"
    exit 0
else
    echo "❌ 应用健康检查失败"
    echo "响应: $response"
    exit 1
fi
EOF

chmod +x scripts/health-check.sh

# 10. 创建快速诊断脚本
echo "🔍 创建快速诊断脚本..."
tee scripts/quick-diagnosis.sh > /dev/null <<'EOF'
#!/bin/bash
# 快速诊断脚本

echo "🔍 Egg.js 应用快速诊断"
echo "时间: $(date)"
echo "================================"

# 检查进程
echo "📋 进程状态:"
ps aux | grep node | grep -v grep || echo "❌ 没有找到 Node.js 进程"

# 检查端口
echo -e "\n🌐 端口状态:"
netstat -tlnp | grep :7001 || echo "❌ 端口 7001 未监听"

# 检查内存
echo -e "\n💾 内存使用:"
free -h

# 检查磁盘
echo -e "\n💿 磁盘使用:"
df -h /

# 检查负载
echo -e "\n📈 系统负载:"
uptime

# 检查最近错误
echo -e "\n🚨 最近错误 (最后10行):"
if [ -f "/root/eggjslogs/egg-server-example/common-error.log" ]; then
    tail -10 /root/eggjslogs/egg-server-example/common-error.log
else
    echo "❌ 错误日志文件不存在"
fi

# 健康检查
echo -e "\n🏥 健康检查:"
curl -s --max-time 5 http://localhost:7001/health | jq . 2>/dev/null || echo "❌ 健康检查失败"
EOF

chmod +x scripts/quick-diagnosis.sh

echo "✅ 生产环境监控设置完成!"
echo ""
echo "📋 可用命令:"
echo "  npm run prod:monitor        - 运行一次监控检查"
echo "  npm run prod:monitor:daemon - 启动守护进程监控"
echo "  npm run prod:analyze        - 分析应用日志"
echo "  npm run prod:health         - 检查应用健康状态"
echo "  ./scripts/quick-diagnosis.sh - 快速诊断"
echo ""
echo "🔧 系统服务:"
echo "  sudo systemctl start eggjs   - 启动服务"
echo "  sudo systemctl enable eggjs  - 开机自启"
echo "  sudo systemctl status eggjs  - 查看状态"
echo ""
echo "📊 监控日志位置:"
echo "  /var/log/eggjs-monitor/      - 监控日志"
echo "  /var/log/eggjs-analysis/     - 分析报告"
echo ""
echo "⚠️  注意: 请重启系统以使内核参数生效，或运行 'sudo sysctl -p'"

'use strict';

/**
 * Agent启动文件 - 初始化优化的文件监听器
 */

module.exports = (agent) => {
  // 记录Agent启动时间
  const agentStartTime = Date.now();
  
  // 在Agent启动完成后初始化优化的文件监听器
  agent.ready(() => {
    const startupTime = Date.now() - agentStartTime;
    console.log(`🚀 [Agent] 启动完成 - ${startupTime}ms`);
    
    // 初始化优化的文件监听器
    if (agent.config.env !== 'prod') {
      console.log('🔥 [Agent] 初始化优化文件监听器...');
      agent.initOptimizedWatcher();
    } else {
      console.log('📦 [Agent] 生产环境，跳过文件监听器初始化');
    }
  });

  // Agent错误处理
  agent.on('error', (err) => {
    console.error('❌ [Agent] 发生错误:', err);
  });

  // 监听来自App的消息
  agent.messenger.on('agent-watcher-stats', () => {
    const stats = agent.getWatcherStats();
    agent.messenger.sendToApp('watcher-stats-response', stats);
  });

  // 优雅关闭处理
  agent.beforeClose(() => {
    console.log('🔥 [Agent] 正在关闭...');
  });
};

@echo off
echo 🔍 查找占用 7001 端口的进程...

for /f "tokens=5" %%a in ('netstat -ano ^| findstr :7001') do (
    echo 📋 找到进程 PID: %%a
    echo 🔪 正在终止进程...
    taskkill /PID %%a /F
    if !errorlevel! == 0 (
        echo ✅ 成功终止进程 %%a
    ) else (
        echo ❌ 终止进程失败
    )
)

echo 🔍 验证端口状态...
netstat -ano | findstr :7001
if %errorlevel% == 1 (
    echo ✅ 端口 7001 已释放
) else (
    echo ⚠️ 端口 7001 仍被占用
)

echo.
echo 💡 现在可以启动应用了: pnpm run dev:all
pause

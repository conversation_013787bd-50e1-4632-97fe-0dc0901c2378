const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const FormData = require('form-data');
const fs = require('fs');
const { Buffer } = require('buffer');
const { log } = require('console');
const path = require('path');

class TeleService extends Service {
  async push(text, type = 1) {
    //CongBot
    let url =
      'https://api.telegram.org/bot1065855390:AAH1csAwCPZwR547FZMLJF6HxgSdqCv9PyM/sendMessage';
    let chat_id = '383938699';
    //斗鱼直播推送群
    if (type === 2) {
      url =
        'https://api.telegram.org/bot479408724:AAFtOx2rh1Yikf-9f4KvuaIPaoAC6Ysh3jU/sendMessage';
      chat_id = '-290805035';
    }
    //斗鱼bot
    if (type === 3) {
      url =
        'https://api.telegram.org/bot479408724:AAFtOx2rh1Yikf-9f4KvuaIPaoAC6Ysh3jU/sendMessage';
      chat_id = '383938699';
    }
    return await axios
      .get(url, {
        params: {
          chat_id: chat_id,
          text: text,
        },
      })
      .then(function (response) {
        return response.data;
      });
  }

  async photo(text) {
    var data = new FormData();
    data.append('chat_id', '383938699');
    data.append('photo', fs.createReadStream(text));
    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://api.telegram.org/bot479408724:AAFtOx2rh1Yikf-9f4KvuaIPaoAC6Ysh3jU/sendPhoto',
      headers: {
        ...data.getHeaders(),
      },
      data: data,
    };
    return await axios(config)
      .then(function (response) {
        return response.data;
      })
      .catch(function (error) {
        console.log(error);
      });
  }

  async upload(text) {
    let data = new FormData();
    data.append('file', fs.createReadStream(text));
    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://img1.101616.xyz/upload',
      headers: {
        ...data.getHeaders(),
      },
      data: data,
    };
    return await axios(config)
      .then(function (response) {
        return response.data;
      })
      .catch(function (error) {
        console.log(error);
      });
  }

  async num() {
    return await axios
      .post('http://juhaodan.cn/api/numSelect', {
        amounts: '10',
        cityCode: '395',
        goodsId: '981610241535',
        provinceCode: '38',
        searchCategory: '2',
        searchType: '',
        searchValue: '',
        serviceKey: 'dg_juhao_HIUR37F3L6EN878IU59EP6FKL',
        sign: 'D8F4A1BB1FE8B30297AC2A138FA65DCD',
      })
      .then(function (res) {
        console.log(res);
        let shuzi = [];
        let iii = 0;
        let wcz = 0;
        for (let sz of res.data.body.numArray) {
          if (sz === 1 || sz === 0 || sz === 5) {
            wcz = sz;
          } else {
            shuzi[iii] = sz;
            iii++;
          }
        }
        // console.log(shuzi);
        return shuzi;
      });
  }

  async ydnofo() {
    function cleanArray(actual) {
      const newArray = [];
      for (let i = 0; i < actual.length; i++) {
        if (actual[i]) {
          newArray.push(actual[i]);
        }
      }
      return newArray;
    }

    function toQueryString(obj) {
      if (!obj) return '';
      return cleanArray(
        Object.keys(obj).map((key) => {
          if (obj[key] === undefined) return '';
          return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
        }),
      ).join('&');
    }

    return await axios
      .post(
        'https://wap.fj.10086.cn/weixin/we_portal/numberPickClient/readNumbers.do?math=0.****************',
        toQueryString({
          searchStr: '',
          cityCode: 596,
          code: 0,
          numberFilter: '',
          utm_id: 71195,
          cityDefault: '',
        }),
      )
      .then(function (res) {
        return res.data.data.numbersList;
      });
  }

  async ydbm(aaa, citycode) {
    function cleanArray(actual) {
      const newArray = [];
      for (let i = 0; i < actual.length; i++) {
        if (actual[i]) {
          newArray.push(actual[i]);
        }
      }
      return newArray;
    }

    function toQueryString(obj) {
      if (!obj) return '';
      return cleanArray(
        Object.keys(obj).map((key) => {
          if (obj[key] === undefined) return '';
          return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
        }),
      ).join('&');
    }

    return await axios
      .post(
        'https://wap.fj.10086.cn/weixin/we_portal/numberPickClient/readNumbers.do?math=0.****************',
        toQueryString({
          searchStr: aaa,
          cityCode: citycode,
          code: 0,
          numberFilter: '',
          utm_id: 71195,
          cityDefault: '',
        }),
      )
      .then(function (res) {
        return res.data.data.numbersList;
      });
  }

  async wxh() {
    return await axios
      .get('http://i.wcy9.com/wxh.php')
      .then(function (response) {
        return response.data;
      });

    // return await (
    //     axios.get('https://card.10010.com/NumDockerApp/NumberCenter/qryNum/privacyToken?numRetailList=0&callback=jsonp_queryMoreNums&provinceCode=38&cityCode=395&qryMode=02&token=x7fHub9URZl8qRlMVBKkrln%2F4wUEqcI4uHS3kSKO4e02LqFSmpiNuh1Bzop53VnXwgL7qtN6YxRog%2FPnRXVsNb6b%2BG%2F5KLb1UxbvaJt9Or9PvA8o%2F7b0%2FXydockA1KVLMiZe7F0EL%2FIeZcq7NVLqErNLEGK99SXUeZYZORL4SCTWGD8qEF6BgZYgSSkjLX7SUDyVSZ6XhBVhblyJMxrJTw%3D%3D&channel=B2C&_=1639468075204')
    //         .then(function (response) {
    //             return response.data.slice(20).slice(0,response.data.slice(20).length-2)
    //         })
    // );
  }

  async nfc() {
    return await axios
      .get('http://165.22.103.222:18060/')
      .then(function (response) {
        return response.data;
      });
  }

  async nfcarm() {
    return await axios
      .get('http://152.69.223.51:18060/')
      .then(function (response) {
        return response.data;
      });
  }
}

module.exports = TeleService;

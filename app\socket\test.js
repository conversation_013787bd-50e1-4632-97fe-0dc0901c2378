'use strict';

module.exports = (app) => {
  const { router, controller, io } = app;
  
  // 测试socket连接
  io.of('/test').on('connection', async (socket) => {
    console.log('✅ 测试客户端连接:', socket.id);
    
    socket.emit('test', {
      message: '测试连接成功',
      socketId: socket.id,
      timestamp: new Date().toISOString()
    });
    
    socket.on('disconnect', () => {
      console.log('❌ 测试客户端断开:', socket.id);
    });
  });
}; 
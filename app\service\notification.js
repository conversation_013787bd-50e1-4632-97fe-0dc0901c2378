'use strict';

const Service = require('egg').Service;
const os = require('os');

class NotificationService extends Service {
  
  /**
   * 发送应用掉线通知到飞书
   * @param {Object} options 通知选项
   */
  async sendDownAlert(options = {}) {
    const {
      reason = '未知原因',
      details = {},
      severity = 'critical'
    } = options;

    const hostname = os.hostname();
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    
    // 构建通知消息
    const alertMessage = this.buildDownAlertMessage({
      hostname,
      timestamp,
      reason,
      details,
      severity
    });

    try {
      // 发送到飞书 - 使用带标题的文本方法
      await this.ctx.service.feishu.fsText(alertMessage, '🔴 应用掉线告警');
      this.ctx.logger.info('应用掉线通知已发送到飞书');

      // 同时记录到本地日志
      this.ctx.logger.error('应用掉线告警', {
        reason,
        details,
        timestamp,
        hostname
      });

    } catch (error) {
      this.ctx.logger.error('发送飞书通知失败:', error);
      // 记录到本地日志
      this.ctx.logger.error('应用掉线告警 (飞书通知失败)', {
        reason,
        details,
        timestamp,
        hostname,
        notificationError: error.message
      });
    }
  }

  /**
   * 发送应用恢复通知
   */
  async sendRecoveryAlert() {
    const hostname = os.hostname();
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    
    const message = `🟢 **应用恢复通知**
    
**服务器**: ${hostname}
**时间**: ${timestamp}
**状态**: 应用已恢复正常运行

✅ 服务已重新启动并正常响应`;

    try {
      await this.ctx.service.feishu.fsText(message, '🟢 应用恢复通知');
      this.ctx.logger.info('应用恢复通知已发送到飞书');
    } catch (error) {
      this.ctx.logger.error('发送恢复通知失败:', error);
    }
  }

  /**
   * 发送资源告警通知
   */
  async sendResourceAlert(type, usage, threshold) {
    const hostname = os.hostname();
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    
    const typeNames = {
      memory: '内存',
      cpu: 'CPU',
      disk: '磁盘'
    };
    
    const message = `⚠️ **资源使用告警**
    
**服务器**: ${hostname}
**时间**: ${timestamp}
**资源类型**: ${typeNames[type] || type}
**当前使用率**: ${usage}%
**告警阈值**: ${threshold}%

🔍 请及时检查服务器资源使用情况`;

    try {
      await this.ctx.service.feishu['fs'](message);
      this.ctx.logger.warn(`${type}资源告警通知已发送`, { usage, threshold });
    } catch (error) {
      this.ctx.logger.error('发送资源告警失败:', error);
    }
  }

  /**
   * 构建掉线告警消息
   */
  buildDownAlertMessage({ hostname, timestamp, reason, details, severity }) {
    const severityEmojis = {
      critical: '🔴',
      warning: '🟡',
      info: '🔵'
    };

    const emoji = severityEmojis[severity] || '🔴';
    
    let message = `${emoji} **应用掉线告警**

**服务器**: ${hostname}
**时间**: ${timestamp}
**严重程度**: ${severity.toUpperCase()}
**掉线原因**: ${reason}`;

    // 添加详细信息
    if (details.pid) {
      message += `\n**进程ID**: ${details.pid}`;
    }
    
    if (details.memory) {
      message += `\n**内存使用**: ${details.memory}`;
    }
    
    if (details.cpu) {
      message += `\n**CPU使用**: ${details.cpu}`;
    }
    
    if (details.uptime) {
      message += `\n**运行时长**: ${details.uptime}`;
    }
    
    if (details.lastError) {
      message += `\n**最后错误**: ${details.lastError}`;
    }

    message += `\n\n🔧 **建议操作**:
1. 检查服务器资源使用情况
2. 查看应用错误日志
3. 检查数据库连接状态
4. 验证网络连接
5. 考虑重启应用服务`;

    return message;
  }

  /**
   * 发送定期健康报告
   */
  async sendHealthReport(healthData) {
    const hostname = os.hostname();
    const timestamp = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    
    const status = healthData.status === 'ok' ? '🟢 正常' : '🔴 异常';
    
    let message = `📊 **应用健康报告**

**服务器**: ${hostname}
**时间**: ${timestamp}
**整体状态**: ${status}
**运行时长**: ${Math.round(healthData.uptime / 3600)}小时`;

    if (healthData.system) {
      message += `\n**系统负载**: ${healthData.system.loadAverage['1min'].toFixed(2)}`;
      message += `\n**内存使用**: ${healthData.system.memory.usage}%`;
    }

    if (healthData.checks) {
      message += `\n\n**服务检查**:`;
      Object.entries(healthData.checks).forEach(([service, check]) => {
        const statusIcon = check.status === 'ok' ? '✅' : '❌';
        message += `\n${statusIcon} ${service}: ${check.status}`;
      });
    }

    try {
      await this.ctx.service.feishu['fs'](message);
      this.ctx.logger.info('健康报告已发送到飞书');
    } catch (error) {
      this.ctx.logger.error('发送健康报告失败:', error);
    }
  }
}

module.exports = NotificationService;

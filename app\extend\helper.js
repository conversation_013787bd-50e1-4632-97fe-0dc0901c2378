const dayjs = require('dayjs');
const relativeTime = require('dayjs/plugin/relativeTime');
const axios = require('axios');

// 启用相对时间插件
dayjs.extend(relativeTime);

exports.relativeTime = (time) => dayjs(new Date(time * 1000)).fromNow();
module.exports = {
  gai(url, page = 2) {
    var preurl = url.split('/');
    var type = preurl[3];
    preurl = preurl[4].split('.');
    var id = preurl[0];
    var all = [];
    for (var i = 0; i <= page; i++) {
      if (i === 0) {
        url = 'http://www.xiurenji.cc/' + type + '/' + id + '.html';
      } else {
        url = 'http://www.xiurenji.cc/' + type + '/' + id + '_' + i + '.html';
      }
      all.push(url);
    }
    return all;
  },
  dateNow() {
    return dayjs().format('YYYY-MM-DD HH:mm:ss');
  },

  dayNow() {
    return dayjs().format('YYYY-MM-DD');
  },

  dayformat(date) {
    return dayjs(date).format('YYYY-MM-DD');
  },

  dateformat(date) {
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
  },

  async getPublicip() {
    let ip = await axios.get('http://ip.wcy9.com/cnip.php');
    ip = ip.data.toString('utf8'); // 将二进制数据转换为字符串
    return ip;
  },
};

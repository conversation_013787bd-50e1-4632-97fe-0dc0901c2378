<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <script src="/public/vue/vue.min.js"></script>
  <script src="/public/element/index.js"></script>
  <link rel="stylesheet" href="/public/element/index.css">
  <script src="/public/js/axios.min.js"></script>
  <title>Element Plus demo</title>
</head>

<body>
<div>
  {[jzsj]}
</div>
<div id="app">
  <el-alert :title="this.time" type="success" center></el-alert>
  <el-table :data="tableData" :row-class-name="tableRowClassName" border
            :default-sort="{prop: 'total', order: 'descending'}">

    <el-table-column label="序号" type="index" :index="indexMethod">
    </el-table-column>
    <el-table-column label="单位" sortable width="100">
      <template #default="scope">
        <div>
          <p v-text="scope.row.competentDeptName"></p>
          <p v-text="scope.row.deptName"></p>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="allNum" label="总人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="examineNum" label="	审核通过人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="holdnum" label="待定人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="notExamineNum" label="待审核人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="notApprovedNum" label="审核不通过人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="stuHasAbandon" label="放弃人数" sortable width="100%">
    </el-table-column>
    <el-table-column label="地区地址" sortable width="100%">
      <template #default="scope">
        <div>
          <p v-text="scope.row.areaName"></p>
          <p v-text="scope.row.unitAddress"></p>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="招聘岗位" width="100%">
      <template #default="scope">
        <div>
          <p v-text="scope.row.postName"></p>
          <p v-text="scope.row.sex"></p>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="专业" width="160">
      <template #default="scope">
        <div>
          <p :class="{ 'blue-text': scope.row.specialty.includes('计算机信息管理类') }"
             v-text="scope.row.specialty"></p>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="postNum" label="招聘人数" sortable width="100%">
    </el-table-column>
    <el-table-column prop="deptCode" label="岗位代码" sortable width="100%">
    </el-table-column>
    <el-table-column prop="postCode" label="岗位序号" sortable width="100%">
    </el-table-column>

    <el-table-column prop="other" label="其他要求" sortable width="166">
    </el-table-column>
    <el-table-column prop="remark" label="备注" sortable width="166">
    </el-table-column>
    <el-table-column prop="update" label="更新时间" :formatter="formatterdate" width="110">
    </el-table-column>
  </el-table>
</div>

<script>
  new Vue({
    el: "#app",
    data: function() {
      return {
        tableData: null,
        link: "wcnm",
        updatetime: "",
        time: ""
      };
    },
    created() {

    },
    mounted() {   //自动触发写入的函数
      this.getData();
    },
    methods: {
      formatter(row, column) {
        return row.zbmrs - 0;
        //:formatter="formatter"
      },
      formatterdate(row, column) {
        if (row.update) {
          let DateString = row.update;
          let date = new Date(DateString);
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let Hours = date.getHours();
          let Minutes = date.getMinutes();
          let Seconds = date.getSeconds();
          if (month < 10) {
            month = "0" + month;
          }
          if (day < 10) {
            day = "0" + day;
          }
          return year + "-" + month + "-" + day + " " + Hours + ":" + Minutes + ":" + Seconds;
        }
      },
      tableRowClassName({ row, rowIndex }) {
        console.log(row);
        if (
          (row.postCode === "170701") ||
          (row.postCode === "171201") ||
          (row.postCode === "171601") ||
          (row.postCode === "210901") ||
          (row.postCode === "202601")
        ) {
          return "warning-row";
        } else if (
          (row.postCode === "110102") ||
          (row.postCode === "105703") ||
          (row.postCode === "106302") ||
          (row.postCode === "107403")
        ) {
          return "success-row";
        } else if (
          (row.postCode === "140203") ||
          (row.postCode === "150301") ||
          (row.postCode === "190303") ||
          (row.postCode === "210103")
        ) {
          return "error-row";
        }
        return "";
      },
      getData() {
        axios.get("/fjzz?all={[all]}&riqi=qz").then(res => {
          this.tableData = res.data;
          this.time = res.data[0].update;
          console.log(this.time);
        });
      },
      indexMethod(index) {
        return index * 1 + 1;
      }

    }
  });


</script>

<style>

    body {
        display: inline-block;
    }

    .el-table .warning-row {
        background: oldlace;
    }

    .el-table .success-row {
        background: #f0f9eb;
    }

    .el-table .error-row {
        background: #ffc3c3;
    }

    .el-table .man-row {
        background: #ffff90;
    }

    .blue-text {
        color: skyblue;
    }
</style>
</body>

</html>

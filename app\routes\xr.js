'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  router.get('/xr', controller.xrplus['index']);
  router.get('/xr1', controller.xrplus['plusindex']);
  router.get('/xrlist', controller.xrplus['clist']);
  router.get('/xrpluslist', controller.xrplus['cclist']);
  router.get('/sou', controller.xrplus['sou']);
  router.get('/xz', controller.xrplus['sindex1']);
  router.get('/xrs', controller.xrplus['sindex']);
  router.get('/xrpage', controller.xrplus['pages']);
  router.get('/xrinfo', controller.xrplus['info']);
  router.get('/xrpageinfo', controller.xrplus['pageinfo']);
  router.get('/xrpluspageinfo', controller.xrplus['xrpluspageinfo']);
  router.get('/xrnei', controller.xrplus['nei']);
  router.get('/xrplusnei', controller.xrplus['plusnei']);
  router.get('/xrdl', controller.xrplus['download']);
  router.get('/imglist', controller.xrplus['imglist']);
  router.get('/xrsql', controller.xrplus['sql']);
  router.get('/getxrindex', controller.xrplus['getlist']);
  router.get('/refmurl', controller.xrplus['refmurl']);
  router.get('/xrurl', controller.xrplus['xrurl']);
  router.get('/getxrimglist', controller.xrplus['getxrimglist']);
  router.get('/xr2index', controller.xrplus['xr2getlist']);
  router.get('/xr2', controller.xrplus['xr2']);
  router.get('/xr2nei', controller.xrplus['xr2nei']);
  router.get('/xr2imglist', controller.xrplus['xr2imglist']);

  router.get('/img', controller.xrplus['imgs']);
};

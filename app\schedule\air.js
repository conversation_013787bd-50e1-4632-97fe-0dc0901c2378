const axios = require('axios');
const { dateNow } = require('../extend/helper');
module.exports = {
  schedule: {
    interval: '62s', // 1 分钟间隔
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['prod'],
    immediate: true,
    enabled: true,
  },
  async task(ctx) {
    let ip = await axios.get('https://ip.wcy9.com/cnip.php');
    ip = ip.data.toString('utf8'); // 将二进制数据转换为字符串
    if (ip === '************') {
      // const data = ['airall'];
      const data = ['showip'];

      for (let i in data) {
        await axios
          .get('http://127.0.0.1:7001/' + data[i])
          .then((res) => {
            if (res) {
              // console.log(res.data);
            }
          })
          .catch((e) => {
            if (e) {
              ctx.service.feishu.fs(data[i] + '异常');
            }
          });
      }
    }
    /** 你的问题出在 一个定时任务文件中导出了多个任务（数组形式），而 Egg.js 的egg-schedule模块默认要求 每个文件对应一个定时任务，且必须导出一个包含schedule和task的对象（而非数组）。
错误原因
Egg.js 在扫描app/schedule目录时，会将每个文件视为一个独立的定时任务，并尝试从文件导出的内容中读取schedule和task属性。
你当前的代码导出了一个数组[job81s, job60s]，导致模块加载时无法从数组中找到schedule和task属性，从而触发 “必须包含这两个属性” 的错误。
解决方案：拆分任务为单独文件
Egg.js 推荐 每个定时任务单独放在一个文件中，这样更符合模块设计规范，也便于维护。你的问题出在 一个定时任务文件中导出了多个任务（数组形式），而 Egg.js 的egg-schedule模块默认要求 每个文件对应一个定时任务，且必须导出一个包含schedule和task的对象（而非数组）。
错误原因
Egg.js 在扫描app/schedule目录时，会将每个文件视为一个独立的定时任务，并尝试从文件导出的内容中读取schedule和task属性。
你当前的代码导出了一个数组[job81s, job60s]，导致模块加载时无法从数组中找到schedule和task属性，从而触发 “必须包含这两个属性” 的错误。
解决方案：拆分任务为单独文件
Egg.js 推荐 每个定时任务单独放在一个文件中，这样更符合模块设计规范，也便于维护。* */
    // await axios.get("http://127.0.0.1:7001/xccf").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xchl").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdaxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdaxmch").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdahfhl").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xmairxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xmairhfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/hfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lzxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sda").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lyxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lyhfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lylzxm").then(() => {});
  },
};

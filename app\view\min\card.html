<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Document</title>
</head>
<body>
<div id="app">
    <el-alert
            title="还有{{total}}题没做"
            type="warning">
    </el-alert>
    <div class="content">
        <div style="--color-background: red" v-html="question.content"></div>
        <el-button @click="finishqs(question.sid)" style="margin-bottom: 10px">
            <label>做完了</label>
        </el-button>
        <el-button @click="refreshPage" style="margin-bottom: 10px">
            <label>刷新</label>
        </el-button>
        <el-alert
                v-if="showAlert"
                title="点击成功"
                type="success">
        </el-alert>
        <div>
            <div v-for="(answer, index) in [question.A, question.B, question.C, question.D]">
                <el-button
                        :key="answer"
                        :value="answer" v-model="selectedAnswer" @click="checkAnswer(index)"
                        style="margin-bottom: 10px;margin-top: 10px; width: 100%;"

                >
                    <!--          <input type="radio" :id="answer" />-->
                    <label v-text="answer" style="width: 100%;overflow: auto;
   white-space: normal;
   word-wrap: break-word;"></label>
                </el-button>
            </div>

        </div>
        <div v-if="showAnalysis">
            <div>{{"答案是"}}<p v-text="question.referenceAnswer"></p></div>
            <br>
            <div v-html="question.referenceAnalysis"></div>
        </div>

    </div>
</div>
</body>
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                question: "",
                selectedAnswer: '',
                showAnalysis: false,
                showAlert: false,
                showStyle: ""
            };
        },
        created() {
            axios.get('/minget')
                .then((response) => {
                    this.question = response.data[0];
                    console.log(this.question)
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        mounted() {
        },
        methods: {
            refreshPage() {
                location.reload();
            },
            finishqs(data) {
                console.log(data);
                axios.post('/minupdate', {
                    id: data
                }).then(result => {
                    // console.log(result.data.qwmQuestionList.length);
                    this.showAlert = true;
                })
            },
            checkAnswer(data) {
                this.showAnalysis = true;
                let ans = "";
                if (data === 0) {
                    ans = "A";
                }
                if (data === 1) {
                    ans = "B";
                }
                if (data === 2) {
                    ans = "C";
                }
                if (data === 3) {
                    ans = "D";
                }

                console.log(typeof ans)
                console.log(ans)
                console.log(typeof this.question.referenceAnswer)
                console.log(this.question.referenceAnswer)

                if (ans === this.question.referenceAnswer) {
                    this.showStyle = "success"
                } else {
                    this.showStyle = "danger"
                }

                // console.log(data)
            },
        }
    })


</script>
<style>
    body {
        font-size: 22px;
    }

    @media screen and (min-width: 1900px) {
        .content {
            margin: 0 auto;
            width: 960px;
        }

    }

    @media screen and (min-width: 1000px) {
        .content {
            margin: 0 auto;
            width: 960px;
        }
    }

    button {
        font-size: 22px;
    }

    * {
        font-size: 22px;
    }

</style>
</html>

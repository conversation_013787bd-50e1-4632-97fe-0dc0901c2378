const axios = require('axios');
const { dateNow } = require('../extend/helper');
/**
 * @param {Egg.Application} app - egg application
 */
module.exports = {
  schedule: {
    interval: '60s', // 1 分钟间隔
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['prod'],
    immediate: true,
    enabled: true,
  },
  async task(ctx) {
    await axios
      .get('http://127.0.0.1:7001/querylog?limit=30000')
      .then((res) => {
        // console.log(checkip);
        // console.log(res.data);
        // console.log(dateNow());
      })
      .catch((e) => {
        if (e) {
        }
      });
    await axios
      .get('http://127.0.0.1:7001/checkip')
      .then((res) => {
        // console.log(checkip);
        // console.log(res.data);
        // console.log(dateNow());
      })
      .catch((e) => {
        if (e) {
        }
      });
  },
};

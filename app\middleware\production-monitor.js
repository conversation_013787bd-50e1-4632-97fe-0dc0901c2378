'use strict';

/**
 * 生产环境监控中间件
 * 监控资源使用、错误率、响应时间等关键指标
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

module.exports = (options = {}) => {
  const config = {
    enabled: options.enabled !== false,
    collectMetrics: options.collectMetrics !== false,
    metricsInterval: options.metricsInterval || 60000,
    slowRequestThreshold: options.slowRequestThreshold || 1000,
    memoryLeakDetection: options.memoryLeakDetection !== false,
    maxMemoryUsage: options.maxMemoryUsage || (os.totalmem() * 0.8),
    maxCpuUsage: options.maxCpuUsage || 0.9,
    ...options,
  };

  // 监控数据存储
  const metrics = {
    requests: {
      total: 0,
      errors: 0,
      slow: 0,
      responseTimes: [],
    },
    system: {
      memoryUsage: [],
      cpuUsage: [],
      lastGC: Date.now(),
    },
    alerts: [],
  };

  // 定期收集系统指标
  if (config.collectMetrics) {
    setInterval(() => {
      collectSystemMetrics();
    }, config.metricsInterval);
  }

  /**
   * 收集系统指标
   */
  function collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    // 记录内存使用
    metrics.system.memoryUsage.push({
      timestamp: Date.now(),
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      rss: memUsage.rss,
      external: memUsage.external,
    });

    // 记录CPU使用
    metrics.system.cpuUsage.push({
      timestamp: Date.now(),
      user: cpuUsage.user,
      system: cpuUsage.system,
    });

    // 只保留最近100个数据点
    if (metrics.system.memoryUsage.length > 100) {
      metrics.system.memoryUsage = metrics.system.memoryUsage.slice(-100);
    }
    if (metrics.system.cpuUsage.length > 100) {
      metrics.system.cpuUsage = metrics.system.cpuUsage.slice(-100);
    }

    // 检查内存泄漏
    if (config.memoryLeakDetection) {
      checkMemoryLeak(memUsage);
    }

    // 检查资源限制
    checkResourceLimits(memUsage);
  }

  /**
   * 检查内存泄漏
   */
  function checkMemoryLeak(memUsage) {
    const recentMemory = metrics.system.memoryUsage.slice(-10);
    if (recentMemory.length >= 10) {
      const trend = calculateMemoryTrend(recentMemory);
      if (trend > 0.1) { // 内存增长超过10%
        addAlert('MEMORY_LEAK', `检测到可能的内存泄漏，内存增长趋势: ${(trend * 100).toFixed(2)}%`);
      }
    }
  }

  /**
   * 计算内存增长趋势
   */
  function calculateMemoryTrend(memoryData) {
    if (memoryData.length < 2) return 0;
    
    const first = memoryData[0].heapUsed;
    const last = memoryData[memoryData.length - 1].heapUsed;
    
    return (last - first) / first;
  }

  /**
   * 检查资源限制
   */
  function checkResourceLimits(memUsage) {
    // 检查内存使用
    if (memUsage.rss > config.maxMemoryUsage) {
      addAlert('HIGH_MEMORY', `内存使用过高: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
    }

    // 检查堆内存使用率
    const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
    if (heapUsageRatio > 0.9) {
      addAlert('HIGH_HEAP_USAGE', `堆内存使用率过高: ${(heapUsageRatio * 100).toFixed(2)}%`);
    }
  }

  /**
   * 添加告警
   */
  function addAlert(type, message) {
    const alert = {
      type,
      message,
      timestamp: Date.now(),
      count: 1,
    };

    // 检查是否已存在相同类型的告警
    const existingAlert = metrics.alerts.find(a => a.type === type && Date.now() - a.timestamp < 300000); // 5分钟内
    if (existingAlert) {
      existingAlert.count++;
      existingAlert.timestamp = Date.now();
    } else {
      metrics.alerts.push(alert);
      console.warn(`⚠️ [生产监控] ${type}: ${message}`);
    }

    // 只保留最近50个告警
    if (metrics.alerts.length > 50) {
      metrics.alerts = metrics.alerts.slice(-50);
    }
  }

  /**
   * 保存监控报告
   */
  function saveMonitoringReport() {
    const reportPath = path.join(process.cwd(), 'run', 'production-monitoring.json');
    const reportDir = path.dirname(reportPath);

    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      timestamp: new Date().toISOString(),
      metrics: {
        requests: {
          total: metrics.requests.total,
          errors: metrics.requests.errors,
          slow: metrics.requests.slow,
          errorRate: metrics.requests.total > 0 ? metrics.requests.errors / metrics.requests.total : 0,
          averageResponseTime: metrics.requests.responseTimes.length > 0 
            ? metrics.requests.responseTimes.reduce((sum, time) => sum + time, 0) / metrics.requests.responseTimes.length 
            : 0,
        },
        system: {
          currentMemory: process.memoryUsage(),
          currentCpu: process.cpuUsage(),
          uptime: process.uptime(),
        },
        alerts: metrics.alerts.slice(-10), // 最近10个告警
      },
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  }

  // 定期保存监控报告
  setInterval(saveMonitoringReport, config.metricsInterval);

  // 进程退出时保存最终报告
  process.on('exit', saveMonitoringReport);
  process.on('SIGTERM', saveMonitoringReport);
  process.on('SIGINT', saveMonitoringReport);

  return async function productionMonitor(ctx, next) {
    if (!config.enabled) {
      return await next();
    }

    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    try {
      await next();

      // 记录成功请求
      const responseTime = Date.now() - startTime;
      metrics.requests.total++;
      metrics.requests.responseTimes.push(responseTime);

      // 检查慢请求
      if (responseTime > config.slowRequestThreshold) {
        metrics.requests.slow++;
        console.warn(`🐌 [生产监控] 慢请求: ${ctx.method} ${ctx.url} - ${responseTime}ms`);
      }

      // 只保留最近1000个响应时间
      if (metrics.requests.responseTimes.length > 1000) {
        metrics.requests.responseTimes = metrics.requests.responseTimes.slice(-1000);
      }

      // 设置监控响应头
      ctx.set('X-Response-Time', `${responseTime}ms`);
      ctx.set('X-Memory-Usage', `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);

    } catch (error) {
      // 记录错误请求
      metrics.requests.total++;
      metrics.requests.errors++;

      const responseTime = Date.now() - startTime;
      console.error(`❌ [生产监控] 请求错误: ${ctx.method} ${ctx.url} - ${responseTime}ms - ${error.message}`);

      // 检查错误率
      const errorRate = metrics.requests.errors / metrics.requests.total;
      if (errorRate > 0.05) { // 错误率超过5%
        addAlert('HIGH_ERROR_RATE', `错误率过高: ${(errorRate * 100).toFixed(2)}%`);
      }

      throw error;
    }
  };
};

/**
 * 获取监控统计信息
 */
function getMonitoringStats() {
  return {
    requests: {
      total: metrics.requests.total,
      errors: metrics.requests.errors,
      slow: metrics.requests.slow,
      errorRate: metrics.requests.total > 0 ? metrics.requests.errors / metrics.requests.total : 0,
    },
    system: {
      memoryUsage: metrics.system.memoryUsage.slice(-10),
      cpuUsage: metrics.system.cpuUsage.slice(-10),
    },
    alerts: metrics.alerts.slice(-10),
  };
}

module.exports.getMonitoringStats = getMonitoringStats;

'use strict';

const Controller = require('egg').Controller;

class ThinkprocessController extends Controller {
  async ping() {
    const { ctx } = this;
    const message = ctx.args[0];
    console.log('收到ping消息:', message);
    await ctx.socket.emit('pong', `收到ping: ${message}`);
  }

  async connect() {
    const { ctx } = this;
    console.log('✅ 客户端连接到thinkprocess命名空间:', ctx.socket.id);
    
    // 发送连接成功消息
    await ctx.socket.emit('connected', {
      message: '已连接到思考过程监听器',
      socketId: ctx.socket.id,
      timestamp: new Date().toISOString()
    });
  }

  async disconnect() {
    const { ctx } = this;
    console.log('❌ 客户端断开thinkprocess命名空间连接:', ctx.socket.id);
  }
}

module.exports = ThinkprocessController; 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="referrer" content="no-referrer"/>
    <title>Title</title>
</head>
<script src="https://libs.baidu.com/jquery/2.1.1/jquery.js"></script>
<body>
<div id="hc">
</div>
<script>
  $('#hc').empty();
  var type = '{{ type }}';
  var id = '{{ id }}';
  // console.log(type);

  $.get('/xr/info', {
    id: id,
    type: type
  }, function(result) {
    // $("#hc").append(result).append('<br>');
    // console.log(result);
    // var obj = JSON.parse(result);
    var page = result;
    console.log(page);
    for (var i = 0; i < page; i++) {
      $.get('/xr', {
        id: id,
        type: type,
        page: i
      }, function(result) {
        // $("#hc").append(result).append('<br>');
        // var obj = JSON.parse(result);
        var page = result.length;
        for (var ii = 0; ii < page; ii++) {
          $('#hc').append('<a href=' + result[ii] + '><img src="' + result[ii] + '"></a><br>');
          console.log(result[ii]);
        }
        // console.log(page);
        $('.id').val('');
      });
    }

  });
</script>
</body>
</html>
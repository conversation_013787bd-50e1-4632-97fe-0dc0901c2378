#!/bin/bash

# Debian专用Egg.js监控脚本 - 专门解决Debian环境下的掉线问题
# 增强版监控，包含系统资源、内存、网络等全面检查

APP_PORT=7001
CHECK_INTERVAL=60
LOG_FILE="/var/log/eggjs-debian-monitor.log"
CRASH_LOG="/var/log/eggjs-crash-analysis.log"
PROJECT_DIR="/home/<USER>"

# 创建日志目录
mkdir -p $(dirname $LOG_FILE)
mkdir -p $(dirname $CRASH_LOG)

# 记录日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 记录崩溃分析日志
crash_log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$CRASH_LOG"
}

# 检查应用是否运行
check_app_running() {
    if netstat -tlnp | grep ":$APP_PORT " > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 检查应用健康状态
check_app_health() {
    local response=$(curl -s --max-time 10 "http://localhost:$APP_PORT/health" 2>/dev/null)
    local status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

    if [ "$status" = "ok" ]; then
        return 0
    else
        return 1
    fi
}

# 检查系统内存状态
check_memory_status() {
    local mem_info=$(free -m)
    local total_mem=$(echo "$mem_info" | awk 'NR==2{print $2}')
    local used_mem=$(echo "$mem_info" | awk 'NR==2{print $3}')
    local mem_usage=$((used_mem * 100 / total_mem))

    log "💾 内存使用率: ${mem_usage}%"

    # 检查是否有OOM事件
    local oom_events=$(dmesg | grep -i "killed process" | tail -5)
    if [ ! -z "$oom_events" ]; then
        crash_log "🚨 检测到OOM Killer事件:"
        echo "$oom_events" >> "$CRASH_LOG"
    fi

    # 内存使用超过85%发出警告
    if [ $mem_usage -gt 85 ]; then
        log "⚠️ 内存使用率过高: ${mem_usage}%"
        return 1
    fi

    return 0
}

# 检查系统负载
check_system_load() {
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_ratio=$(echo "$load_avg $cpu_cores" | awk '{printf "%.2f", $1/$2}')

    log "📈 系统负载: $load_avg (${cpu_cores}核心, 比率: $load_ratio)"

    # 负载比率超过2.0发出警告
    if (( $(echo "$load_ratio > 2.0" | bc -l) )); then
        log "⚠️ 系统负载过高: $load_avg"
        return 1
    fi

    return 0
}

# 检查磁盘空间
check_disk_space() {
    local disk_usage=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
    log "💿 磁盘使用率: ${disk_usage}%"

    if [ $disk_usage -gt 90 ]; then
        log "⚠️ 磁盘空间不足: ${disk_usage}%"
        return 1
    fi

    return 0
}

# 检查网络连接状态
check_network_status() {
    local conn_count=$(netstat -an | grep :$APP_PORT | wc -l)
    local time_wait_count=$(netstat -an | grep TIME_WAIT | wc -l)

    log "🌐 网络连接数: $conn_count, TIME_WAIT: $time_wait_count"

    # TIME_WAIT连接过多可能导致问题
    if [ $time_wait_count -gt 1000 ]; then
        log "⚠️ TIME_WAIT连接过多: $time_wait_count"
        return 1
    fi

    return 0
}

# 检查进程状态
check_process_status() {
    local node_processes=$(ps aux | grep node | grep -v grep | wc -l)
    local egg_processes=$(ps aux | grep egg | grep -v grep | wc -l)

    log "🔍 Node.js进程数: $node_processes, Egg进程数: $egg_processes"

    # 检查是否有僵尸进程
    local zombie_count=$(ps aux | awk '$8 ~ /^Z/ { count++ } END { print count+0 }')
    if [ $zombie_count -gt 0 ]; then
        log "⚠️ 检测到 $zombie_count 个僵尸进程"
        return 1
    fi

    return 0
}

# 检查应用日志错误
check_app_logs() {
    local error_log="/root/eggjslogs/egg-server-example/common-error.log"

    if [ -f "$error_log" ]; then
        # 检查最近5分钟的错误
        local recent_errors=$(find "$error_log" -mmin -5 -exec tail -50 {} \; 2>/dev/null | grep -i error | wc -l)

        if [ $recent_errors -gt 0 ]; then
            log "🚨 最近5分钟发现 $recent_errors 个错误"
            crash_log "最近错误日志:"
            tail -20 "$error_log" >> "$CRASH_LOG"
            return 1
        fi
    fi

    return 0
}

# 全面系统检查
comprehensive_check() {
    local issues=0

    log "🔍 开始全面系统检查..."

    check_memory_status || issues=$((issues + 1))
    check_system_load || issues=$((issues + 1))
    check_disk_space || issues=$((issues + 1))
    check_network_status || issues=$((issues + 1))
    check_process_status || issues=$((issues + 1))
    check_app_logs || issues=$((issues + 1))

    if [ $issues -gt 0 ]; then
        log "⚠️ 发现 $issues 个系统问题"
        return 1
    else
        log "✅ 系统状态正常"
        return 0
    fi
}

# 发送详细的掉线通知
send_detailed_alert() {
    local reason="$1"
    local details="$2"

    log "🚨 发送详细掉线通知: $reason"

    # 收集系统信息
    local mem_info=$(free -h | head -2 | tail -1)
    local load_info=$(uptime)
    local disk_info=$(df -h / | tail -1)

    local alert_data=$(cat <<EOF
{
    "action": "sendDetailedAlert",
    "reason": "$reason",
    "details": {
        "timestamp": "$(date -Iseconds)",
        "hostname": "$(hostname)",
        "port": $APP_PORT,
        "details": "$details",
        "systemInfo": {
            "memory": "$mem_info",
            "load": "$load_info",
            "disk": "$disk_info",
            "processes": "$(ps aux | grep node | grep -v grep | wc -l) Node.js processes"
        }
    }
}
EOF
)

    # 发送通知
    curl -s --max-time 5 \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$alert_data" \
        "http://localhost:$APP_PORT/api/notification/alert" 2>/dev/null || \
        echo "$alert_data" >> "/var/log/pending-alerts.json"
}

# 智能重启应用（低内存环境优化）
smart_restart() {
    log "🔄 开始智能重启流程（低内存环境优化）..."

    # 检查内存状态
    local mem_total=$(free -m | awk 'NR==2{print $2}')
    local mem_available=$(free -m | awk 'NR==2{print $7}')
    local is_low_memory=false

    if [ $mem_total -lt 2048 ]; then
        is_low_memory=true
        log "⚠️ 检测到低内存环境: ${mem_total}MB"
    fi

    # 记录重启前的系统状态
    crash_log "=== 重启前系统状态 ==="
    crash_log "内存: $(free -h | head -2 | tail -1)"
    crash_log "负载: $(uptime)"
    crash_log "进程: $(ps aux | grep node | grep -v grep)"

    cd "$PROJECT_DIR" || {
        log "❌ 无法进入项目目录: $PROJECT_DIR"
        return 1
    }

    # 低内存环境的特殊处理
    if [ "$is_low_memory" = true ]; then
        log "🧹 低内存环境预处理..."

        # 强制垃圾回收和缓存清理
        sync
        echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true

        # 临时禁用swap（如果可能）
        swapoff -a 2>/dev/null || true
        swapon -a 2>/dev/null || true

        # 设置更严格的内存限制
        ulimit -v 524288  # 限制虚拟内存为512MB
    fi

    # 优雅停止
    log "🛑 优雅停止应用..."
    pnpm stop > /dev/null 2>&1
    sleep 5

    # 强制清理残留进程
    log "🧹 清理残留进程..."
    pkill -f "egg-server-example" 2>/dev/null || true
    pkill -f "egg-scripts" 2>/dev/null || true
    pkill -f "node.*egg" 2>/dev/null || true
    sleep 3

    # 再次清理系统缓存
    log "🧹 清理系统缓存..."
    sync
    echo 1 > /proc/sys/vm/drop_caches 2>/dev/null || true

    # 低内存环境启动策略
    if [ "$is_low_memory" = true ]; then
        log "🚀 低内存环境启动应用..."

        # 设置Node.js内存限制
        export NODE_OPTIONS="--max-old-space-size=256 --max-semi-space-size=32"

        # 移除jemalloc（在低内存环境下可能导致问题）
        if grep -q "LD_PRELOAD.*jemalloc" package.json; then
            log "🔧 临时移除jemalloc配置..."
            sed -i.bak 's/LD_PRELOAD=[^ ]* //' package.json
        fi

        # 使用更保守的启动方式
        timeout 60 pnpm exec egg-scripts start --daemon --title=egg --ignore-stderr
    else
        log "🚀 标准环境启动应用..."
        pnpm start > /dev/null 2>&1
    fi

    local start_result=$?

    if [ $start_result -eq 0 ]; then
        log "✅ 应用重启命令执行成功"
        sleep 20  # 低内存环境需要更长启动时间

        # 验证启动状态
        local retry_count=0
        while [ $retry_count -lt 6 ]; do
            if check_app_running; then
                sleep 5
                if check_app_health; then
                    log "✅ 应用重启成功并通过健康检查"

                    # 恢复jemalloc配置（如果之前移除了）
                    if [ -f "package.json.bak" ]; then
                        mv package.json.bak package.json
                        log "🔧 恢复jemalloc配置"
                    fi

                    return 0
                fi
            fi

            retry_count=$((retry_count + 1))
            log "⏳ 等待应用启动... (${retry_count}/6)"
            sleep 10
        done

        log "❌ 应用重启后健康检查失败"
        return 1
    else
        log "❌ 应用重启命令执行失败 (退出码: $start_result)"

        # 恢复jemalloc配置
        if [ -f "package.json.bak" ]; then
            mv package.json.bak package.json
        fi

        return 1
    fi
}

# 主监控循环
monitor_loop() {
    local last_status="unknown"
    local down_count=0
    local system_issues_count=0

    log "🚀 开始Debian专用监控 (端口: $APP_PORT)"

    while true; do
        local current_status="down"
        local has_system_issues=false

        # 检查应用状态
        if check_app_running; then
            if check_app_health; then
                current_status="healthy"
            else
                current_status="unhealthy"
            fi
        fi

        # 检查系统状态
        if ! comprehensive_check; then
            has_system_issues=true
            system_issues_count=$((system_issues_count + 1))
        else
            system_issues_count=0
        fi

        # 状态变化处理
        case "$current_status" in
            "healthy")
                if [ "$last_status" != "healthy" ]; then
                    log "✅ 应用状态恢复正常"
                    # 发送恢复通知
                    curl -s --max-time 5 \
                        -X POST \
                        -H "Content-Type: application/json" \
                        -d '{"action": "sendRecovery"}' \
                        "http://localhost:$APP_PORT/api/notification/alert" 2>/dev/null
                fi
                down_count=0
                ;;

            "unhealthy")
                log "⚠️ 应用运行但健康检查失败"
                if [ "$last_status" = "healthy" ]; then
                    send_detailed_alert "应用健康检查失败" "应用运行但响应异常"
                fi
                ;;

            "down")
                down_count=$((down_count + 1))
                log "❌ 应用未运行 (连续 $down_count 次)"

                if [ "$last_status" = "healthy" ] || [ "$last_status" = "unhealthy" ]; then
                    send_detailed_alert "应用进程停止" "端口 $APP_PORT 无响应"
                fi

                # 连续3次检测到掉线，或系统问题严重时立即重启
                if [ $down_count -ge 3 ] || [ $system_issues_count -ge 2 ]; then
                    smart_restart
                    down_count=0
                    system_issues_count=0
                fi
                ;;
        esac

        # 系统问题警告
        if [ "$has_system_issues" = true ] && [ "$current_status" = "healthy" ]; then
            log "⚠️ 应用正常但系统存在潜在问题 (连续 $system_issues_count 次)"
        fi

        last_status="$current_status"
        sleep $CHECK_INTERVAL
    done
}

# 主函数
main() {
    case "${1:-monitor}" in
        "monitor")
            monitor_loop
            ;;
        "check")
            comprehensive_check
            ;;
        "test")
            ./scripts/simple-monitor.sh test
            ;;
        *)
            echo "用法: $0 [monitor|check|test]"
            exit 1
            ;;
    esac
}

# 信号处理
trap 'log "🛑 Debian监控脚本停止"; exit 0' SIGINT SIGTERM

# 运行主函数
main "$@"

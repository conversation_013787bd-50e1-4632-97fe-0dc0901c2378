const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const FormData = require('form-data');
const fs = require('fs');
const { Buffer } = require('buffer');
const { log } = require('console');
const path = require('path');

class FeishuService extends Service {
  async fs(text) {
    const secret = 'MsrMWf48gWQilMkpJ9lcpe';
    const timestamp = Math.floor(Date.now() / 1000);
    const stringToSign = `${timestamp}\n${secret}`;
    const hmac = crypto.createHmac('sha256', stringToSign);
    const signature = hmac.digest('base64');
    const xurl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/8df456d9-de0c-4942-b48c-e4e65f77b80c';
    const data = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'text',
      content: {
        text: text,
      },
    };
    try {
      const response = await axios.post(xurl, data);
      console.log('飞书fs消息发送成功:', response.data);
      return response.data;
    } catch (error) {
      console.error('飞书fs消息发送失败:', error.message);
      if (error.response) {
        console.error('响应状态:', error.response.status);
        console.error('响应数据:', error.response.data);
      }
      throw error;
    }
  }

  async fs2(text) {
    const secret = 'uJ5OihJsGhEjzbJ5k7ewGd';
    const timestamp = Math.floor(Date.now() / 1000);
    const stringToSign = `${timestamp}\n${secret}`;
    const hmac = crypto.createHmac('sha256', stringToSign);
    const signature = hmac.digest('base64');
    const xurl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/c9bc1206-1824-4dba-b95f-361b737abd0f';
    const data = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'text',
      content: {
        text: text,
      },
    };
    try {
      const response = await axios.post(xurl, data);
      return response.data;
    } catch (error) {
      // console.error(error);
    }
  }

  async fs3(text) {
    const secret = 'tnKSokV6SosAfaGdBBuVSc';
    const timestamp = Math.floor(Date.now() / 1000);
    const stringToSign = `${timestamp}\n${secret}`;
    const hmac = crypto.createHmac('sha256', stringToSign);
    const signature = hmac.digest('base64');
    const xurl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/cd179988-4ce0-4004-88dc-16b15da3feb1';
    const data = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'text',
      content: {
        text: text,
      },
    };
    try {
      const response = await axios.post(xurl, data);
      return response.data;
    } catch (error) {
      // console.error(error);
    }
  }

  // 支持Markdown的飞书消息发送方法（简化版）
  async fsMarkdown(text, title = '通知') {
    // 先尝试发送富文本，如果失败则降级为普通文本
    try {
      return await this.fsRichText(text, title);
    } catch (error) {
      console.log('富文本发送失败，降级为普通文本...');
      return await this.fs(text);
    }
  }

  // 发送富文本消息
  async fsRichText(text, title = '通知') {
    const secret = 'MsrMWf48gWQilMkpJ9lcpe';
    const timestamp = Math.floor(Date.now() / 1000);
    const stringToSign = `${timestamp}\n${secret}`;
    const hmac = crypto.createHmac('sha256', stringToSign);
    const signature = hmac.digest('base64');
    const xurl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/8df456d9-de0c-4942-b48c-e4e65f77b80c';

    // 简单的富文本格式
    const content = [
      [
        {
          tag: 'text',
          text: text,
        },
      ],
    ];

    const data = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'post',
      content: {
        post: {
          zh_cn: {
            title: title,
            content: content,
          },
        },
      },
    };

    const response = await axios.post(xurl, data);
    console.log('飞书富文本消息发送成功:', response.data);

    // 检查响应是否成功
    if (response.data.code !== 0) {
      throw new Error(
        `飞书API错误: ${response.data.msg} (code: ${response.data.code})`,
      );
    }

    return response.data;
  }

  // 发送纯文本消息（带标题效果）
  async fsText(text, title = null) {
    const fullText = title ? `${title}\n\n${text}` : text;
    return await this.fs(fullText);
  }

  // 将Markdown转换为飞书富文本格式（简化版本）
  convertMarkdownToRichText(markdown) {
    // 简化处理：将整个markdown文本作为一个段落
    const lines = markdown.split('\n');
    const content = [];

    for (const line of lines) {
      if (line.trim() === '') {
        // 空行作为换行
        content.push([
          {
            tag: 'text',
            text: '',
          },
        ]);
        continue;
      }

      const lineParts = [];
      let remainingText = line;

      // 处理粗体 **text**
      const boldRegex = /\*\*(.*?)\*\*/g;
      let lastIndex = 0;
      let match;

      while ((match = boldRegex.exec(line)) !== null) {
        // 添加粗体前的文本
        if (match.index > lastIndex) {
          const beforeText = line.substring(lastIndex, match.index);
          if (beforeText) {
            lineParts.push({
              tag: 'text',
              text: beforeText,
            });
          }
        }

        // 添加粗体文本
        lineParts.push({
          tag: 'text',
          text: match[1],
          style: ['bold'],
        });

        lastIndex = match.index + match[0].length;
      }

      // 添加剩余文本
      if (lastIndex < line.length) {
        const remainingText = line.substring(lastIndex);
        if (remainingText) {
          lineParts.push({
            tag: 'text',
            text: remainingText,
          });
        }
      }

      // 如果没有特殊格式，直接添加整行
      if (lineParts.length === 0) {
        lineParts.push({
          tag: 'text',
          text: line,
        });
      }

      content.push(lineParts);
    }

    return content;
  }

  async getfsToken() {
    const url =
      'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal';
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    const data = new URLSearchParams();
    data.append('app_id', 'cli_a4d20850d43b100d');
    data.append('app_secret', 'UMrgkY6P5HRLPhFaSSQkehrkE1cp1uUB');
    try {
      const response = await axios.post(url, data, { headers });
      return response.data;
    } catch (error) {
      // console.error(error);
    }
  }

  async fsupload(filepath) {
    const { ctx } = this;
    const { tenant_access_token: tk } = await this.getfsToken();
    const url = 'https://open.feishu.cn/open-apis/im/v1/images';
    const form = new FormData();
    form.append('image_type', 'message');
    form.append('image', fs.createReadStream(filepath));
    try {
      const response = await axios.post(url, form, {
        headers: {
          Authorization: `Bearer ${tk}`,
          'Content-Type': `multipart/form-data; boundary=${form._boundary}`,
        },
      });
      await this.fsimg(response.data.data.image_key);
      return response.data;
    } catch (error) {
      // ctx.throw(error.response.status, error.response.data);
    }
  }

  async fsimg(text) {
    const secret = 'MsrMWf48gWQilMkpJ9lcpe';
    const timestamp = Math.floor(Date.now() / 1000);
    const stringToSign = `${timestamp}\n${secret}`;
    const hmac = crypto.createHmac('sha256', stringToSign);
    const signature = hmac.digest('base64');
    const xurl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/8df456d9-de0c-4942-b48c-e4e65f77b80c';
    const data = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'image',
      content: {
        image_key: text,
      },
    };
    try {
      const response = await axios.post(xurl, data);
      return response.data;
    } catch (error) {
      // console.error(error);
    }
  }

  // 发送丰富的部署成功信息
  async fsDeploySuccess(deployInfo = {}) {
    const {
      projectName = 'Vue项目',
      environment = 'production',
      version = 'latest',
      branch = 'main',
      commitHash = '',
      commitMessage = '',
      deployTime = new Date().toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
      }),
      deployDuration = '',
      deployedBy = 'GitHub Actions',
      serverUrl = '',
      buildSize = '',
      features = [],
      notes = '',
    } = deployInfo;

    // 构建丰富的部署成功消息
    const deployMessage = `🎉 部署成功通知

🚀 **项目信息**
📦 项目名称: ${projectName}
🌍 部署环境: ${environment}
🏷️ 版本标签: ${version}
🌿 分支名称: ${branch}

💻 **代码信息**
🔗 提交哈希: ${commitHash ? commitHash.substring(0, 8) : 'N/A'}
📝 提交信息: ${commitMessage || '无提交信息'}

⏰ **部署详情**
🕐 部署时间: ${deployTime}
⏱️ 部署耗时: ${deployDuration || '未知'}
👤 部署人员: ${deployedBy}
${serverUrl ? `🌐 访问地址: ${serverUrl}` : ''}
${buildSize ? `📊 构建大小: ${buildSize}` : ''}

${features.length > 0 ? `✨ **本次更新**\n${features.map((f) => `• ${f}`).join('\n')}\n` : ''}

${notes ? `📋 **备注说明**\n${notes}\n` : ''}

🎊 部署完成，服务已上线！`;

    try {
      // 尝试发送富文本消息
      return await this.fsRichText(deployMessage, '🚀 部署成功通知');
    } catch (error) {
      console.log('富文本部署消息发送失败，降级为普通文本...');
      // 降级为普通文本消息
      return await this.fs(deployMessage);
    }
  }

  // 发送部署失败信息
  async fsDeployFailure(deployInfo = {}) {
    const {
      projectName = 'Vue项目',
      environment = 'production',
      branch = 'main',
      commitHash = '',
      failureTime = new Date().toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
      }),
      errorMessage = '未知错误',
      deployedBy = 'GitHub Actions',
      logUrl = '',
      retryCount = 0,
    } = deployInfo;

    const failureMessage = `❌ 部署失败通知

🚨 **失败信息**
📦 项目名称: ${projectName}
🌍 部署环境: ${environment}
🌿 分支名称: ${branch}
🔗 提交哈希: ${commitHash ? commitHash.substring(0, 8) : 'N/A'}

⏰ **失败详情**
🕐 失败时间: ${failureTime}
👤 部署人员: ${deployedBy}
🔄 重试次数: ${retryCount}

💥 **错误信息**
${errorMessage}

${logUrl ? `📋 查看日志: ${logUrl}` : ''}

🔧 请检查错误信息并重新部署！`;

    try {
      return await this.fsRichText(failureMessage, '❌ 部署失败通知');
    } catch (error) {
      console.log('富文本部署失败消息发送失败，降级为普通文本...');
      return await this.fs(failureMessage);
    }
  }
}

module.exports = FeishuService;

const Service = require('egg').Service;
const cheerio = require('cheerio');
const axios = require('axios');
const iconv = require('iconv-lite');
var fs = require('fs');
var path = require('path');

class XrService extends Service {
  async select(biao, data1 = {}) {
    // 假如 我们拿到用户 id 从数据库获取用户详细信息
    const data = await this.app.mysql.select(biao, data1);
    return { data };
  }

  //查询一条记录
  async find(biao, data2) {
    // 假如 我们拿到用户 id 从数据库获取用户详细信息
    const data = await this.app.mysql.get(biao, data2);
    return { data };
  }

  async create(biao, data) {
    return await this.app.mysql.insert(biao, data);
  }

  async update(biao, data, options = {}) {
    return await this.app.mysql.update(biao, data, options);
  }

  async query(query, params = []) {
    return await this.app.mysql['query'](query, params);
  }

  async del(biao, data) {
    return await this.app.mysql.delete(biao, data);
  }

  async mkdirSync(dirname) {
    if (fs.existsSync(dirname)) {
      return true;
    } else {
      if (this.mkdirSync(path.dirname(dirname))) {
        fs.mkdirSync(dirname);
        return true;
      }
    }
    return false;
  }

  async down(id, page) {
    const ctx = this.ctx;
    let by = 0;
    for (let i = 1; i <= page; i++) {
      axios
        .get('http://127.0.0.1:7001/xr?type=XiuRen&id=' + id + '&page=' + i)
        .then(function (res) {
          // console.log(res.data);
          downloadUrl(res.data, by);
          by = by + 3;
        });
    }

    function mkdirSync(dirname) {
      if (fs.existsSync(dirname)) {
        return true;
      } else {
        if (mkdirSync(path.dirname(dirname))) {
          fs.mkdirSync(dirname);
          return true;
        }
      }
      return false;
    }
  }

  async gwy() {
    let url = 'http://gwykl.fujian.gov.cn/signupcount';
    let data = [];
    return await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);
      odata('tr').each(function (index, item) {
        let tr = odata('tbody').children('tr:eq(' + index + ')');
        let obj = {};
        tr.children('td').each(function (i, item) {
          let tdd = tr.children('td:eq(' + i + ')').text();
          let tda = tr.children('td:eq(' + i + ')').attr('href');
          if (i === 0) {
            obj.dw = tdd;
          }
          if (i === 1) {
            obj.zw = tdd;
          }
          if (i === 2) {
            obj.kslx = tdd;
          }
          if (i === 3) {
            obj.zkrs = tdd / 1;
          }
          if (i === 4) {
            obj.zbmrs = tdd / 1;
          }
          if (i === 5) {
            obj.shtg = tdd / 1;
          }
          if (i === 6) {
            obj.shbtg = tdd / 1;
          }
        });
        data.push(obj);
      });
      return data;
    });
  }

  async zzrcsydw() {
    let url = 'https://rcy.zzrcjt.com/exam/examUser/postStatisticsList?eiId=65';
    let res = await axios
      .post(url, '', {
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
      })
      .then(function (res) {
        // console.log(res.data);
        return res;
      });
    return res.data;
  }

  async zzrczkz() {
    let url = 'https://rcy.zzrcjt.com/exam/examUser/list';
    let data = {
      orderByColumn:
        'eiWritePrintStartTime is null, eiWritePrintStartTime, eiNo',
      isAsc: 'desc',
      'params[waitPrintDate]': 1,
    };
    let ret = '';
    for (let it in data) {
      ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
    }
    ret = ret.substring(0, ret.lastIndexOf('&'));
    let res = await axios
      .post(url, ret, {
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
      })
      .then(function (res) {
        // console.log(res.data);
        return res;
      });
    return res.data;
  }

  async lizhi(data) {
    let url = 'https://store.lizhi.io/site/getProduct/_paramKey_/_paramVal_';

    let ret = '';
    for (let it in data) {
      ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&';
    }
    ret = ret.substring(0, ret.lastIndexOf('&'));

    let res = await axios
      .post(url, ret, {
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
      })
      .then(function (res) {
        // console.log(res.data);
        return res;
      });
    return res.data;
  }

  async zzsydwweihu() {
    let url = 'https://zzksbm.com/';
    let data = [];
    let res = await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);
      return odata('h1').text();
    });
    return res;
  }
}

module.exports = XrService;

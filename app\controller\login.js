const Controller = require('egg').Controller;

class LoginController extends Controller {
  async login() {
    const { ctx, app } = this;
    const { username, password } = ctx.request.body;
    const configLogin = app.config.login;
    if (username === configLogin.username && password === configLogin.password) {
      ctx.session.isLogin = true;

      // 获取登录前用户想访问的URL
      const redirectUrl = ctx.session.redirectUrl || '/';
      // 清除保存的重定向URL
      ctx.session.redirectUrl = null;

      ctx.body = { success: true, redirectUrl: redirectUrl };
    } else {
      ctx.body = { success: false, message: '帐号或密码错误' };
    }
  }
  async logout() {
    const { ctx } = this;
    ctx.session.isLogin = false;
    ctx.redirect('/login');
  }
}

module.exports = LoginController;

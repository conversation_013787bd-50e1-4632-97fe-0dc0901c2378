'use strict';

/**
 * 🎨 画布实时传输 Socket.IO 处理器
 * 基于增量传输的高性能画布协作系统
 */

module.exports = (app) => {
  const { router, controller, io } = app;

  // 房间管理 - 存储每个房间的用户和画布状态
  const rooms = new Map(); // roomId -> { users: Set, canvasData: Object, lastUpdate: timestamp }
  const userRooms = new Map(); // socketId -> roomId

  // 监听画布命名空间连接
  io.of('/canvas').on('connection', async (socket) => {
    // console.log('🎨 画布客户端连接:', socket.id); // 减少日志输出

    // 发送连接成功消息
    socket.emit('connected', {
      message: '已连接到画布实时传输服务',
      socketId: socket.id,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    });

    // 🏠 加入房间
    socket.on('join_room', async (data) => {
      const { roomId, userId, userInfo } = data;
      // console.log(`👤 用户 ${userId} 加入房间 ${roomId}`); // 减少日志输出

      // 离开之前的房间
      const previousRoom = userRooms.get(socket.id);
      if (previousRoom) {
        await leaveRoom(socket, previousRoom);
      }

      // 加入新房间
      socket.join(roomId);
      userRooms.set(socket.id, roomId);

      // 初始化房间数据
      if (!rooms.has(roomId)) {
        rooms.set(roomId, {
          users: new Set(),
          canvasData: null,
          lastUpdate: Date.now(),
          metadata: {
            created: Date.now(),
            totalStrokes: 0,
            activeUsers: 0,
          },
        });
      }

      const room = rooms.get(roomId);
      room.users.add({
        socketId: socket.id,
        userId: userId,
        userInfo: userInfo || {},
        joinTime: Date.now(),
      });
      room.metadata.activeUsers = room.users.size;

      // 通知房间内其他用户
      socket.to(roomId).emit('user_joined', {
        userId: userId,
        userInfo: userInfo,
        timestamp: Date.now(),
        activeUsers: room.metadata.activeUsers,
      });

      // 发送当前画布数据给新用户
      if (room.canvasData) {
        socket.emit('full_sync', {
          canvasData: room.canvasData,
          metadata: room.metadata,
          timestamp: Date.now(),
        });
      }

      // 确认加入成功
      socket.emit('room_joined', {
        roomId: roomId,
        activeUsers: room.metadata.activeUsers,
        hasData: !!room.canvasData,
        timestamp: Date.now(),
      });
    });

    // 🎨 接收画布增量更新
    socket.on('canvas_update', async (data) => {
      const roomId = userRooms.get(socket.id);
      if (!roomId) {
        socket.emit('error', { message: '未加入任何房间' });
        return;
      }

      const room = rooms.get(roomId);
      if (!room) {
        socket.emit('error', { message: '房间不存在' });
        return;
      }

      try {
        // 验证数据格式
        if (!data.strokes || !Array.isArray(data.strokes)) {
          throw new Error('无效的笔画数据格式');
        }

        // 更新房间画布数据
        if (!room.canvasData) {
          room.canvasData = {
            version: 4,
            strokes: [],
            metadata: {
              created: Date.now(),
              lastModified: Date.now(),
            },
          };
        }

        // 添加新笔画
        room.canvasData.strokes.push(...data.strokes);
        room.canvasData.metadata.lastModified = Date.now();
        room.lastUpdate = Date.now();
        room.metadata.totalStrokes = room.canvasData.strokes.length;

        // 广播给房间内其他用户
        socket.to(roomId).emit('canvas_update', {
          strokes: data.strokes,
          fromUser: data.userId || socket.id,
          timestamp: Date.now(),
          totalStrokes: room.metadata.totalStrokes,
        });

        // 异步保存到数据库
        setImmediate(async () => {
          try {
            await saveCanvasToDatabase(roomId, room.canvasData);
          } catch (error) {
            // console.error('保存画布数据失败:', error); // 减少日志输出
          }
        });

        // 确认更新成功
        socket.emit('update_confirmed', {
          timestamp: Date.now(),
          strokeCount: data.strokes.length,
        });
      } catch (error) {
        console.error('处理画布更新失败:', error);
        socket.emit('error', {
          message: '处理画布更新失败',
          error: error.message,
        });
      }
    });

    // 🗑️ 接收擦除操作
    socket.on('canvas_erase', async (data) => {
      const roomId = userRooms.get(socket.id);
      if (!roomId) return;

      const room = rooms.get(roomId);
      if (!room || !room.canvasData) return;

      try {
        // 处理擦除逻辑
        const { eraseArea, eraseType } = data;

        // 广播擦除操作
        socket.to(roomId).emit('canvas_erase', {
          eraseArea: eraseArea,
          eraseType: eraseType,
          fromUser: data.userId || socket.id,
          timestamp: Date.now(),
        });

        room.lastUpdate = Date.now();
      } catch (error) {
        console.error('处理擦除操作失败:', error);
        socket.emit('error', { message: '处理擦除操作失败' });
      }
    });

    // 🔄 请求完整同步
    socket.on('request_full_sync', async () => {
      const roomId = userRooms.get(socket.id);
      if (!roomId) return;

      const room = rooms.get(roomId);
      if (!room) return;

      socket.emit('full_sync', {
        canvasData: room.canvasData,
        metadata: room.metadata,
        timestamp: Date.now(),
      });
    });

    // 💓 心跳检测
    socket.on('ping', (data) => {
      socket.emit('pong', {
        timestamp: Date.now(),
        serverTime: new Date().toISOString(),
        ...data,
      });
    });

    // 📊 获取房间状态
    socket.on('get_room_status', () => {
      const roomId = userRooms.get(socket.id);
      if (!roomId) return;

      const room = rooms.get(roomId);
      if (!room) return;

      socket.emit('room_status', {
        roomId: roomId,
        activeUsers: room.metadata.activeUsers,
        totalStrokes: room.metadata.totalStrokes,
        lastUpdate: room.lastUpdate,
        hasData: !!room.canvasData,
        timestamp: Date.now(),
      });
    });

    // 🚪 断开连接处理
    socket.on('disconnect', async () => {
      console.log('❌ 画布客户端断开连接:', socket.id);

      const roomId = userRooms.get(socket.id);
      if (roomId) {
        await leaveRoom(socket, roomId);
      }
    });

    // 🚪 主动离开房间
    socket.on('leave_room', async () => {
      const roomId = userRooms.get(socket.id);
      if (roomId) {
        await leaveRoom(socket, roomId);
      }
    });
  });

  // 🚪 离开房间的通用处理函数
  async function leaveRoom(socket, roomId) {
    try {
      socket.leave(roomId);
      userRooms.delete(socket.id);

      const room = rooms.get(roomId);
      if (room) {
        // 移除用户
        room.users = new Set([...room.users].filter((user) => user.socketId !== socket.id));
        room.metadata.activeUsers = room.users.size;

        // 通知房间内其他用户
        socket.to(roomId).emit('user_left', {
          socketId: socket.id,
          activeUsers: room.metadata.activeUsers,
          timestamp: Date.now(),
        });

        // 如果房间空了，清理房间数据（可选）
        if (room.users.size === 0) {
          // 可以选择立即清理或延迟清理
          setTimeout(
            () => {
              if (rooms.has(roomId) && rooms.get(roomId).users.size === 0) {
                console.log(`🗑️ 清理空房间: ${roomId}`);
                rooms.delete(roomId);
              }
            },
            5 * 60 * 1000,
          ); // 5分钟后清理空房间
        }
      }
    } catch (error) {
      console.error('离开房间失败:', error);
    }
  }

  // 💾 保存画布数据到数据库
  async function saveCanvasToDatabase(roomId, canvasData) {
    try {
      // 这里集成到现有的数据库保存逻辑
      // 假设roomId就是题目ID
      const compressedData = JSON.stringify(canvasData);

      // 使用现有的数据库连接，根据表结构优化字段
      const strokesArray = canvasData.s || canvasData.strokes || [];

      // 构建更新数据，确保字段类型正确
      const updateData = {
        canvas_data: compressedData, // longtext 类型
        canvas_width: parseInt(canvasData.metadata?.width) || null, // int(11) 类型，可为null
        canvas_height: parseInt(canvasData.metadata?.height) || null, // int(11) 类型，可为null
        has_canvas: strokesArray.length > 0 ? 1 : 0, // tinyint(1) 类型，默认0
        canvas_updated_time: new Date(), // timestamp 类型
      };

      // 检查记录是否存在
      const existingRecord = await app.mysql.get('fbsy', { id: roomId });

      if (existingRecord) {
        // 记录存在，执行更新
        await app.mysql.update('fbsy', updateData, {
          where: { id: roomId },
        });
        // console.log(`💾 画布数据已更新到数据库: ${roomId}`); // 减少日志输出
      } else {
        // 记录不存在，跳过保存（题目记录应该已存在）
        console.warn(`⚠️ 题目记录不存在，跳过画布数据保存: ${roomId}`);
      }
    } catch (error) {
      console.error('保存画布数据到数据库失败:', error);
      throw error;
    }
  }

  // 📊 定期清理和统计
  setInterval(
    () => {
      const totalRooms = rooms.size;
      const totalUsers = Array.from(rooms.values()).reduce((sum, room) => sum + room.users.size, 0);

      if (totalRooms > 0) {
        console.log(`📊 画布服务状态: ${totalRooms} 个房间, ${totalUsers} 个活跃用户`);
      }
    },
    5 * 60 * 1000,
  ); // 每5分钟统计一次
};

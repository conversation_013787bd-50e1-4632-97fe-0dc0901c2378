'use strict';

const Service = require('egg').Service;
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class DeploymentService extends Service {
  
  /**
   * 执行自动部署
   */
  async executeDeployment(webhookData) {
    const { ctx } = this;
    const startTime = Date.now();
    
    try {
      ctx.logger.info('开始执行自动部署...');
      
      // 1. 执行部署脚本
      const deployResult = await this.runDeploymentScript();
      
      // 2. 计算部署时间
      const deployTime = Math.round((Date.now() - startTime) / 1000);
      
      // 3. 发送成功通知
      await this.sendSuccessNotification(webhookData, deployResult, deployTime);
      
      ctx.logger.info('自动部署完成', { deployTime, result: deployResult });
      
    } catch (error) {
      ctx.logger.error('自动部署失败:', error);
      
      // 发送失败通知
      await this.sendFailureNotification(webhookData, error);
      
      throw error;
    }
  }
  
  /**
   * 运行部署脚本
   */
  async runDeploymentScript() {
    const { ctx } = this;
    const projectPath = '/home/<USER>';
    const scriptPath = path.join(projectPath, 'update.sh');
    
    // 检查脚本是否存在
    if (!fs.existsSync(scriptPath)) {
      throw new Error(`部署脚本不存在: ${scriptPath}`);
    }
    
    // 确保脚本有执行权限
    try {
      fs.chmodSync(scriptPath, '755');
    } catch (error) {
      ctx.logger.warn('设置脚本权限失败:', error.message);
    }
    
    return new Promise((resolve, reject) => {
      const process = spawn('bash', [scriptPath], {
        cwd: projectPath,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, NODE_ENV: 'production' }
      });
      
      let stdout = '';
      let stderr = '';
      
      process.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        ctx.logger.info('部署输出:', output.trim());
      });
      
      process.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        ctx.logger.warn('部署警告:', output.trim());
      });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve({
            success: true,
            code,
            stdout: stdout.trim(),
            stderr: stderr.trim()
          });
        } else {
          reject(new Error(`部署脚本执行失败，退出码: ${code}\n错误输出: ${stderr}`));
        }
      });
      
      process.on('error', (error) => {
        reject(new Error(`部署脚本启动失败: ${error.message}`));
      });
      
      // 设置超时（10分钟）
      setTimeout(() => {
        process.kill('SIGTERM');
        reject(new Error('部署超时（10分钟）'));
      }, 10 * 60 * 1000);
    });
  }
  
  /**
   * 发送成功通知
   */
  async sendSuccessNotification(webhookData, deployResult, deployTime) {
    const { ctx } = this;
    
    const commits = webhookData.commits || [];
    const commitMessages = commits.slice(0, 3).map(commit => 
      `• ${commit.message.split('\n')[0]} (${commit.author.name})`
    ).join('\n');
    
    const message = `✅ 自动部署成功

仓库: ${webhookData.repository.full_name}
分支: ${webhookData.ref.replace('refs/heads/', '')}
提交数: ${commits.length}
部署时间: ${deployTime}秒
完成时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}

最近提交:
${commitMessages}

🚀 应用已重启并正常运行`;

    try {
      await ctx.service.feishu.fsText(message, '✅ 部署成功');
    } catch (error) {
      ctx.logger.error('发送成功通知失败:', error);
    }
  }
  
  /**
   * 发送失败通知
   */
  async sendFailureNotification(webhookData, error) {
    const { ctx } = this;
    
    const message = `❌ 自动部署失败

仓库: ${webhookData.repository.full_name}
分支: ${webhookData.ref.replace('refs/heads/', '')}
失败时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}

错误信息:
${error.message}

🔧 请检查:
1. 服务器磁盘空间
2. 网络连接状态
3. 依赖安装情况
4. 应用配置文件`;

    try {
      await ctx.service.feishu.fsText(message, '❌ 部署失败');
    } catch (notifyError) {
      ctx.logger.error('发送失败通知失败:', notifyError);
    }
  }
  
  /**
   * 验证GitHub签名（可选安全措施）
   */
  verifyGitHubSignature(payload, signature, secret) {
    const crypto = require('crypto');
    const expectedSignature = 'sha256=' + crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }
  
  /**
   * 获取部署状态
   */
  async getDeploymentStatus() {
    const { ctx } = this;
    
    try {
      // 检查应用是否运行
      const healthCheck = await ctx.curl('http://localhost:7001/health', {
        timeout: 5000,
        dataType: 'json'
      });
      
      return {
        status: 'running',
        healthy: healthCheck.status === 200,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        status: 'error',
        healthy: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = DeploymentService;

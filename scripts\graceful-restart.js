#!/usr/bin/env node

/**
 * 优雅重启脚本
 * 实现零停机部署和平滑重启功能
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

// 重启配置
const RESTART_CONFIG = {
  gracefulTimeout: 30000, // 30秒优雅关闭超时
  healthCheckInterval: 2000, // 2秒健康检查间隔
  maxHealthCheckAttempts: 15, // 最多15次健康检查
  pidFile: path.join(__dirname, '..', 'run', 'app.pid'),
  logFile: path.join(__dirname, '..', 'logs', 'restart.log'),
};

/**
 * 日志记录
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  
  console.log(logMessage);
  
  // 写入日志文件
  const logDir = path.dirname(RESTART_CONFIG.logFile);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  fs.appendFileSync(RESTART_CONFIG.logFile, logMessage + '\n');
}

/**
 * 检查应用是否运行
 */
function checkAppRunning(pid) {
  try {
    process.kill(pid, 0); // 发送信号0检查进程是否存在
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 获取当前运行的应用PID
 */
function getCurrentPid() {
  if (!fs.existsSync(RESTART_CONFIG.pidFile)) {
    return null;
  }
  
  try {
    const pid = parseInt(fs.readFileSync(RESTART_CONFIG.pidFile, 'utf8').trim());
    return checkAppRunning(pid) ? pid : null;
  } catch (error) {
    log(`读取PID文件失败: ${error.message}`, 'ERROR');
    return null;
  }
}

/**
 * 健康检查
 */
function healthCheck(port = 7001) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: '127.0.0.1',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 3000,
    }, (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => resolve(false));
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 等待应用健康
 */
async function waitForHealthy(port = 7001) {
  log('等待应用启动并通过健康检查...');
  
  for (let i = 0; i < RESTART_CONFIG.maxHealthCheckAttempts; i++) {
    const isHealthy = await healthCheck(port);
    
    if (isHealthy) {
      log('✅ 应用健康检查通过');
      return true;
    }
    
    log(`健康检查失败，重试 ${i + 1}/${RESTART_CONFIG.maxHealthCheckAttempts}`);
    await new Promise(resolve => setTimeout(resolve, RESTART_CONFIG.healthCheckInterval));
  }
  
  log('❌ 应用健康检查超时失败', 'ERROR');
  return false;
}

/**
 * 优雅停止应用
 */
async function gracefulStop(pid) {
  if (!pid) {
    log('没有找到运行中的应用进程');
    return true;
  }
  
  log(`开始优雅停止应用进程 ${pid}...`);
  
  try {
    // 发送SIGTERM信号
    process.kill(pid, 'SIGTERM');
    log('已发送SIGTERM信号');
    
    // 等待进程优雅退出
    const startTime = Date.now();
    while (checkAppRunning(pid) && (Date.now() - startTime) < RESTART_CONFIG.gracefulTimeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      log('等待进程优雅退出...');
    }
    
    // 检查进程是否已退出
    if (!checkAppRunning(pid)) {
      log('✅ 应用已优雅退出');
      return true;
    }
    
    // 如果还在运行，发送SIGKILL强制终止
    log('优雅退出超时，强制终止进程...', 'WARN');
    process.kill(pid, 'SIGKILL');
    
    // 再次检查
    await new Promise(resolve => setTimeout(resolve, 2000));
    if (!checkAppRunning(pid)) {
      log('✅ 应用已强制终止');
      return true;
    }
    
    log('❌ 无法停止应用进程', 'ERROR');
    return false;
  } catch (error) {
    log(`停止应用进程失败: ${error.message}`, 'ERROR');
    return false;
  }
}

/**
 * 启动应用
 */
function startApp() {
  return new Promise((resolve, reject) => {
    log('启动新的应用实例...');
    
    const nodeEnv = process.env.NODE_ENV || 'production';
    const startScript = nodeEnv === 'production' ? 'start:optimized' : 'dev:optimized';
    
    const child = spawn('pnpm', ['run', startScript], {
      cwd: path.join(__dirname, '..'),
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe'],
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    child.on('error', (error) => {
      log(`启动应用失败: ${error.message}`, 'ERROR');
      reject(error);
    });
    
    // 等待一段时间让应用启动
    setTimeout(() => {
      if (child.pid) {
        log(`应用启动中，PID: ${child.pid}`);
        
        // 保存PID到文件
        fs.writeFileSync(RESTART_CONFIG.pidFile, child.pid.toString());
        
        // 分离子进程
        child.unref();
        
        resolve(child.pid);
      } else {
        log('应用启动失败', 'ERROR');
        if (errorOutput) {
          log(`错误输出: ${errorOutput}`, 'ERROR');
        }
        reject(new Error('Failed to start application'));
      }
    }, 5000);
  });
}

/**
 * 执行优雅重启
 */
async function performGracefulRestart() {
  log('🔄 开始优雅重启流程...');
  
  try {
    // 1. 获取当前运行的PID
    const currentPid = getCurrentPid();
    if (currentPid) {
      log(`发现运行中的应用进程: ${currentPid}`);
    }
    
    // 2. 启动新实例
    const newPid = await startApp();
    
    // 3. 等待新实例健康
    const isHealthy = await waitForHealthy();
    
    if (!isHealthy) {
      log('新实例健康检查失败，回滚操作...', 'ERROR');
      
      // 停止新实例
      if (checkAppRunning(newPid)) {
        await gracefulStop(newPid);
      }
      
      throw new Error('New instance health check failed');
    }
    
    // 4. 停止旧实例
    if (currentPid && currentPid !== newPid) {
      const stopped = await gracefulStop(currentPid);
      if (!stopped) {
        log('停止旧实例失败，但新实例已正常运行', 'WARN');
      }
    }
    
    log('✅ 优雅重启完成');
    return true;
    
  } catch (error) {
    log(`优雅重启失败: ${error.message}`, 'ERROR');
    return false;
  }
}

/**
 * 执行简单重启
 */
async function performSimpleRestart() {
  log('🔄 开始简单重启流程...');
  
  try {
    // 1. 停止当前应用
    const currentPid = getCurrentPid();
    if (currentPid) {
      const stopped = await gracefulStop(currentPid);
      if (!stopped) {
        throw new Error('Failed to stop current application');
      }
    }
    
    // 2. 启动新应用
    await startApp();
    
    // 3. 健康检查
    const isHealthy = await waitForHealthy();
    
    if (!isHealthy) {
      throw new Error('Application health check failed after restart');
    }
    
    log('✅ 简单重启完成');
    return true;
    
  } catch (error) {
    log(`简单重启失败: ${error.message}`, 'ERROR');
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'graceful';
  
  log(`开始执行 ${mode} 重启模式`);
  
  let success = false;
  
  if (mode === 'graceful') {
    success = await performGracefulRestart();
  } else if (mode === 'simple') {
    success = await performSimpleRestart();
  } else {
    log(`未知的重启模式: ${mode}`, 'ERROR');
    log('支持的模式: graceful, simple');
    process.exit(1);
  }
  
  process.exit(success ? 0 : 1);
}

// 如果直接运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  performGracefulRestart,
  performSimpleRestart,
  gracefulStop,
  startApp,
  healthCheck,
  waitForHealthy,
};

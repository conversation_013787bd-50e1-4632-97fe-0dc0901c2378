<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Document</title>
</head>
<body>
<div id="app">
    <el-alert
            title="还有{{total}}题没做"
            type="warning">
    </el-alert>

    <div class="content">
        <div style="--color-background: red" v-html="question.content"></div>
        <el-button @click="finishqs(question.sid)" style="margin-bottom: 10px">
            <label>做完了</label>
        </el-button>
        <el-button @click="refreshPage" style="margin-bottom: 10px">
            <label>刷新</label>
        </el-button>
        <el-alert
                v-if="showAlert"
                title="点击成功"
                type="success">
        </el-alert>
        <div>
            <div v-for="(answer, index) in this.options">
                <el-button
                        :key="answer.value"
                        :value="answer.value"
                        style="margin-bottom: 10px;margin-top: 10px;width: 100% "

                >
                    <!--          <input type="radio" :id="answer" />-->
                    <el-link :href="answer.href" target="_blank">

                        <label v-text="answer.value" style="width: 100%;overflow: auto;
   white-space: normal;
   word-wrap: break-word;"></label>
                    </el-link>

                </el-button>

            </div>

        </div>
        <div v-if="showAnalysis">
            <div>{{"答案是"}}<p v-text="question.referenceAnswer"></p></div>
            <br>
            <div v-html="question.referenceAnalysis"></div>
        </div>

    </div>
</div>
</body>
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                question: "",
                selectedAnswer: '',
                showAnalysis: false,
                showAlert: false,
                showStyle: "",
                value: "",
                options: []
            };
        },
        created() {

            this.getnav()
        },
        mounted() {
        },
        methods: {
            refreshPage() {
                location.reload();
            },
            getnav() {
                let url = '/cardalljson?nav=1';
                if ('{{o}}' === '1') {
                    url = '/cardalljson?o=1'
                }
                axios.get(url)
                    .then(response => {
                        this.options = response.data;
                        for (let item of response.data) {
                            item.href = `/cardall?s=${item.value}`
                            if ('{{o}}' === '1') {
                                item.href = `/mintimu?f=${item.value}&per=1&page=1`
                            }
                        }
                    })
                    .catch(error => {
                        console.error(error);
                    });
            },

            finishqs(data) {
                console.log(data);
                axios.post('/minupdate', {
                    id: data
                }).then(result => {
                    // console.log(result.data.qwmQuestionList.length);
                    this.showAlert = true;
                })
            },
            checkAnswer(data) {
                this.showAnalysis = true;
                let ans = "";
                if (data === 0) {
                    ans = "A";
                }
                if (data === 1) {
                    ans = "B";
                }
                if (data === 2) {
                    ans = "C";
                }
                if (data === 3) {
                    ans = "D";
                }

                console.log(typeof ans)
                console.log(ans)
                console.log(typeof this.question.referenceAnswer)
                console.log(this.question.referenceAnswer)

                if (ans === this.question.referenceAnswer) {
                    this.showStyle = "success"
                } else {
                    this.showStyle = "danger"
                }

                // console.log(data)
            },
        }
    })


</script>
<style>
    body {
        font-size: 22px;
    }

    @media screen and (min-width: 1900px) {
        .content {
            margin: 0 auto;
            width: 960px;
        }

    }

    @media screen and (min-width: 1000px) {
        .content {
            margin: 0 auto;
            width: 960px;
        }
    }

    button {
        font-size: 22px;
    }

    * {
        font-size: 22px;
    }

</style>
</html>

#!/bin/bash

# 设置日志管理的 cron 任务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_MANAGER_SCRIPT="$SCRIPT_DIR/log-manager.sh"

echo "设置 Egg.js 日志管理 cron 任务..."

# 检查脚本是否存在
if [ ! -f "$LOG_MANAGER_SCRIPT" ]; then
    echo "错误: 日志管理脚本不存在: $LOG_MANAGER_SCRIPT"
    exit 1
fi

# 给脚本执行权限
chmod +x "$LOG_MANAGER_SCRIPT"

# 创建 cron 任务
CRON_JOBS="
# Egg.js 日志管理任务
# 每天凌晨2点清理旧日志
0 2 * * * $LOG_MANAGER_SCRIPT clean >/dev/null 2>&1

# 每天凌晨3点压缩大日志文件
0 3 * * * $LOG_MANAGER_SCRIPT compress >/dev/null 2>&1

# 每周日凌晨4点执行完整清理
0 4 * * 0 $LOG_MANAGER_SCRIPT all >/dev/null 2>&1
"

# 添加到当前用户的 crontab
echo "添加 cron 任务到当前用户..."
(crontab -l 2>/dev/null; echo "$CRON_JOBS") | crontab -

echo "✅ Cron 任务设置完成!"
echo ""
echo "已添加的任务:"
echo "- 每天 02:00 清理旧日志"
echo "- 每天 03:00 压缩大文件"
echo "- 每周日 04:00 完整清理"
echo ""
echo "查看当前 cron 任务: crontab -l"
echo "手动执行清理: $LOG_MANAGER_SCRIPT all"

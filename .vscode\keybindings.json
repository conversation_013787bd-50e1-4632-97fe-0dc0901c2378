[
  // ==================== WebStorm/IntelliJ IDEA 快捷键映射 ====================
  
  // 代码导航
  {
    "key": "ctrl+b",
    "command": "editor.action.revealDefinition",
    "when": "editorHasDefinitionProvider && editorTextFocus && !isInEmbeddedEditor"
  },
  {
    "key": "ctrl+alt+b",
    "command": "editor.action.goToImplementation",
    "when": "editorHasImplementationProvider && editorTextFocus && !isInEmbeddedEditor"
  },
  {
    "key": "ctrl+u",
    "command": "editor.action.goToDeclaration",
    "when": "editorHasDeclarationProvider && editorTextFocus && !isInEmbeddedEditor"
  },
  {
    "key": "ctrl+alt+left",
    "command": "workbench.action.navigateBack"
  },
  {
    "key": "ctrl+alt+right",
    "command": "workbench.action.navigateForward"
  },
  {
    "key": "ctrl+e",
    "command": "workbench.action.quickOpen"
  },
  {
    "key": "ctrl+shift+e",
    "command": "workbench.action.quickOpenNavigateNext",
    "when": "inQuickOpen"
  },
  
  // 搜索和替换
  {
    "key": "ctrl+shift+f",
    "command": "workbench.action.findInFiles"
  },
  {
    "key": "ctrl+shift+r",
    "command": "workbench.action.replaceInFiles"
  },
  {
    "key": "ctrl+f",
    "command": "actions.find"
  },
  {
    "key": "ctrl+r",
    "command": "editor.action.startFindReplaceAction"
  },
  {
    "key": "f3",
    "command": "editor.action.nextMatchFindAction",
    "when": "editorFocus"
  },
  {
    "key": "shift+f3",
    "command": "editor.action.previousMatchFindAction",
    "when": "editorFocus"
  },
  
  // 代码编辑
  {
    "key": "ctrl+d",
    "command": "editor.action.duplicateSelection"
  },
  {
    "key": "ctrl+y",
    "command": "editor.action.deleteLines",
    "when": "textInputFocus && !editorReadonly"
  },
  {
    "key": "ctrl+shift+up",
    "command": "editor.action.moveLinesUpAction",
    "when": "editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+shift+down",
    "command": "editor.action.moveLinesDownAction",
    "when": "editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+shift+j",
    "command": "editor.action.joinLines",
    "when": "editorTextFocus && !editorReadonly"
  },
  
  // 代码折叠
  {
    "key": "ctrl+numpad_add",
    "command": "editor.unfold",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+numpad_subtract",
    "command": "editor.fold",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+shift+numpad_add",
    "command": "editor.unfoldAll",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+shift+numpad_subtract",
    "command": "editor.foldAll",
    "when": "editorTextFocus"
  },
  
  // 重构
  {
    "key": "shift+f6",
    "command": "editor.action.rename",
    "when": "editorHasRenameProvider && editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+alt+v",
    "command": "editor.action.refactor",
    "when": "editorHasCodeActionsProvider && editorTextFocus && !editorReadonly"
  },
  {
    "key": "alt+enter",
    "command": "editor.action.quickFix",
    "when": "editorHasCodeActionsProvider && editorTextFocus && !editorReadonly"
  },
  
  // 代码格式化
  {
    "key": "ctrl+alt+l",
    "command": "editor.action.formatDocument",
    "when": "editorHasDocumentFormattingProvider && editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+alt+i",
    "command": "editor.action.formatSelection",
    "when": "editorHasDocumentSelectionFormattingProvider && editorTextFocus && !editorReadonly"
  },
  
  // 注释
  {
    "key": "ctrl+slash",
    "command": "editor.action.commentLine",
    "when": "editorTextFocus && !editorReadonly"
  },
  {
    "key": "ctrl+shift+slash",
    "command": "editor.action.blockComment",
    "when": "editorTextFocus && !editorReadonly"
  },
  
  // 选择
  {
    "key": "ctrl+w",
    "command": "editor.action.smartSelect.expand",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+shift+w",
    "command": "editor.action.smartSelect.shrink",
    "when": "editorTextFocus"
  },
  {
    "key": "ctrl+a",
    "command": "editor.action.selectAll"
  },
  
  // 运行和调试
  {
    "key": "shift+f10",
    "command": "workbench.action.debug.run",
    "when": "debuggersAvailable"
  },
  {
    "key": "shift+f9",
    "command": "workbench.action.debug.start",
    "when": "debuggersAvailable"
  },
  {
    "key": "f8",
    "command": "workbench.action.debug.continue",
    "when": "inDebugMode"
  },
  {
    "key": "f7",
    "command": "workbench.action.debug.stepInto",
    "when": "inDebugMode"
  },
  {
    "key": "shift+f8",
    "command": "workbench.action.debug.stepOut",
    "when": "inDebugMode"
  },
  {
    "key": "f9",
    "command": "editor.debug.action.toggleBreakpoint",
    "when": "editorTextFocus"
  },
  
  // 窗口管理
  {
    "key": "alt+1",
    "command": "workbench.view.explorer"
  },
  {
    "key": "alt+2",
    "command": "workbench.view.search"
  },
  {
    "key": "alt+3",
    "command": "workbench.view.scm"
  },
  {
    "key": "alt+4",
    "command": "workbench.view.debug"
  },
  {
    "key": "alt+5",
    "command": "workbench.view.extensions"
  },
  {
    "key": "escape",
    "command": "workbench.action.hidePanel",
    "when": "panelFocus"
  },
  
  // 终端
  {
    "key": "alt+f12",
    "command": "workbench.action.terminal.toggleTerminal"
  },
  {
    "key": "ctrl+shift+f12",
    "command": "workbench.action.terminal.new"
  },
  
  // Git 操作
  {
    "key": "ctrl+k",
    "command": "git.commitAll",
    "when": "!inDebugMode"
  },
  {
    "key": "ctrl+shift+k",
    "command": "git.push",
    "when": "!inDebugMode"
  },
  
  // 文件操作
  {
    "key": "ctrl+alt+s",
    "command": "workbench.action.files.saveAll"
  },
  {
    "key": "ctrl+shift+n",
    "command": "workbench.action.files.newUntitledFile"
  },
  {
    "key": "ctrl+shift+alt+n",
    "command": "explorer.newFile"
  }
]

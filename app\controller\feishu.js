const axios = require('axios');
const { dateNow } = require('../extend/helper');
const Controller = require('egg').Controller;

class FeishuController extends Controller {
  async fs() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    let date = ctx.query.date || 0;
    +date === 1 ? (text = text + `\n` + dateNow()) : text;
    ctx.body = await ctx.service.feishu['fs'](text);
  }
  async fs3() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    let date = ctx.query.date || 0;
    +date === 1 ? (text = text + `\n` + dateNow()) : text;
    ctx.body = await ctx.service.feishu['fs3'](text);
  }

  async fsimg() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.feishu['fsimg'](text);
  }

  async fstoken() {
    const ctx = this.ctx;
    ctx.body = await ctx.service.feishu['getfsToken']();
  }

  async fsupload() {
    const { ctx } = this;
    const file = ctx.request.files[0];
    // console.log('fs', ctx.request);
    // console.log('fs', ctx.request.files);
    ctx.body = await ctx.service.feishu['fsupload'](file.filepath);
  }

  async fsdc() {
    const ctx = this.ctx;
    for (let i = 1; i <= 6; i++) {
      let text = i + '抢到动车票了';
      await ctx.service.feishu['fs'](text);
    }
    ctx.body = '抢到动车票了';
  }

  async fsproxy() {
    const { ctx, app } = this;
    // console.log(ctx.request.body);
    // console.log(ctx.request.body.data.changes);
    let info = ctx.request.body;
    const xurl =
      'https://open.feishu.cn/open-apis/bot/v2/hook/cd179988-4ce0-4004-88dc-16b15da3feb1';
    let data = {
      msg_type: 'post',
      content: {
        post: {
          zh_cn: {
            title: info.data.title,
            content: [
              [
                {
                  tag: 'text',
                  text: info.data.text,
                },
                {
                  tag: 'a',
                  text: info.data.text,
                  href: info.data.url,
                },
              ],
            ],
          },
        },
      },
    };
    try {
      const response = await axios.post(xurl, data);
      console.log(response.data);
      return response.data;
    } catch (error) {
      // console.error(error);
    }
  }
}

module.exports = FeishuController;

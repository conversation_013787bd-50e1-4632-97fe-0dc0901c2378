const dayjs = require('dayjs');
const axios = require('axios');

module.exports = {
  async allair() {
    const { ctx, app } = this;
    let airportToCity = [
      { city: '北京', code: 'BJS' },
      { city: '上海', code: 'SHA' },
      {
        city: '广州',
        code: 'CAN',
      },
      { city: '深圳', code: 'SZX' },
      { city: '成都', code: 'CTU' },
      {
        city: '杭州',
        code: 'HGH',
      },
      { city: '武汉', code: 'WUH' },
      { city: '西安', code: 'SIA' },
      {
        city: '重庆',
        code: 'CKG',
      },
      { city: '青岛', code: 'TAO' },
      { city: '长沙', code: 'CSX' },
      {
        city: '南京',
        code: 'NKG',
      },
      { city: '厦门', code: 'XMN' },
      { city: '昆明', code: 'KMG' },
      {
        city: '大连',
        code: 'DLC',
      },
      { city: '天津', code: 'TSN' },
      { city: '郑州', code: 'CGO' },
      {
        city: '三亚',
        code: 'SYX',
      },
      { city: '济南', code: 'TNA' },
      { city: '福州', code: 'FOC' },
      {
        city: '阿勒泰',
        code: 'AAT',
      },
      { city: '阿克苏', code: 'AKU' },
      { city: '鞍山', code: 'AOG' },
      {
        city: '安庆',
        code: 'AQG',
      },
      { city: '安顺', code: 'AVA' },
      { city: '阿拉善左旗', code: 'AXF' },
      {
        city: '中国澳门',
        code: 'MFM',
      },
      { city: '阿里', code: 'NGQ' },
      { city: '阿拉善右旗', code: 'RHT' },
      {
        city: '阿尔山',
        code: 'YIE',
      },
      { city: '巴中', code: 'BZX' },
      { city: '百色', code: 'AEB' },
      {
        city: '包头',
        code: 'BAV',
      },
      { city: '毕节', code: 'BFJ' },
      { city: '北海', code: 'BHY' },
      {
        city: '北京(大兴国际机场)',
        code: 'BJS,PKX',
      },
      { city: '北京(首都国际机场)', code: 'BJS,PEK' },
      { city: '博乐', code: 'BPL' },
      {
        city: '保山',
        code: 'BSD',
      },
      { city: '白城', code: 'DBC' },
      { city: '布尔津', code: 'KJI' },
      {
        city: '白山',
        code: 'NBS',
      },
      { city: '巴彦淖尔', code: 'RLK' },
      { city: '昌都', code: 'BPX' },
      {
        city: '承德',
        code: 'CDE',
      },
      { city: '常德', code: 'CGD' },
      { city: '长春', code: 'CGQ' },
      {
        city: '朝阳',
        code: 'CHG',
      },
      { city: '赤峰', code: 'CIF' },
      { city: '长治', code: 'CIH' },
      {
        city: '沧源',
        code: 'CWJ',
      },
      { city: '常州', code: 'CZX' },
      { city: '池州', code: 'JUH' },
      {
        city: '大同',
        code: 'DAT',
      },
      { city: '达州', code: 'DAX' },
      { city: '稻城', code: 'DCY' },
      {
        city: '丹东',
        code: 'DDG',
      },
      { city: '迪庆', code: 'DIG' },
      { city: '大理', code: 'DLU' },
      {
        city: '敦煌',
        code: 'DNH',
      },
      { city: '东营', code: 'DOY' },
      { city: '大庆', code: 'DQA' },
      {
        city: '德令哈',
        code: 'HXD',
      },
      { city: '鄂尔多斯', code: 'DSN' },
      { city: '额济纳旗', code: 'EJN' },
      {
        city: '恩施',
        code: 'ENH',
      },
      { city: '二连浩特', code: 'ERL' },
      { city: '阜阳', code: 'FUG' },
      {
        city: '抚远',
        code: 'FYJ',
      },
      { city: '富蕴', code: 'FYN' },
      { city: '果洛', code: 'GMQ' },
      {
        city: '格尔木',
        code: 'GOQ',
      },
      { city: '广元', code: 'GYS' },
      { city: '固原', code: 'GYU' },
      {
        city: '中国高雄',
        code: 'KHH',
      },
      { city: '赣州', code: 'KOW' },
      { city: '贵阳', code: 'KWE' },
      {
        city: '桂林',
        code: 'KWL',
      },
      { city: '红原', code: 'AHJ' },
      { city: '海口', code: 'HAK' },
      {
        city: '河池',
        code: 'HCJ',
      },
      { city: '邯郸', code: 'HDG' },
      { city: '黑河', code: 'HEK' },
      {
        city: '呼和浩特',
        code: 'HET',
      },
      { city: '合肥', code: 'HFE' },
      { city: '淮安', code: 'HIA' },
      {
        city: '怀化',
        code: 'HJJ',
      },
      { city: '海拉尔', code: 'HLD' },
      { city: '哈密', code: 'HMI' },
      {
        city: '衡阳',
        code: 'HNY',
      },
      { city: '哈尔滨', code: 'HRB' },
      { city: '和田', code: 'HTN' },
      {
        city: '花土沟',
        code: 'HTT',
      },
      { city: '中国花莲', code: 'HUN' },
      { city: '霍林郭勒', code: 'HUO' },
      {
        city: '惠州',
        code: 'HUZ',
      },
      { city: '汉中', code: 'HZG' },
      { city: '黄山', code: 'TXN' },
      {
        city: '呼伦贝尔',
        code: 'XRQ',
      },
      { city: '中国嘉义', code: 'CYI' },
      { city: '景德镇', code: 'JDZ' },
      {
        city: '加格达奇',
        code: 'JGD',
      },
      { city: '嘉峪关', code: 'JGN' },
      { city: '井冈山', code: 'JGS' },
      {
        city: '金昌',
        code: 'JIC',
      },
      { city: '九江', code: 'JIU' },
      { city: '荆门', code: 'JM1' },
      {
        city: '佳木斯',
        code: 'JMU',
      },
      { city: '济宁', code: 'JNG' },
      { city: '锦州', code: 'JNZ' },
      {
        city: '建三江',
        code: 'JSJ',
      },
      { city: '鸡西', code: 'JXA' },
      { city: '九寨沟', code: 'JZH' },
      {
        city: '中国金门',
        code: 'KNH',
      },
      { city: '揭阳', code: 'SWA' },
      { city: '库车', code: 'KCA' },
      {
        city: '康定',
        code: 'KGT',
      },
      { city: '喀什', code: 'KHG' },
      { city: '凯里', code: 'KJH' },
      {
        city: '库尔勒',
        code: 'KRL',
      },
      { city: '克拉玛依', code: 'KRY' },
      { city: '黎平', code: 'HZH' },
      {
        city: '澜沧',
        code: 'JMJ',
      },
      { city: '龙岩', code: 'LCX' },
      { city: '临汾', code: 'LFQ' },
      {
        city: '兰州',
        code: 'LHW',
      },
      { city: '丽江', code: 'LJG' },
      { city: '荔波', code: 'LLB' },
      {
        city: '吕梁',
        code: 'LLV',
      },
      { city: '临沧', code: 'LNJ' },
      { city: '陇南', code: 'LNL' },
      {
        city: '六盘水',
        code: 'LPF',
      },
      { city: '拉萨', code: 'LXA' },
      { city: '洛阳', code: 'LYA' },
      {
        city: '连云港',
        code: 'LYG',
      },
      { city: '临沂', code: 'LYI' },
      { city: '柳州', code: 'LZH' },
      {
        city: '泸州',
        code: 'LZO',
      },
      { city: '林芝', code: 'LZY' },
      { city: '芒市', code: 'LUM' },
      {
        city: '牡丹江',
        code: 'MDG',
      },
      { city: '中国马祖', code: 'MFK' },
      { city: '绵阳', code: 'MIG' },
      {
        city: '梅州',
        code: 'MXZ',
      },
      { city: '中国马公', code: 'MZG' },
      { city: '满洲里', code: 'NZH' },
      {
        city: '漠河',
        code: 'OHE',
      },
      { city: '南昌', code: 'KHN' },
      { city: '中国南竿', code: 'LZN' },
      {
        city: '南充',
        code: 'NAO',
      },
      { city: '宁波', code: 'NGB' },
      { city: '宁蒗', code: 'NLH' },
      {
        city: '南宁',
        code: 'NNG',
      },
      { city: '南阳', code: 'NNY' },
      { city: '南通', code: 'NTG' },
      {
        city: '攀枝花',
        code: 'PZI',
      },
      { city: '普洱', code: 'SYM' },
      { city: '琼海', code: 'BAR' },
      {
        city: '秦皇岛',
        code: 'BPE',
      },
      { city: '祁连', code: 'HBQ' },
      { city: '且末', code: 'IQM' },
      {
        city: '庆阳',
        code: 'IQN',
      },
      { city: '黔江', code: 'JIQ' },
      { city: '泉州', code: 'JJN' },
      {
        city: '衢州',
        code: 'JUZ',
      },
      { city: '齐齐哈尔', code: 'NDG' },
      { city: '日照', code: 'RIZ' },
      {
        city: '日喀则',
        code: 'RKZ',
      },
      { city: '若羌', code: 'RQA' },
      { city: '神农架', code: 'HPG' },
      {
        city: '莎车',
        code: 'QSZ',
      },
      { city: '沈阳', code: 'SHE' },
      { city: '石河子', code: 'SHF' },
      {
        city: '石家庄',
        code: 'SJW',
      },
      { city: '上饶', code: 'SQD' },
      { city: '三明', code: 'SQJ' },
      {
        city: '十堰',
        code: 'WDS',
      },
      { city: '邵阳', code: 'WGN' },
      { city: '松原', code: 'YSQ' },
      {
        city: '台州',
        code: 'HYN',
      },
      { city: '中国台中', code: 'RMQ' },
      { city: '塔城', code: 'TCG' },
      {
        city: '腾冲',
        code: 'TCZ',
      },
      { city: '铜仁', code: 'TEN' },
      { city: '通辽', code: 'TGO' },
      {
        city: '天水',
        code: 'THQ',
      },
      { city: '吐鲁番', code: 'TLQ' },
      { city: '通化', code: 'TNH' },
      {
        city: '中国台南',
        code: 'TNN',
      },
      { city: '中国台北', code: 'TPE' },
      { city: '中国台东', code: 'TTT' },
      {
        city: '唐山',
        code: 'TVS',
      },
      { city: '太原', code: 'TYN' },
      { city: '五大连池', code: 'DTU' },
      {
        city: '乌兰浩特',
        code: 'HLH',
      },
      { city: '乌兰察布', code: 'UCB' },
      { city: '乌鲁木齐', code: 'URC' },
      {
        city: '潍坊',
        code: 'WEF',
      },
      { city: '威海', code: 'WEH' },
      { city: '文山', code: 'WNH' },
      {
        city: '温州',
        code: 'WNZ',
      },
      { city: '乌海', code: 'WUA' },
      { city: '武夷山', code: 'WUS' },
      {
        city: '无锡',
        code: 'WUX',
      },
      { city: '梧州', code: 'WUZ' },
      { city: '万州', code: 'WXN' },
      {
        city: '乌拉特中旗',
        code: 'WZQ',
      },
      { city: '巫山', code: 'WSK' },
      { city: '兴义', code: 'ACX' },
      {
        city: '夏河',
        code: 'GXH',
      },
      { city: '中国香港', code: 'HKG' },
      { city: '西双版纳', code: 'JHG' },
      {
        city: '新源',
        code: 'NLT',
      },
      { city: '忻州', code: 'WUT' },
      { city: '信阳', code: 'XAI' },
      {
        city: '襄阳',
        code: 'XFN',
      },
      { city: '西昌', code: 'XIC' },
      { city: '锡林浩特', code: 'XIL' },
      {
        city: '西宁',
        code: 'XNN',
      },
      { city: '徐州', code: 'XUZ' },
      { city: '延安', code: 'ENY' },
      {
        city: '银川',
        code: 'INC',
      },
      { city: '伊春', code: 'LDS' },
      { city: '永州', code: 'LLF' },
      {
        city: '榆林',
        code: 'UYN',
      },
      { city: '宜宾', code: 'YBP' },
      { city: '运城', code: 'YCU' },
      {
        city: '宜春',
        code: 'YIC',
      },
      { city: '宜昌', code: 'YIH' },
      { city: '伊宁', code: 'YIN' },
      {
        city: '义乌',
        code: 'YIW',
      },
      { city: '营口', code: 'YKH' },
      { city: '延吉', code: 'YNJ' },
      {
        city: '烟台',
        code: 'YNT',
      },
      { city: '盐城', code: 'YNZ' },
      { city: '扬州', code: 'YTY' },
      {
        city: '玉树',
        code: 'YUS',
      },
      { city: '岳阳', code: 'YYA' },
      { city: '张家界', code: 'DYG' },
      {
        city: '舟山',
        code: 'HSN',
      },
      { city: '扎兰屯', code: 'NZL' },
      { city: '张掖', code: 'YZY' },
      {
        city: '昭通',
        code: 'ZAT',
      },
      { city: '湛江', code: 'ZHA' },
      { city: '中卫', code: 'ZHY' },
      {
        city: '张家口',
        code: 'ZQZ',
      },
      { city: '珠海', code: 'ZUH' },
      { city: '遵义', code: 'ZYI' },
    ];
    let city = [];
    for (let item of airportToCity) {
      city.push(item.code);
    }
    return city;
  },

  async rejc(code) {
    const { ctx, app } = this;
    let airportToCity = [
      { city: '北京', code: 'BJS' },
      { city: '上海', code: 'SHA' },
      {
        city: '广州',
        code: 'CAN',
      },
      { city: '深圳', code: 'SZX' },
      { city: '成都', code: 'CTU' },
      {
        city: '杭州',
        code: 'HGH',
      },
      { city: '武汉', code: 'WUH' },
      { city: '西安', code: 'SIA' },
      {
        city: '重庆',
        code: 'CKG',
      },
      { city: '青岛', code: 'TAO' },
      { city: '长沙', code: 'CSX' },
      {
        city: '南京',
        code: 'NKG',
      },
      { city: '厦门', code: 'XMN' },
      { city: '昆明', code: 'KMG' },
      {
        city: '大连',
        code: 'DLC',
      },
      { city: '天津', code: 'TSN' },
      { city: '郑州', code: 'CGO' },
      {
        city: '三亚',
        code: 'SYX',
      },
      { city: '济南', code: 'TNA' },
      { city: '福州', code: 'FOC' },
      {
        city: '阿勒泰',
        code: 'AAT',
      },
      { city: '阿克苏', code: 'AKU' },
      { city: '鞍山', code: 'AOG' },
      {
        city: '安庆',
        code: 'AQG',
      },
      { city: '安顺', code: 'AVA' },
      { city: '阿拉善左旗', code: 'AXF' },
      {
        city: '中国澳门',
        code: 'MFM',
      },
      { city: '阿里', code: 'NGQ' },
      { city: '阿拉善右旗', code: 'RHT' },
      {
        city: '阿尔山',
        code: 'YIE',
      },
      { city: '巴中', code: 'BZX' },
      { city: '百色', code: 'AEB' },
      {
        city: '包头',
        code: 'BAV',
      },
      { city: '毕节', code: 'BFJ' },
      { city: '北海', code: 'BHY' },
      {
        city: '北京(大兴国际机场)',
        code: 'BJS,PKX',
      },
      { city: '北京(首都国际机场)', code: 'BJS,PEK' },
      { city: '博乐', code: 'BPL' },
      {
        city: '保山',
        code: 'BSD',
      },
      { city: '白城', code: 'DBC' },
      { city: '布尔津', code: 'KJI' },
      {
        city: '白山',
        code: 'NBS',
      },
      { city: '巴彦淖尔', code: 'RLK' },
      { city: '昌都', code: 'BPX' },
      {
        city: '承德',
        code: 'CDE',
      },
      { city: '常德', code: 'CGD' },
      { city: '长春', code: 'CGQ' },
      {
        city: '朝阳',
        code: 'CHG',
      },
      { city: '赤峰', code: 'CIF' },
      { city: '长治', code: 'CIH' },
      {
        city: '沧源',
        code: 'CWJ',
      },
      { city: '常州', code: 'CZX' },
      { city: '池州', code: 'JUH' },
      {
        city: '大同',
        code: 'DAT',
      },
      { city: '达州', code: 'DAX' },
      { city: '稻城', code: 'DCY' },
      {
        city: '丹东',
        code: 'DDG',
      },
      { city: '迪庆', code: 'DIG' },
      { city: '大理', code: 'DLU' },
      {
        city: '敦煌',
        code: 'DNH',
      },
      { city: '东营', code: 'DOY' },
      { city: '大庆', code: 'DQA' },
      {
        city: '德令哈',
        code: 'HXD',
      },
      { city: '鄂尔多斯', code: 'DSN' },
      { city: '额济纳旗', code: 'EJN' },
      {
        city: '恩施',
        code: 'ENH',
      },
      { city: '二连浩特', code: 'ERL' },
      { city: '阜阳', code: 'FUG' },
      {
        city: '抚远',
        code: 'FYJ',
      },
      { city: '富蕴', code: 'FYN' },
      { city: '果洛', code: 'GMQ' },
      {
        city: '格尔木',
        code: 'GOQ',
      },
      { city: '广元', code: 'GYS' },
      { city: '固原', code: 'GYU' },
      {
        city: '中国高雄',
        code: 'KHH',
      },
      { city: '赣州', code: 'KOW' },
      { city: '贵阳', code: 'KWE' },
      {
        city: '桂林',
        code: 'KWL',
      },
      { city: '红原', code: 'AHJ' },
      { city: '海口', code: 'HAK' },
      {
        city: '河池',
        code: 'HCJ',
      },
      { city: '邯郸', code: 'HDG' },
      { city: '黑河', code: 'HEK' },
      {
        city: '呼和浩特',
        code: 'HET',
      },
      { city: '合肥', code: 'HFE' },
      { city: '淮安', code: 'HIA' },
      {
        city: '怀化',
        code: 'HJJ',
      },
      { city: '海拉尔', code: 'HLD' },
      { city: '哈密', code: 'HMI' },
      {
        city: '衡阳',
        code: 'HNY',
      },
      { city: '哈尔滨', code: 'HRB' },
      { city: '和田', code: 'HTN' },
      {
        city: '花土沟',
        code: 'HTT',
      },
      { city: '中国花莲', code: 'HUN' },
      { city: '霍林郭勒', code: 'HUO' },
      {
        city: '惠州',
        code: 'HUZ',
      },
      { city: '汉中', code: 'HZG' },
      { city: '黄山', code: 'TXN' },
      {
        city: '呼伦贝尔',
        code: 'XRQ',
      },
      { city: '中国嘉义', code: 'CYI' },
      { city: '景德镇', code: 'JDZ' },
      {
        city: '加格达奇',
        code: 'JGD',
      },
      { city: '嘉峪关', code: 'JGN' },
      { city: '井冈山', code: 'JGS' },
      {
        city: '金昌',
        code: 'JIC',
      },
      { city: '九江', code: 'JIU' },
      { city: '荆门', code: 'JM1' },
      {
        city: '佳木斯',
        code: 'JMU',
      },
      { city: '济宁', code: 'JNG' },
      { city: '锦州', code: 'JNZ' },
      {
        city: '建三江',
        code: 'JSJ',
      },
      { city: '鸡西', code: 'JXA' },
      { city: '九寨沟', code: 'JZH' },
      {
        city: '中国金门',
        code: 'KNH',
      },
      { city: '揭阳', code: 'SWA' },
      { city: '库车', code: 'KCA' },
      {
        city: '康定',
        code: 'KGT',
      },
      { city: '喀什', code: 'KHG' },
      { city: '凯里', code: 'KJH' },
      {
        city: '库尔勒',
        code: 'KRL',
      },
      { city: '克拉玛依', code: 'KRY' },
      { city: '黎平', code: 'HZH' },
      {
        city: '澜沧',
        code: 'JMJ',
      },
      { city: '龙岩', code: 'LCX' },
      { city: '临汾', code: 'LFQ' },
      {
        city: '兰州',
        code: 'LHW',
      },
      { city: '丽江', code: 'LJG' },
      { city: '荔波', code: 'LLB' },
      {
        city: '吕梁',
        code: 'LLV',
      },
      { city: '临沧', code: 'LNJ' },
      { city: '陇南', code: 'LNL' },
      {
        city: '六盘水',
        code: 'LPF',
      },
      { city: '拉萨', code: 'LXA' },
      { city: '洛阳', code: 'LYA' },
      {
        city: '连云港',
        code: 'LYG',
      },
      { city: '临沂', code: 'LYI' },
      { city: '柳州', code: 'LZH' },
      {
        city: '泸州',
        code: 'LZO',
      },
      { city: '林芝', code: 'LZY' },
      { city: '芒市', code: 'LUM' },
      {
        city: '牡丹江',
        code: 'MDG',
      },
      { city: '中国马祖', code: 'MFK' },
      { city: '绵阳', code: 'MIG' },
      {
        city: '梅州',
        code: 'MXZ',
      },
      { city: '中国马公', code: 'MZG' },
      { city: '满洲里', code: 'NZH' },
      {
        city: '漠河',
        code: 'OHE',
      },
      { city: '南昌', code: 'KHN' },
      { city: '中国南竿', code: 'LZN' },
      {
        city: '南充',
        code: 'NAO',
      },
      { city: '宁波', code: 'NGB' },
      { city: '宁蒗', code: 'NLH' },
      {
        city: '南宁',
        code: 'NNG',
      },
      { city: '南阳', code: 'NNY' },
      { city: '南通', code: 'NTG' },
      {
        city: '攀枝花',
        code: 'PZI',
      },
      { city: '普洱', code: 'SYM' },
      { city: '琼海', code: 'BAR' },
      {
        city: '秦皇岛',
        code: 'BPE',
      },
      { city: '祁连', code: 'HBQ' },
      { city: '且末', code: 'IQM' },
      {
        city: '庆阳',
        code: 'IQN',
      },
      { city: '黔江', code: 'JIQ' },
      { city: '泉州', code: 'JJN' },
      {
        city: '衢州',
        code: 'JUZ',
      },
      { city: '齐齐哈尔', code: 'NDG' },
      { city: '日照', code: 'RIZ' },
      {
        city: '日喀则',
        code: 'RKZ',
      },
      { city: '若羌', code: 'RQA' },
      { city: '神农架', code: 'HPG' },
      {
        city: '莎车',
        code: 'QSZ',
      },
      { city: '沈阳', code: 'SHE' },
      { city: '石河子', code: 'SHF' },
      {
        city: '石家庄',
        code: 'SJW',
      },
      { city: '上饶', code: 'SQD' },
      { city: '三明', code: 'SQJ' },
      {
        city: '十堰',
        code: 'WDS',
      },
      { city: '邵阳', code: 'WGN' },
      { city: '松原', code: 'YSQ' },
      {
        city: '台州',
        code: 'HYN',
      },
      { city: '中国台中', code: 'RMQ' },
      { city: '塔城', code: 'TCG' },
      {
        city: '腾冲',
        code: 'TCZ',
      },
      { city: '铜仁', code: 'TEN' },
      { city: '通辽', code: 'TGO' },
      {
        city: '天水',
        code: 'THQ',
      },
      { city: '吐鲁番', code: 'TLQ' },
      { city: '通化', code: 'TNH' },
      {
        city: '中国台南',
        code: 'TNN',
      },
      { city: '中国台北', code: 'TPE' },
      { city: '中国台东', code: 'TTT' },
      {
        city: '唐山',
        code: 'TVS',
      },
      { city: '太原', code: 'TYN' },
      { city: '五大连池', code: 'DTU' },
      {
        city: '乌兰浩特',
        code: 'HLH',
      },
      { city: '乌兰察布', code: 'UCB' },
      { city: '乌鲁木齐', code: 'URC' },
      {
        city: '潍坊',
        code: 'WEF',
      },
      { city: '威海', code: 'WEH' },
      { city: '文山', code: 'WNH' },
      {
        city: '温州',
        code: 'WNZ',
      },
      { city: '乌海', code: 'WUA' },
      { city: '武夷山', code: 'WUS' },
      {
        city: '无锡',
        code: 'WUX',
      },
      { city: '梧州', code: 'WUZ' },
      { city: '万州', code: 'WXN' },
      {
        city: '乌拉特中旗',
        code: 'WZQ',
      },
      { city: '巫山', code: 'WSK' },
      { city: '兴义', code: 'ACX' },
      {
        city: '夏河',
        code: 'GXH',
      },
      { city: '中国香港', code: 'HKG' },
      { city: '西双版纳', code: 'JHG' },
      {
        city: '新源',
        code: 'NLT',
      },
      { city: '忻州', code: 'WUT' },
      { city: '信阳', code: 'XAI' },
      {
        city: '襄阳',
        code: 'XFN',
      },
      { city: '西昌', code: 'XIC' },
      { city: '锡林浩特', code: 'XIL' },
      {
        city: '西宁',
        code: 'XNN',
      },
      { city: '徐州', code: 'XUZ' },
      { city: '延安', code: 'ENY' },
      {
        city: '银川',
        code: 'INC',
      },
      { city: '伊春', code: 'LDS' },
      { city: '永州', code: 'LLF' },
      {
        city: '榆林',
        code: 'UYN',
      },
      { city: '宜宾', code: 'YBP' },
      { city: '运城', code: 'YCU' },
      {
        city: '宜春',
        code: 'YIC',
      },
      { city: '宜昌', code: 'YIH' },
      { city: '伊宁', code: 'YIN' },
      {
        city: '义乌',
        code: 'YIW',
      },
      { city: '营口', code: 'YKH' },
      { city: '延吉', code: 'YNJ' },
      {
        city: '烟台',
        code: 'YNT',
      },
      { city: '盐城', code: 'YNZ' },
      { city: '扬州', code: 'YTY' },
      {
        city: '玉树',
        code: 'YUS',
      },
      { city: '岳阳', code: 'YYA' },
      { city: '张家界', code: 'DYG' },
      {
        city: '舟山',
        code: 'HSN',
      },
      { city: '扎兰屯', code: 'NZL' },
      { city: '张掖', code: 'YZY' },
      {
        city: '昭通',
        code: 'ZAT',
      },
      { city: '湛江', code: 'ZHA' },
      { city: '中卫', code: 'ZHY' },
      {
        city: '张家口',
        code: 'ZQZ',
      },
      { city: '珠海', code: 'ZUH' },
      { city: '遵义', code: 'ZYI' },
    ];
    // let jc = "";
    // for (let item in airportToCity) {
    //     if (code === airportToCity[item]) {
    //         jc = item;
    //         return item;
    //     }
    // }
    // ctx.body = airportToCity
    // const cityArray = Object.entries(airportToCity).map(([city, code]) => ({ city, code }));
    let jc = airportToCity.find((item) => item.code === code);

    if (jc) {
      return jc.city;
    } else {
      return airportToCity;
    }
  },
};

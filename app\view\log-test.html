<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志编码测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            background: #1a1a1a;
            color: #fff;
            margin: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .log-box {
            background: #2d2d2d;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            height: 300px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-line {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-line:hover {
            background: rgba(255,255,255,0.1);
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
        }
        .status.connected { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.connecting { background: #ff9800; }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .info {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 日志编码测试工具</h1>
        
        <div class="info">
            <h3>测试说明：</h3>
            <p>1. 点击"连接日志流"测试SSE连接</p>
            <p>2. 观察中文字符是否正常显示</p>
            <p>3. 检查控制台是否有错误信息</p>
        </div>

        <div class="controls">
            <button onclick="connectLog()">连接日志流</button>
            <button onclick="disconnectLog()">断开连接</button>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="testAPI()">测试API</button>
            <span id="status" class="status connecting">准备中</span>
        </div>

        <div class="log-box" id="logContainer">
            <div class="log-line">等待连接...</div>
        </div>

        <div class="info">
            <h3>调试信息：</h3>
            <div id="debugInfo">无</div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let lineCount = 0;

        function updateStatus(status, text) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = text;
        }

        function addLogLine(content, type = 'normal') {
            const container = document.getElementById('logContainer');
            const line = document.createElement('div');
            line.className = 'log-line';
            
            // 显示原始数据和处理后的数据
            const timestamp = new Date().toLocaleTimeString();
            line.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${escapeHtml(content)}`;
            
            container.appendChild(line);
            lineCount++;
            
            // 限制最大行数
            if (container.children.length > 100) {
                container.removeChild(container.firstChild);
            }
            
            // 自动滚动
            container.scrollTop = container.scrollHeight;
        }

        function addDebugInfo(info) {
            const debugEl = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugEl.innerHTML += `<div>[${timestamp}] ${escapeHtml(info)}</div>`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function connectLog() {
            if (eventSource) {
                eventSource.close();
            }

            updateStatus('connecting', '连接中...');
            addDebugInfo('开始连接日志流...');

            eventSource = new EventSource('/api/logs/web');

            eventSource.onopen = function() {
                updateStatus('connected', '已连接');
                addDebugInfo('SSE连接成功');
                addLogLine('=== 日志流已连接 ===');
            };

            eventSource.onmessage = function(event) {
                try {
                    addDebugInfo(`收到数据: ${event.data.substring(0, 100)}...`);
                    
                    const data = JSON.parse(event.data);
                    
                    if (data.line) {
                        // 显示原始行和时间戳
                        addLogLine(`${data.line}`);
                        
                        // 检查是否包含中文
                        if (/[\u4e00-\u9fa5]/.test(data.line)) {
                            addDebugInfo('✅ 检测到中文字符，显示正常');
                        }
                        
                        // 检查是否包含乱码
                        if (data.line.includes('鏈') || data.line.includes('嶅') || data.line.includes('姟')) {
                            addDebugInfo('⚠️ 检测到可能的乱码字符');
                        }
                    } else if (data.message) {
                        addLogLine(`[系统] ${data.message}`);
                    }
                } catch (e) {
                    addDebugInfo(`JSON解析失败: ${e.message}`);
                    addLogLine(`[原始数据] ${event.data}`);
                }
            };

            eventSource.onerror = function(error) {
                updateStatus('error', '连接错误');
                addDebugInfo(`连接错误: ${error}`);
                addLogLine('=== 连接中断 ===');
                
                // 5秒后重连
                setTimeout(() => {
                    if (eventSource && eventSource.readyState === EventSource.CLOSED) {
                        addDebugInfo('尝试重新连接...');
                        connectLog();
                    }
                }, 5000);
            };
        }

        function disconnectLog() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateStatus('connecting', '已断开');
            addDebugInfo('手动断开连接');
            addLogLine('=== 连接已断开 ===');
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('debugInfo').innerHTML = '已清空';
            lineCount = 0;
        }

        async function testAPI() {
            addDebugInfo('测试API接口...');
            
            try {
                const response = await fetch('/api/logs/info');
                const data = await response.json();
                addDebugInfo(`API测试成功: ${JSON.stringify(data)}`);
            } catch (error) {
                addDebugInfo(`API测试失败: ${error.message}`);
            }
        }

        // 页面加载时自动连接
        window.addEventListener('load', function() {
            addDebugInfo('页面加载完成');
            updateStatus('connecting', '准备连接');
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>

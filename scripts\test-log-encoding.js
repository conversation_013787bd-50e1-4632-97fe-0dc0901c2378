#!/usr/bin/env node

// 测试日志编码问题

const axios = require('axios');

// 配置
const BASE_URL = 'https://egg.wcy9.com';
// const BASE_URL = 'http://localhost:7001';

async function testLogAPI() {
  console.log('🧪 测试日志API编码问题\n');
  
  try {
    console.log('1️⃣ 测试日志信息接口...');
    const infoResponse = await axios.get(`${BASE_URL}/api/logs/info`);
    console.log('✅ 日志信息接口正常');
    console.log('响应数据:', JSON.stringify(infoResponse.data, null, 2));
    
  } catch (error) {
    console.error('❌ 日志信息接口失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
  
  console.log('\n2️⃣ 测试SSE连接...');
  
  // 测试SSE连接
  try {
    const response = await axios.get(`${BASE_URL}/api/logs/web`, {
      responseType: 'stream',
      timeout: 5000
    });
    
    console.log('✅ SSE连接成功');
    console.log('响应头:', response.headers);
    
    let dataReceived = '';
    let lineCount = 0;
    
    response.data.on('data', (chunk) => {
      const chunkStr = chunk.toString('utf8');
      dataReceived += chunkStr;
      
      // 计算接收到的行数
      const lines = chunkStr.split('\n');
      lineCount += lines.length - 1;
      
      console.log(`📥 接收数据块 (${chunk.length} 字节, 累计${lineCount}行)`);
      
      // 显示前几行数据
      if (lineCount <= 5) {
        console.log('数据内容:', chunkStr.substring(0, 200) + '...');
      }
      
      // 接收到足够数据后停止
      if (lineCount >= 10) {
        response.data.destroy();
      }
    });
    
    response.data.on('end', () => {
      console.log('✅ SSE流结束');
      console.log(`总共接收 ${lineCount} 行数据`);
    });
    
    response.data.on('error', (error) => {
      console.error('❌ SSE流错误:', error.message);
    });
    
    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 3000));
    
  } catch (error) {
    console.error('❌ SSE连接失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('状态文本:', error.response.statusText);
    } else {
      console.error('错误:', error.message);
    }
  }
}

async function testDirectCurl() {
  console.log('\n3️⃣ 模拟curl测试...');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/logs/web`, {
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      responseType: 'text',
      timeout: 3000
    });
    
    console.log('✅ 直接请求成功');
    console.log('响应长度:', response.data.length);
    console.log('前500字符:', response.data.substring(0, 500));
    
  } catch (error) {
    console.error('❌ 直接请求失败');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', error.response.data?.substring(0, 200));
    } else {
      console.error('错误:', error.message);
    }
  }
}

async function testEncodingFix() {
  console.log('\n4️⃣ 测试编码修复...');
  
  // 模拟乱码数据
  const garbledText = '鏈嶅姟鍣ㄤ綅缃�: undefined, AI浠诲姟鍚敤: false';
  
  console.log('原始乱码:', garbledText);
  
  // 尝试不同的解码方法
  try {
    // 方法1: 假设是UTF-8被错误解释为Latin-1
    const method1 = Buffer.from(garbledText, 'latin1').toString('utf8');
    console.log('方法1 (latin1->utf8):', method1);
  } catch (e) {
    console.log('方法1失败:', e.message);
  }
  
  try {
    // 方法2: URL解码
    const method2 = decodeURIComponent(escape(garbledText));
    console.log('方法2 (URL解码):', method2);
  } catch (e) {
    console.log('方法2失败:', e.message);
  }
  
  try {
    // 方法3: 直接转换
    const method3 = garbledText.replace(/鏈/g, '服').replace(/嶅/g, '务').replace(/姟/g, '器');
    console.log('方法3 (字符替换):', method3);
  } catch (e) {
    console.log('方法3失败:', e.message);
  }
}

// 主函数
async function main() {
  console.log('🔍 日志编码问题诊断工具');
  console.log('服务器地址:', BASE_URL);
  console.log('测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('═'.repeat(60));
  
  await testLogAPI();
  await testDirectCurl();
  await testEncodingFix();
  
  console.log('\n🎯 问题分析:');
  console.log('1. 如果API返回404，检查路由配置和控制器');
  console.log('2. 如果有乱码，可能是服务器端编码问题');
  console.log('3. 检查日志文件本身的编码格式');
  console.log('4. 确认tail命令的输出编码');
  
  console.log('\n💡 解决方案:');
  console.log('1. 在spawn中设置正确的编码');
  console.log('2. 在响应头中指定charset=utf-8');
  console.log('3. 在前端正确解码接收到的数据');
  
  console.log('\n🎉 测试完成!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testLogAPI, testDirectCurl, testEncodingFix };

const axios = require('axios');
const { dateNow } = require('../extend/helper');
module.exports = {
  schedule: {
    interval: '62s', // 1 分钟间隔
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['prod'],
    immediate: true,
    enabled: true,
  },
  async task(ctx) {
    let ip = await axios.get('https://ip.wcy9.com/cnip.php');
    ip = ip.data.toString('utf8'); // 将二进制数据转换为字符串
    if (ip === '*************') {
      // const data = ['airall'];
      const data = [''];

      for (let i in data) {
        await axios
          .get('http://127.0.0.1:7001/' + data[i])
          .then((res) => {
            if (res) {
              // console.log(res.data);
            }
          })
          .catch((e) => {
            if (e) {
              // ctx.service.feishu.fs(data[i] + '异常');
            }
          });
      }
    }

    // await axios.get("http://127.0.0.1:7001/xccf").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xchl").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdaxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdaxmch").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sdahfhl").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xmairxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/xmairhfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/hfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lzxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/sda").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lyxmlz").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lyhfxm").then(() => {});
    // await axios.get("http://127.0.0.1:7001/lylzxm").then(() => {});
  },
};

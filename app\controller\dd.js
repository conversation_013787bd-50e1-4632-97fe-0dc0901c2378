const Controller = require('egg').Controller;

class Dd<PERSON>ontroller extends Controller {
  async dd() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.dd.dd(text);
  }

  async dd2() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.dd.dd2(text);
  }

  async ddm() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    let title = ctx.query.title;
    ctx.body = await ctx.service.dd.ddm(text, title);
  }

  async ddpost() {
    const { ctx } = this;
    const text = ctx.request.body.text;
    let dd = await ctx.service.dd.dd2(text);
    let fs = await ctx.service.feishu.fs(text);
    ctx.body = { dd: dd, fs: fs };
  }

  async ddup() {
    const { ctx } = this;
    const file = ctx.request.files[0];
    let res = await ctx.service.tele.upload(file.filepath);
    console.log(res[0].src);
    let text = 'https://img1.101616.xyz' + res[0].src;
    let title = ctx.query.title;
    console.log(text);
    ctx.body = await ctx.service.dd.ddm(text, title, 2);
  }
}

module.exports = DdController;

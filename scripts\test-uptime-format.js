#!/usr/bin/env node

// 测试运行时长格式化函数

function formatUptime(seconds) {
  // 使用更精确的时间计算
  const YEAR_SECONDS = 365.25 * 24 * 3600; // 考虑闰年
  const MONTH_SECONDS = 30.44 * 24 * 3600; // 平均月份天数
  const DAY_SECONDS = 24 * 3600;
  const HOUR_SECONDS = 3600;
  const MINUTE_SECONDS = 60;

  let remaining = seconds;

  const years = Math.floor(remaining / YEAR_SECONDS);
  remaining = remaining % YEAR_SECONDS;

  const months = Math.floor(remaining / MONTH_SECONDS);
  remaining = remaining % MONTH_SECONDS;

  const days = Math.floor(remaining / DAY_SECONDS);
  remaining = remaining % DAY_SECONDS;

  const hours = Math.floor(remaining / HOUR_SECONDS);
  remaining = remaining % HOUR_SECONDS;

  const minutes = Math.floor(remaining / MINUTE_SECONDS);
  const secs = Math.floor(remaining % MINUTE_SECONDS);

  const parts = [];
  if (years > 0) parts.push(`${years}年`);
  if (months > 0) parts.push(`${months}月`);
  if (days > 0) parts.push(`${days}日`);
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分钟`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);

  return parts.join('');
}

// 测试用例
const testCases = [
  { seconds: 30, description: '30秒' },
  { seconds: 90, description: '1分30秒' },
  { seconds: 3661, description: '1小时1分1秒' },
  { seconds: 86400, description: '1天' },
  { seconds: 90061, description: '1天多' },
  { seconds: 2592000, description: '约1个月' },
  { seconds: 31557600, description: '约1年 (365.25天)' },
  { seconds: 0, description: '0秒' },
  { seconds: 60, description: '1分钟' },
  { seconds: 3600, description: '1小时' },
  { seconds: 604800, description: '1周' },
  { seconds: 31557600 + 2629800, description: '1年1个月' },
];

console.log('🧪 运行时长格式化测试\n');

testCases.forEach((testCase, index) => {
  const result = formatUptime(testCase.seconds);

  console.log(`测试 ${index + 1}: ✅`);
  console.log(`  输入: ${testCase.seconds} 秒 (${testCase.description})`);
  console.log(`  结果: ${result}`);
  console.log('');
});

// 模拟当前进程运行时长
console.log('📊 当前进程运行时长示例:');
console.log(`当前进程已运行: ${formatUptime(process.uptime())}`);

// 模拟不同运行时长的显示效果
console.log('\n🎯 不同运行时长显示效果:');
const examples = [
  { name: '刚启动', seconds: 5 },
  { name: '运行1小时', seconds: 3600 },
  { name: '运行1天', seconds: 86400 },
  { name: '运行1周', seconds: 604800 },
  { name: '运行1个月', seconds: 2592000 },
  { name: '运行1年', seconds: 31536000 },
  { name: '长期运行', seconds: 34214461 },
];

examples.forEach((example) => {
  console.log(`${example.name}: ${formatUptime(example.seconds)}`);
});

console.log('\n🎉 测试完成!');

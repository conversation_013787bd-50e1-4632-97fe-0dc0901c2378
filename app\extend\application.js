'use strict';

/**
 * 应用扩展 - 模块懒加载管理器
 * 实现大型依赖模块的按需加载，减少启动时间
 */

const HEAVY_MODULES = {
  puppeteer: () => require('puppeteer'),
  sharp: () => require('sharp'),
  openai: () => require('openai'),
  cheerio: () => require('cheerio'),
  'tencentcloud-sdk-nodejs': () => require('tencentcloud-sdk-nodejs'),
  'form-data': () => require('form-data'),
  axios: () => require('axios'),
  yaml: () => require('yaml'),
};

module.exports = {
  /**
   * 模块懒加载管理器
   */
  get lazyLoader() {
    if (!this._lazyLoader) {
      this._lazyLoader = new LazyModuleLoader(this);
    }
    return this._lazyLoader;
  },

  /**
   * 懒加载模块
   * @param {string} moduleName - 模块名称
   * @returns {Promise<any>} 模块实例
   */
  async loadModule(moduleName) {
    return this.lazyLoader.load(moduleName);
  },

  /**
   * 预加载指定模块（可选）
   * @param {string[]} moduleNames - 模块名称数组
   */
  async preloadModules(moduleNames) {
    return this.lazyLoader.preload(moduleNames);
  },

  /**
   * 获取模块加载统计信息
   */
  getModuleStats() {
    return this.lazyLoader.getStats();
  },

  /**
   * 进程间通信优化
   */
  get ipcOptimizer() {
    if (!this._ipcOptimizer) {
      this._ipcOptimizer = new IPCOptimizer(this);
    }
    return this._ipcOptimizer;
  },

  /**
   * 获取集群状态信息
   */
  getClusterStats() {
    return {
      pid: process.pid,
      workerId: this.config.cluster?.workerId || 0,
      workers: this.config.cluster?.workers || 1,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
    };
  },

  /**
   * 广播消息到所有worker
   */
  broadcastToWorkers(event, data) {
    if (this.messenger) {
      this.messenger.broadcast(event, data);
    }
  },

  /**
   * 发送消息到特定worker
   */
  sendToWorker(workerId, event, data) {
    if (this.messenger) {
      this.messenger.sendTo(workerId, event, data);
    }
  },
};

/**
 * 懒加载模块管理器类
 */
class LazyModuleLoader {
  constructor(app) {
    this.app = app;
    this.loadedModules = new Map();
    this.loadingPromises = new Map();
    this.loadStats = {
      totalLoads: 0,
      loadTimes: {},
      memoryUsage: {},
    };
  }

  /**
   * 加载模块
   * @param {string} moduleName - 模块名称
   * @returns {Promise<any>} 模块实例
   */
  async load(moduleName) {
    // 如果模块已加载，直接返回
    if (this.loadedModules.has(moduleName)) {
      return this.loadedModules.get(moduleName);
    }

    // 如果正在加载，返回加载Promise
    if (this.loadingPromises.has(moduleName)) {
      return this.loadingPromises.get(moduleName);
    }

    // 开始加载模块
    const loadPromise = this._loadModuleInternal(moduleName);
    this.loadingPromises.set(moduleName, loadPromise);

    try {
      const module = await loadPromise;
      this.loadedModules.set(moduleName, module);
      this.loadingPromises.delete(moduleName);
      return module;
    } catch (error) {
      this.loadingPromises.delete(moduleName);
      throw error;
    }
  }

  /**
   * 内部模块加载方法
   * @param {string} moduleName - 模块名称
   * @returns {Promise<any>} 模块实例
   */
  async _loadModuleInternal(moduleName) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    try {
      // 记录性能监控
      if (global.performanceMonitor) {
        global.performanceMonitor.markPhase(`开始加载模块: ${moduleName}`);
      }

      console.log(`🔄 [懒加载] 正在加载模块: ${moduleName}`);

      let module;

      // 检查是否为预定义的重型模块
      if (HEAVY_MODULES[moduleName]) {
        module = HEAVY_MODULES[moduleName]();
      } else {
        // 动态导入其他模块
        module = require(moduleName);
      }

      const loadTime = Date.now() - startTime;
      const endMemory = process.memoryUsage();
      const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

      // 记录统计信息
      this.loadStats.totalLoads++;
      this.loadStats.loadTimes[moduleName] = loadTime;
      this.loadStats.memoryUsage[moduleName] = memoryDelta;

      console.log(
        `✅ [懒加载] 模块 ${moduleName} 加载完成 - ${loadTime}ms, 内存增加: ${Math.round(memoryDelta / 1024)}KB`,
      );

      // 记录性能监控
      if (global.performanceMonitor) {
        global.performanceMonitor.markPhase(`模块加载完成: ${moduleName}`);
      }

      return module;
    } catch (error) {
      const loadTime = Date.now() - startTime;
      console.error(`❌ [懒加载] 模块 ${moduleName} 加载失败 - ${loadTime}ms:`, error.message);
      throw new Error(`Failed to load module ${moduleName}: ${error.message}`);
    }
  }

  /**
   * 预加载模块
   * @param {string[]} moduleNames - 模块名称数组
   * @returns {Promise<void>}
   */
  async preload(moduleNames) {
    console.log(`🚀 [预加载] 开始预加载 ${moduleNames.length} 个模块`);

    const preloadPromises = moduleNames.map(async (moduleName) => {
      try {
        await this.load(moduleName);
      } catch (error) {
        console.warn(`⚠️ [预加载] 模块 ${moduleName} 预加载失败:`, error.message);
      }
    });

    await Promise.all(preloadPromises);
    console.log(`✅ [预加载] 预加载完成`);
  }

  /**
   * 获取加载统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    const loadedModuleNames = Array.from(this.loadedModules.keys());
    const totalMemoryUsage = Object.values(this.loadStats.memoryUsage).reduce(
      (sum, usage) => sum + usage,
      0,
    );

    return {
      loadedModules: loadedModuleNames,
      totalLoads: this.loadStats.totalLoads,
      loadTimes: { ...this.loadStats.loadTimes },
      memoryUsage: { ...this.loadStats.memoryUsage },
      totalMemoryUsage: Math.round(totalMemoryUsage / 1024), // KB
      averageLoadTime:
        loadedModuleNames.length > 0
          ? Math.round(
              Object.values(this.loadStats.loadTimes).reduce((sum, time) => sum + time, 0) /
                loadedModuleNames.length,
            )
          : 0,
    };
  }

  /**
   * 清理未使用的模块（可选功能）
   */
  cleanup() {
    // 这里可以实现模块清理逻辑
    // 但需要谨慎，因为可能影响正在使用的模块
    console.log('🧹 [懒加载] 模块清理功能暂未实现');
  }
}

/**
 * 创建懒加载的模块代理
 * @param {string} moduleName - 模块名称
 * @param {object} app - 应用实例
 * @returns {Proxy} 模块代理
 */
function createLazyModuleProxy(moduleName, app) {
  let moduleCache = null;

  return new Proxy(
    {},
    {
      get(target, prop) {
        if (!moduleCache) {
          // 同步加载（注意：这可能会阻塞）
          try {
            moduleCache = require(moduleName);
          } catch (error) {
            throw new Error(`Failed to load module ${moduleName}: ${error.message}`);
          }
        }
        return moduleCache[prop];
      },
    },
  );
}

/**
 * 进程间通信优化器类
 */
class IPCOptimizer {
  constructor(app) {
    this.app = app;
    this.messageQueue = [];
    this.batchSize = 10;
    this.batchTimeout = 100; // 100ms批处理超时
    this.stats = {
      messagesSent: 0,
      messagesReceived: 0,
      batchesSent: 0,
      averageBatchSize: 0,
    };

    this.initBatchProcessor();
  }

  /**
   * 初始化批处理器
   */
  initBatchProcessor() {
    setInterval(() => {
      this.processBatch();
    }, this.batchTimeout);
  }

  /**
   * 添加消息到队列
   */
  queueMessage(event, data, target = 'all') {
    this.messageQueue.push({
      event,
      data,
      target,
      timestamp: Date.now(),
    });

    // 如果队列满了，立即处理
    if (this.messageQueue.length >= this.batchSize) {
      this.processBatch();
    }
  }

  /**
   * 处理批量消息
   */
  processBatch() {
    if (this.messageQueue.length === 0) return;

    const batch = this.messageQueue.splice(0, this.batchSize);
    this.stats.batchesSent++;
    this.stats.messagesSent += batch.length;
    this.stats.averageBatchSize = this.stats.messagesSent / this.stats.batchesSent;

    // 按目标分组消息
    const groupedMessages = {};
    batch.forEach((msg) => {
      if (!groupedMessages[msg.target]) {
        groupedMessages[msg.target] = [];
      }
      groupedMessages[msg.target].push(msg);
    });

    // 发送分组消息
    Object.entries(groupedMessages).forEach(([target, messages]) => {
      if (target === 'all') {
        this.app.messenger.broadcast('batch-messages', messages);
      } else {
        this.app.messenger.sendTo(target, 'batch-messages', messages);
      }
    });
  }

  /**
   * 获取IPC统计信息
   */
  getStats() {
    return {
      ...this.stats,
      queueLength: this.messageQueue.length,
      uptime: process.uptime(),
    };
  }
}

module.exports.createLazyModuleProxy = createLazyModuleProxy;

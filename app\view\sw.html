<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ 开关管理中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 彩虹色定义 */
        .rainbow-0 { background: linear-gradient(135deg, #ff6b6b, #ff8e8e); }
        .rainbow-1 { background: linear-gradient(135deg, #ffa726, #ffcc80); }
        .rainbow-2 { background: linear-gradient(135deg, #ffeb3b, #fff176); }
        .rainbow-3 { background: linear-gradient(135deg, #66bb6a, #a5d6a7); }
        .rainbow-4 { background: linear-gradient(135deg, #42a5f5, #90caf9); }
        .rainbow-5 { background: linear-gradient(135deg, #ab47bc, #ce93d8); }
        .rainbow-6 { background: linear-gradient(135deg, #ec407a, #f48fb1); }

        .rainbow-border-0 { border-color: #ff6b6b; }
        .rainbow-border-1 { border-color: #ffa726; }
        .rainbow-border-2 { border-color: #ffeb3b; }
        .rainbow-border-3 { border-color: #66bb6a; }
        .rainbow-border-4 { border-color: #42a5f5; }
        .rainbow-border-5 { border-color: #ab47bc; }
        .rainbow-border-6 { border-color: #ec407a; }

        /* 开关动画 */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* 卡片悬停效果 */
        .switch-card {
            transition: all 0.3s ease;
        }

        .switch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 加载动画 */
        .loading {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 脉冲动画 */
        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 min-h-screen">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
    </div>

    <!-- 主容器 -->
    <div class="relative z-10 container mx-auto px-4 py-8">
        <!-- 头部 -->
        <header class="text-center mb-12">
            <h1 class="text-5xl font-bold text-gray-800 mb-4">
                🎛️ <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">开关管理中心</span>
            </h1>
            <p class="text-xl text-gray-600 mb-2">智能开关控制面板</p>
            <p class="text-sm text-gray-500">最后更新: <span id="lastUpdate"></span></p>
        </header>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-gray-800" id="totalSwitches">{[ switches | length ]}</div>
                <div class="text-gray-600">总开关数</div>
            </div>
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-green-600" id="activeSwitches">
                    {% set activeCount = 0 %}
                    {% for switch in switches %}
                        {% if switch.sw == 1 %}
                            {% set activeCount = activeCount + 1 %}
                        {% endif %}
                    {% endfor %}
                    {[ activeCount ]}
                </div>
                <div class="text-gray-600">已开启</div>
            </div>
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-red-600" id="inactiveSwitches">
                    {% set inactiveCount = 0 %}
                    {% for switch in switches %}
                        {% if switch.sw == 0 %}
                            {% set inactiveCount = inactiveCount + 1 %}
                        {% endif %}
                    {% endfor %}
                    {[ inactiveCount ]}
                </div>
                <div class="text-gray-600">已关闭</div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
            <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                刷新数据
            </button>
            <button id="addSwitchBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加开关
            </button>
            <button id="toggleAllBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                全部切换
            </button>
        </div>

        <!-- 错误提示 -->
        {% if error %}
        <div class="bg-red-50 border border-red-300 text-red-800 px-4 py-3 rounded-lg mb-6 shadow-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {[ error ]}
            </div>
        </div>
        {% endif %}

        <!-- 开关列表 -->
        <div id="switchesContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {% for switch in switches %}
            <div class="switch-card bg-white/90 backdrop-blur-lg rounded-xl p-6 border-2 rainbow-border-{[ loop.index0 % 7 ]} shadow-lg" data-switch-id="{[ switch.id ]}">
                <!-- 开关头部 -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 rainbow-{[ loop.index0 % 7 ]} rounded-full flex items-center justify-center text-white font-bold text-lg">
                            {[ switch.name[0] | upper ]}
                        </div>
                        <div>
                            <h3 class="text-gray-800 font-semibold text-lg">{[ switch.name ]}</h3>
                            <p class="text-gray-500 text-sm">ID: {[ switch.id ]}</p>
                        </div>
                    </div>
                    <button class="delete-switch text-red-500 hover:text-red-600 transition-colors" data-id="{[ switch.id ]}" title="删除开关">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>

                <!-- 开关控件 -->
                <div class="flex items-center justify-between">
                    <div class="text-gray-800">
                        <div class="text-sm text-gray-600">状态</div>
                        <div class="font-semibold {% if switch.sw %}text-green-600{% else %}text-red-600{% endif %}">
                            {% if switch.sw %}🟢 已开启{% else %}🔴 已关闭{% endif %}
                        </div>
                    </div>
                    <label class="switch">
                        <input type="checkbox" class="switch-toggle" data-id="{[ switch.id ]}" {% if switch.sw %}checked{% endif %}>
                        <span class="slider"></span>
                    </label>
                </div>

                <!-- 开关信息 -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex justify-between text-sm text-gray-500">
                        <span>最后操作</span>
                        <span class="last-updated">刚刚</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 空状态 -->
        {% if switches.length === 0 %}
        <div class="text-center py-16">
            <div class="text-6xl mb-4">🎛️</div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-2">暂无开关</h3>
            <p class="text-gray-600 mb-6">点击"添加开关"按钮创建第一个开关</p>
            <button class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors shadow-lg">
                立即添加
            </button>
        </div>
        {% endif %}
    </div>

    <!-- 添加开关模态框 -->
    <div id="addSwitchModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
        <div class="bg-white/95 backdrop-blur-lg rounded-xl p-8 max-w-md w-full mx-4 border border-gray-200 shadow-2xl">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">添加新开关</h3>
            <form id="addSwitchForm">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2">开关名称</label>
                    <input type="text" id="switchName" class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200" placeholder="请输入开关名称" required>
                </div>
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">初始状态</label>
                    <label class="switch">
                        <input type="checkbox" id="switchInitialState">
                        <span class="slider"></span>
                    </label>
                    <span class="text-gray-600 ml-3">默认开启</span>
                </div>
                <div class="flex gap-4">
                    <button type="button" id="cancelAddSwitch" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-medium transition-colors shadow-md">
                        取消
                    </button>
                    <button type="submit" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium transition-colors shadow-md">
                        添加
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
        <div class="bg-white/95 backdrop-blur-lg rounded-xl p-8 text-center shadow-2xl border border-gray-200">
            <div class="loading w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <div class="text-gray-800 font-medium">处理中...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let switches = {[ switches | dump | safe ]};

        // DOM元素
        const switchesContainer = document.getElementById('switchesContainer');
        const refreshBtn = document.getElementById('refreshBtn');
        const addSwitchBtn = document.getElementById('addSwitchBtn');
        const toggleAllBtn = document.getElementById('toggleAllBtn');
        const addSwitchModal = document.getElementById('addSwitchModal');
        const addSwitchForm = document.getElementById('addSwitchForm');
        const cancelAddSwitch = document.getElementById('cancelAddSwitch');
        const loadingOverlay = document.getElementById('loadingOverlay');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateStatistics();
            updateLastUpdateTime();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 刷新按钮
            refreshBtn.addEventListener('click', refreshSwitches);

            // 添加开关按钮
            addSwitchBtn.addEventListener('click', showAddSwitchModal);

            // 全部切换按钮
            toggleAllBtn.addEventListener('click', toggleAllSwitches);

            // 模态框相关
            cancelAddSwitch.addEventListener('click', hideAddSwitchModal);
            addSwitchForm.addEventListener('submit', handleAddSwitch);

            // 点击模态框外部关闭
            addSwitchModal.addEventListener('click', function(e) {
                if (e.target === addSwitchModal) {
                    hideAddSwitchModal();
                }
            });

            // 开关切换事件委托
            switchesContainer.addEventListener('change', function(e) {
                if (e.target.classList.contains('switch-toggle')) {
                    handleSwitchToggle(e.target);
                }
            });

            // 删除开关事件委托
            switchesContainer.addEventListener('click', function(e) {
                if (e.target.closest('.delete-switch')) {
                    const btn = e.target.closest('.delete-switch');
                    handleDeleteSwitch(btn.dataset.id);
                }
            });
        }

        // 显示加载状态
        function showLoading() {
            loadingOverlay.classList.remove('hidden');
            loadingOverlay.classList.add('flex');
        }

        // 隐藏加载状态
        function hideLoading() {
            loadingOverlay.classList.add('hidden');
            loadingOverlay.classList.remove('flex');
        }

        // 显示添加开关模态框
        function showAddSwitchModal() {
            addSwitchModal.classList.remove('hidden');
            addSwitchModal.classList.add('flex');
            document.getElementById('switchName').focus();
        }

        // 隐藏添加开关模态框
        function hideAddSwitchModal() {
            addSwitchModal.classList.add('hidden');
            addSwitchModal.classList.remove('flex');
            addSwitchForm.reset();
        }

        // 刷新开关数据
        async function refreshSwitches() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<span class="loading w-5 h-5 border-2 border-white border-t-transparent rounded-full inline-block mr-2"></span>刷新中...';
            try {
                const response = await fetch('/api/sw/switches');
                const result = await response.json();

                if (result.success) {
                    switches = result.data;
                    renderSwitches();
                    updateStatistics();
                    updateLastUpdateTime();
                    showNotification('数据刷新成功', 'success');
                } else {
                    showNotification('刷新失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('刷新失败:', error);
                showNotification('网络错误，刷新失败', 'error');
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>刷新数据`;
            }
        }

        // 处理开关切换
        async function handleSwitchToggle(toggle) {
            const id = toggle.dataset.id;
            const newState = toggle.checked ? 1 : 0;

            // 禁用开关防止重复点击
            toggle.disabled = true;

            try {
                const response = await fetch('/api/sw/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id: parseInt(id), sw: newState })
                });

                const result = await response.json();

                if (result.success) {
                    // 更新本地数据
                    const switchIndex = switches.findIndex(s => s.id == id);
                    if (switchIndex !== -1) {
                        switches[switchIndex].sw = newState;
                    }

                    updateStatistics();
                    updateSwitchCard(id, newState);
                    showNotification(result.message, 'success');
                } else {
                    // 恢复开关状态
                    toggle.checked = !toggle.checked;
                    showNotification('操作失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('开关切换失败:', error);
                toggle.checked = !toggle.checked;
                showNotification('网络错误，操作失败', 'error');
            } finally {
                // 重新启用开关
                toggle.disabled = false;
            }
        }

        // 处理添加开关
        async function handleAddSwitch(e) {
            e.preventDefault();

            const name = document.getElementById('switchName').value.trim();
            const sw = document.getElementById('switchInitialState').checked ? 1 : 0;

            if (!name) {
                showNotification('请输入开关名称', 'error');
                return;
            }

            showLoading();

            try {
                const response = await fetch('/api/sw/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, sw })
                });

                const result = await response.json();

                if (result.success) {
                    switches.push(result.data);
                    renderSwitches();
                    updateStatistics();
                    hideAddSwitchModal();
                    showNotification(result.message, 'success');
                } else {
                    showNotification('添加失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('添加开关失败:', error);
                showNotification('网络错误，添加失败', 'error');
            } finally {
                hideLoading();
            }
        }

        // 处理删除开关
        async function handleDeleteSwitch(id) {
            if (!confirm('确定要删除这个开关吗？此操作不可恢复。')) {
                return;
            }

            showLoading();

            try {
                const response = await fetch(`/api/sw/delete/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    switches = switches.filter(s => s.id != id);
                    renderSwitches();
                    updateStatistics();
                    showNotification(result.message, 'success');
                } else {
                    showNotification('删除失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('删除开关失败:', error);
                showNotification('网络错误，删除失败', 'error');
            } finally {
                hideLoading();
            }
        }

        // 全部切换
        async function toggleAllSwitches() {
            const activeCount = switches.filter(s => s.sw === 1).length;
            const newState = activeCount < switches.length / 2 ? 1 : 0;

            showLoading();

            const promises = switches.map(async (switchItem) => {
                if (switchItem.sw !== newState) {
                    try {
                        const response = await fetch('/api/sw/update', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ id: switchItem.id, sw: newState })
                        });
                        return response.json();
                    } catch (error) {
                        console.error(`切换开关 ${switchItem.id} 失败:`, error);
                        return { success: false };
                    }
                }
                return { success: true };
            });

            try {
                await Promise.all(promises);
                await refreshSwitches();
                showNotification(`所有开关已${newState ? '开启' : '关闭'}`, 'success');
            } catch (error) {
                console.error('批量切换失败:', error);
                showNotification('批量操作失败', 'error');
            } finally {
                hideLoading();
            }
        }

        // 渲染开关列表
        function renderSwitches() {
            if (switches.length === 0) {
                switchesContainer.innerHTML = `
                    <div class="col-span-full text-center py-16">
                        <div class="text-6xl mb-4">🎛️</div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-2">暂无开关</h3>
                        <p class="text-gray-600 mb-6">点击"添加开关"按钮创建第一个开关</p>
                        <button onclick="showAddSwitchModal()" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors shadow-lg">
                            立即添加
                        </button>
                    </div>
                `;
                return;
            }

            switchesContainer.innerHTML = switches.map((switchItem, index) => {
                const colorIndex = index % 7;
                return `
                    <div class="switch-card bg-white/90 backdrop-blur-lg rounded-xl p-6 border-2 rainbow-border-${colorIndex} shadow-lg" data-switch-id="${switchItem.id}">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 rainbow-${colorIndex} rounded-full flex items-center justify-center text-white font-bold text-lg">
                                    ${switchItem.name.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <h3 class="text-gray-800 font-semibold text-lg">${switchItem.name}</h3>
                                    <p class="text-gray-500 text-sm">ID: ${switchItem.id}</p>
                                </div>
                            </div>
                            <button class="delete-switch text-red-500 hover:text-red-600 transition-colors" data-id="${switchItem.id}" title="删除开关">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-gray-800">
                                <div class="text-sm text-gray-600">状态</div>
                                <div class="font-semibold ${switchItem.sw ? 'text-green-600' : 'text-red-600'}">
                                    ${switchItem.sw ? '🟢 已开启' : '🔴 已关闭'}
                                </div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" class="switch-toggle" data-id="${switchItem.id}" ${switchItem.sw ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex justify-between text-sm text-gray-500">
                                <span>最后操作</span>
                                <span class="last-updated">刚刚</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新统计信息
        function updateStatistics() {
            document.getElementById('totalSwitches').textContent = switches.length;
            document.getElementById('activeSwitches').textContent = switches.filter(s => s.sw === 1).length;
            document.getElementById('inactiveSwitches').textContent = switches.filter(s => s.sw === 0).length;
        }

        // 更新开关卡片状态
        function updateSwitchCard(id, newState) {
            const card = document.querySelector(`[data-switch-id="${id}"]`);
            if (card) {
                const statusDiv = card.querySelector('.font-semibold');
                if (statusDiv) {
                    statusDiv.className = `font-semibold ${newState ? 'text-green-400' : 'text-red-400'}`;
                    statusDiv.textContent = newState ? '🟢 已开启' : '🔴 已关闭';
                }

                const lastUpdated = card.querySelector('.last-updated');
                if (lastUpdated) {
                    lastUpdated.textContent = '刚刚';
                }
            }
        }

        // 更新最后更新时间
        function updateLastUpdateTime() {
            var el = document.getElementById('lastUpdate');
            if (el) {
                el.textContent = new Date().toLocaleString('zh-CN');
            }
        }

        // 显示通知
        // 通知队列，避免堆叠
        const notificationQueue = [];
        let notificationActive = false;
        function showNotification(message, type) {
            if (typeof type === 'undefined') type = 'info';
            notificationQueue.push({ message: message, type: type });
            if (!notificationActive) {
                processNotificationQueue();
            }
        }
        function processNotificationQueue() {
            if (notificationQueue.length === 0) {
                notificationActive = false;
                return;
            }
            notificationActive = true;
            var item = notificationQueue.shift();
            var message = item.message;
            var type = item.type;
            var notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 z-50 px-6 py-4 rounded-lg text-white font-medium shadow-lg transition-all duration-300 transform translate-x-full ' +
                (type === 'success' ? 'bg-green-600' : (type === 'error' ? 'bg-red-600' : 'bg-blue-600'));
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(function() {
                notification.classList.remove('translate-x-full');
            }, 100);
            setTimeout(function() {
                notification.classList.add('translate-x-full');
                setTimeout(function() {
                    document.body.removeChild(notification);
                    processNotificationQueue();
                }, 300);
            }, 2200); // 每条通知显示2.2秒，避免连在一起
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        refreshSwitches();
                        break;
                    case 'n':
                        e.preventDefault();
                        showAddSwitchModal();
                        break;
                }
            }

            if (e.key === 'Escape') {
                hideAddSwitchModal();
            }
        });
    </script>
</body>
</html>

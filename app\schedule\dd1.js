const Subscription = require('egg').Subscription;

class DdoCache extends Subscription {
  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      cron: '2 12 * * *',
      type: 'all', // 指定所有的 worker 都需要执行
      enabled: true,
    };
  }

  // subscribe 是真正定时任务执行时被运行的函数
  async subscribe() {
    // let ip = await this.ctx.curl("http://ip.wcy9.com/cnip.php")
    // ip = ip.data.toString('utf8'); // 将二进制数据转换为字符串
    // console.log(ip);
    // const res = await this.ctx.curl(
    //   'http://127.0.0.1:7001/fs?text=%E9%92%89%E9%92%89%E6%89%93%E5%8D%A1',
    //   {
    //     dataType: 'json',
    //   },
    // );
    // console.log(res);
  }
}

module.exports = DdoCache;

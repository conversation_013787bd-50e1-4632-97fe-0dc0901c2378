async function login(){
    startloading();
    var res=await postjson("/login",{
        password: md5(V('password')),
    });
    endloading();
    if(res.success){
        // 使用服务器返回的重定向URL，如果没有则默认跳转到首页
        const redirectUrl = res.redirectUrl || '/';
        redirect(redirectUrl);
    } else {
        notice(res.message || '登录失败');
    }
}
E('login').onclick=login;
document.onkeyup=function(e){
    var event=e||window.event;
    var key=event.which||event.keyCode||event.charCode;
    if(key == 13)login();
};
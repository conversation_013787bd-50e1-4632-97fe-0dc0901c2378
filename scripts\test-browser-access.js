#!/usr/bin/env node

// 测试浏览器访问问题

const axios = require('axios');

// 配置
const BASE_URL = 'http://127.0.0.1:7001';

// 模拟不同的User-Agent
const userAgents = {
  chrome: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  firefox: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
  safari: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
  edge: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
  apifox: 'Apifox/1.0.0 (https://www.apifox.cn)',
  curl: 'curl/7.68.0',
  oldChrome: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.0.0 Safari/537.36',
  unknown: 'UnknownBot/1.0'
};

async function testUserAgent(name, userAgent) {
  console.log(`\n🧪 测试 ${name}:`);
  console.log(`User-Agent: ${userAgent.substring(0, 80)}...`);
  
  try {
    // 测试GET请求
    const response = await axios.get(`${BASE_URL}/api/deploy/success`, {
      headers: {
        'User-Agent': userAgent
      },
      timeout: 5000
    });
    
    console.log(`✅ GET请求成功 - 状态码: ${response.status}`);
    console.log(`响应: ${JSON.stringify(response.data).substring(0, 100)}...`);
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ GET请求失败 - 状态码: ${error.response.status}`);
      console.log(`错误: ${error.response.statusText}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`🔌 连接被拒绝 - 服务器可能未启动`);
    } else {
      console.log(`💥 请求错误: ${error.message}`);
    }
  }
  
  try {
    // 测试POST请求
    const postResponse = await axios.post(`${BASE_URL}/api/deploy/success`, {
      projectName: 'Test Project',
      environment: 'test'
    }, {
      headers: {
        'User-Agent': userAgent,
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    console.log(`✅ POST请求成功 - 状态码: ${postResponse.status}`);
    
  } catch (error) {
    if (error.response) {
      console.log(`❌ POST请求失败 - 状态码: ${error.response.status}`);
    } else {
      console.log(`💥 POST请求错误: ${error.message}`);
    }
  }
}

async function testChromeVersions() {
  console.log('\n🔍 测试不同Chrome版本:');
  
  const chromeVersions = [
    { version: '90', shouldBlock: true },
    { version: '100', shouldBlock: false },
    { version: '120', shouldBlock: false },
    { version: '135', shouldBlock: false }
  ];
  
  for (const { version, shouldBlock } of chromeVersions) {
    const ua = `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version}.0.0.0 Safari/537.36`;
    
    console.log(`\n📱 Chrome ${version} (预期${shouldBlock ? '被阻止' : '通过'}):`);
    
    try {
      const response = await axios.get(`${BASE_URL}/api/deploy/template`, {
        headers: { 'User-Agent': ua },
        timeout: 3000
      });
      
      console.log(`✅ 请求成功 - ${shouldBlock ? '❗意外通过' : '✓符合预期'}`);
      
    } catch (error) {
      if (error.response?.status === 404) {
        console.log(`❌ 404错误 - ${shouldBlock ? '✓符合预期' : '❗意外阻止'}`);
      } else {
        console.log(`💥 其他错误: ${error.message}`);
      }
    }
  }
}

async function testAPIEndpoints() {
  console.log('\n🎯 测试API端点可访问性:');
  
  const endpoints = [
    { path: '/api/deploy/success', method: 'GET' },
    { path: '/api/deploy/success', method: 'POST' },
    { path: '/api/deploy/failure', method: 'GET' },
    { path: '/api/deploy/test', method: 'GET' },
    { path: '/api/deploy/template', method: 'GET' },
    { path: '/logs', method: 'GET' },
    { path: '/logs/test', method: 'GET' }
  ];
  
  for (const { path, method } of endpoints) {
    try {
      const config = {
        headers: {
          'User-Agent': userAgents.chrome
        },
        timeout: 3000
      };
      
      let response;
      if (method === 'POST') {
        response = await axios.post(`${BASE_URL}${path}`, {}, config);
      } else {
        response = await axios.get(`${BASE_URL}${path}`, config);
      }
      
      console.log(`✅ ${method} ${path} - 状态码: ${response.status}`);
      
    } catch (error) {
      if (error.response) {
        console.log(`❌ ${method} ${path} - 状态码: ${error.response.status}`);
      } else {
        console.log(`💥 ${method} ${path} - 错误: ${error.message}`);
      }
    }
  }
}

async function main() {
  console.log('🔍 浏览器访问问题诊断工具');
  console.log('服务器地址:', BASE_URL);
  console.log('测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('═'.repeat(60));
  
  // 测试不同User-Agent
  for (const [name, ua] of Object.entries(userAgents)) {
    await testUserAgent(name, ua);
  }
  
  // 测试Chrome版本过滤
  await testChromeVersions();
  
  // 测试API端点
  await testAPIEndpoints();
  
  console.log('\n📋 问题总结:');
  console.log('1. 如果Chrome浏览器被阻止，检查版本过滤逻辑');
  console.log('2. 如果API返回404，检查路由配置');
  console.log('3. 如果中间件过滤，检查User-Agent白名单');
  console.log('4. 确认服务器是否正常启动在7001端口');
  
  console.log('\n💡 解决方案:');
  console.log('1. 降低Chrome版本要求 (135 -> 100)');
  console.log('2. 添加API路由到白名单');
  console.log('3. 支持GET方法用于浏览器测试');
  
  console.log('\n🎉 测试完成!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testUserAgent, testChromeVersions, testAPIEndpoints };

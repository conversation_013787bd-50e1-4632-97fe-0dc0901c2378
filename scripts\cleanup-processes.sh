#!/bin/bash

# 进程清理脚本 - 专门清理残留的Node进程

echo "🧹 开始清理残留进程..."

# 显示当前Node进程
echo "📋 当前Node进程:"
ps aux | grep node | grep -v grep

echo ""
echo "🔍 查找Egg相关进程..."

# 查找并杀死Egg相关进程
EGG_PIDS=$(ps aux | grep -E "(egg-server|egg-cluster|egg-scripts)" | grep -v grep | awk '{print $2}')

if [ -z "$EGG_PIDS" ]; then
    echo "✅ 没有发现Egg相关进程"
else
    echo "🎯 发现以下Egg进程:"
    ps aux | grep -E "(egg-server|egg-cluster|egg-scripts)" | grep -v grep
    
    echo ""
    echo "💀 杀死这些进程..."
    echo "$EGG_PIDS" | xargs kill -9 2>/dev/null || true
    
    sleep 2
    
    # 再次检查
    REMAINING=$(ps aux | grep -E "(egg-server|egg-cluster|egg-scripts)" | grep -v grep | wc -l)
    if [ $REMAINING -eq 0 ]; then
        echo "✅ 所有Egg进程已清理"
    else
        echo "⚠️ 仍有 $REMAINING 个进程未清理"
    fi
fi

# 检查端口占用
echo ""
echo "🔍 检查端口7001占用情况..."
PORT_USAGE=$(netstat -tlnp | grep :7001)
if [ -z "$PORT_USAGE" ]; then
    echo "✅ 端口7001已释放"
else
    echo "⚠️ 端口7001仍被占用:"
    echo "$PORT_USAGE"
    
    # 强制释放端口
    echo "🔥 强制释放端口..."
    fuser -k 7001/tcp 2>/dev/null || true
    sleep 1
    
    # 再次检查
    if netstat -tlnp | grep :7001 > /dev/null; then
        echo "❌ 端口释放失败"
    else
        echo "✅ 端口已释放"
    fi
fi

echo ""
echo "📊 清理后的进程状态:"
ps aux | grep node | grep -v grep || echo "没有Node进程运行"

echo ""
echo "🎉 进程清理完成！"

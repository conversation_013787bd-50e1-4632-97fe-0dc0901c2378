# 定时任务配置说明

## 📋 可用的定时任务文件

### 1. `job.js` - 多任务合并版本
- 包含81秒和60秒两个定时任务
- 使用数组导出多个任务
- 适合简单的多任务场景

### 2. `job-81s.js` - 81秒独立任务
- 专门处理片源同步相关任务
- 启动后立即执行一次
- 包含详细的日志记录

### 3. `job-60s.js` - 60秒独立任务
- 专门处理APSGO监控相关任务
- 启动后不立即执行
- 包含详细的日志记录

### 4. `job-manager.js` - 任务管理器版本
- 统一管理多个定时任务
- 支持任务启用/禁用配置
- 包含任务执行状态监控
- 支持扩展更多任务

### 5. `gz.js` - 广州服务器专用任务
- 只在广州服务器执行
- 2秒间隔的AI相关任务

## ⚙️ 配置选项说明

### 时间间隔配置
```javascript
schedule: {
  interval: '81s',    // 81秒执行一次
  interval: '60s',    // 60秒执行一次
  interval: '30s',    // 30秒执行一次
  interval: '2m',     // 2分钟执行一次
  interval: '1h',     // 1小时执行一次
}
```

### 执行类型
```javascript
schedule: {
  type: 'all',        // 所有worker都执行
  type: 'worker',     // 只有一个worker执行
}
```

### 环境配置
```javascript
schedule: {
  env: ['prod'],              // 只在生产环境执行
  env: ['local', 'prod'],     // 在本地和生产环境执行
}
```

### 立即执行
```javascript
schedule: {
  immediate: true,    // 应用启动后立即执行一次
  immediate: false,   // 应用启动后不立即执行
}
```

### 禁用任务
```javascript
schedule: {
  disable: true,      // 禁用此任务
  disable: false,     // 启用此任务
}
```

## 🎯 推荐使用方案

### 方案A：使用独立文件（推荐）
- 使用 `job-81s.js` 和 `job-60s.js`
- 每个任务独立管理，便于维护
- 可以单独启用/禁用某个任务

### 方案B：使用任务管理器
- 使用 `job-manager.js`
- 统一配置和管理
- 适合复杂的任务调度需求

### 方案C：使用合并文件
- 使用修改后的 `job.js`
- 适合简单的多任务场景
- 代码相对集中

## 🔧 如何启用/禁用任务

### 方法1：删除或重命名文件
```bash
# 禁用81秒任务
mv app/schedule/job-81s.js app/schedule/job-81s.js.disabled

# 重新启用
mv app/schedule/job-81s.js.disabled app/schedule/job-81s.js
```

### 方法2：在配置文件中控制
在 `config/config.prod.js` 中：
```javascript
config.server = {
  enableSchedule: true,  // 总开关
  features: {
    dataSync: true,      // 控制81秒任务
    monitoring: true,    // 控制60秒任务
  },
};
```

### 方法3：在任务文件中添加 disable
```javascript
schedule: {
  disable: true,  // 禁用此任务
}
```

## 📊 任务监控

所有任务都包含：
- 执行开始/结束日志
- 执行耗时统计
- 错误处理和日志记录
- 服务器配置检查

## 🚀 使用建议

1. **开发环境**：建议禁用所有定时任务
2. **测试环境**：可以启用部分任务进行测试
3. **生产环境**：根据服务器角色启用相应任务
4. **监控告警**：建议集成飞书等通知服务

## 📝 添加新任务

1. 复制现有任务文件
2. 修改时间间隔和任务逻辑
3. 更新任务名称和日志
4. 在配置文件中添加对应的开关

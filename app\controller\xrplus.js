const Controller = require('egg').Controller;
const cheerio = require('cheerio');
const axios = require('axios');
const preurl = `https://re.101616.xyz/`;

class XrplusController extends Controller {
  async index() {
    const ctx = this.ctx;
    let page = ctx.query.page ? ctx.query.page : 1;
    await ctx.render('xr/index', {
      page: page,
      ppage: +page - 1,
      npage: +page + 1,
    });
  }

  async sindex() {
    const ctx = this.ctx;
    let page = ctx.query.page ? ctx.query.page : 1;
    let keyword = ctx.query.s;
    keyword = encodeURI(keyword);
    await ctx.render('xr/s', {
      page: page,
      ppage: +page - 1,
      npage: +page + 1,
      skey: keyword,
    });
  }

  async sindex1() {
    const ctx = this.ctx;
    let page = ctx.query.page ? ctx.query.page : 10;
    let keyword = ctx.query.s ? ctx.query.s : '鱼子酱';
    // let page = 10;
    // let keyword = '阿朱';
    keyword = encodeURI(keyword);
    let list = [];
    for (let i = 1; i <= page; i++) {
      let xurl =
        'https://www.xiurenb.vip/plus/search/index.asp?keyword=' +
        keyword +
        '&searchtype=title&p=' +
        i;
      let a = await ctx.service.xrplus.sou(xurl);
      for (let b of a) {
        let url = b.url;
        list.push(url.replace('/xrnei?url=', ''));
      }
      // .replace("/xrnei?url=","")
    }
    ctx.body = list;
  }

  async download() {
    const ctx = this.ctx;
    let page = 1;
    let keyword = '鱼子酱';
    keyword = encodeURI(keyword);
    let list = [];
    for (let i = 1; i <= page; i++) {
      let xurl =
        'https://www.xiurenb.vip/plus/search/index.asp?keyword=' +
        keyword +
        '&searchtype=title&p=' +
        i;
      let a = await ctx.service.xrplus.sou(xurl);
      for (let b of a) {
        let url = b.url;
        list.push(url.replace('/xrnei?url=', ''));
      }
      // .replace("/xrnei?url=","")
    }
    await ctx.service.xr.downloadUrl(list);
    ctx.body = list;
  }

  async nei() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    await ctx.render('xr/nei', { url: url });
  }

  async clist() {
    const ctx = this.ctx;
    let page = ctx.query.page / 1;
    let json = ctx.query.j;
    let xurl;
    if (page === 1) {
      xurl = 'https://www.xr05.xyz/XiuRen/';
    } else {
      xurl = 'https://www.xr05.xyz/XiuRen/index' + page + '.html';
    }
    let data = await ctx.service.xrplus.clist(xurl);
    let ids = [];
    if (json) {
      for (let item of data) {
        const match = item.url.match(/\/(\d+)\.html/);
        if (match) {
          // 提取匹配到的数字作为ID
          const id = match[1];
          ids.push(id);
          // console.log(`提取到的ID为: ${id}`);
        } else {
          // console.log("未找到匹配的ID");
        }
      }
      ctx.body = ids;
      return;
    }
    ctx.body = data;
  }

  async cclist() {
    const { ctx, app } = this;
    let page = ctx.query.page;
    let clientIp =
      ctx.request.headers['x-forwarded-for'] ||
      (ctx.request &&
        ctx.request.connection &&
        ctx.request.connection.remoteAddress);
    // await ctx.service.feishu.fs(`${clientIp}访问了xr:${page}\n`);
    let res = await ctx.service.xrplus.cclist(page);
    let baseurl = await app.redis.get('xrurl');
    let x = [];
    for (let item of res) {
      x.push({
        url: '/xrplusnei?id=' + item.xrid,
        refm:
          item.refm && item.refm.match('/file/')
            ? 'https://img1.101616.xyz' + item.refm
            : 'https://re.101616.xyz/' + baseurl + item.fm,
      });
      // item.url = '/xrplusnei?id=' + item.xrid;
      // item.refm = item.refm.match('/file/')
      //   ? 'https://img1.101616.xyz' + item.refm
      //   : 'https://re.101616.xyz/' + baseurl + item.fm;
      // delete item.id;
      // delete item.issave;
      // delete item.fm;
    }
    ctx.body = x;
  }

  async plusindex() {
    const { ctx } = this;
    let res = await this.app.mysql.query('select count(xrid) as total from xr');
    let total = res[0].total;
    await ctx.render('xrplus/index', { total: total });
  }

  async plusnei() {
    const { ctx } = this;
    let id = ctx.query.id;
    await ctx.render('xrplus/nei', { id: id });
  }

  async xrpluspageinfo() {
    const { ctx, app } = this;
    let id = ctx.query.id;
    let clientIp =
      ctx.request.headers['x-forwarded-for'] ||
      (ctx.request &&
        ctx.request.connection &&
        ctx.request.connection.remoteAddress);
    // await ctx.service.feishu.fs(`${clientIp}访问了xr:${id}\n`);
    let res = await ctx.service.xrplus.xrpluspageinfo(id);
    let data = [];
    let baseurl = await app.redis.get('xrurl');
    for (let item of res) {
      item.reurl =
        item.reurl && item.reurl.match('/file/')
          ? 'https://img1.101616.xyz' + item.reurl
          : 'https://re.101616.xyz/' + baseurl + item.ourl;
      // item.reurl = 'https://re.101616.xyz/' + baseurl + item.ourl;

      data.push(item.reurl);
    }
    ctx.body = data;
  }

  async sou() {
    const { ctx, app } = this;
    let baseurl = await app.redis.get('xrurl');
    let keyword = ctx.query.s;
    let page = ctx.query.page;
    let per = 12;
    const offset = (page - 1) * per;
    let xsql = `select * from xr where title like '%${keyword}%' ORDER BY xrid desc limit ${per} offset ${offset}`;
    let info = await ctx.service.xrplus.query(xsql);
    for (let item of info) {
      item.img =
        item.refm && item.refm.match('/file/')
          ? 'https://img1.101616.xyz' + item.refm
          : 'https://re.101616.xyz/' + baseurl + item.fm;
      item.url = '/xrplusnei?id=' + item.xrid;
    }
    ctx.body = info;
  }

  async pageinfo() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    // data[0] = url;
    ctx.body = await ctx.service.xrplus.page(url);
  }

  async pages() {
    const ctx = this.ctx;
    let url = 'https://www.xr05.xyz/XiuRen/10671.html';
    // data[0] = url;
    ctx.body = await ctx.service.xrplus.page(url);
  }

  async info() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    // let url = "https://www.xr05.xyz/XiuRen/10671.html"
    ctx.body = await ctx.service.xrplus.imgs2(url);
  }

  async imgs() {
    const ctx = this.ctx;
    let url = ctx.query.url;
    let pd = ctx.query.pd;
    // let url = "https://www.1y.is/chinese/no-609-well.html"
    // let url = "https://www.xr05.xyz/XiuRen/10679.html"
    // let data = await ctx.service.xrplus.img(url,"202205");
    ctx.body = await ctx.service.xrplus.img(url, pd);
  }

  async getxrimglist() {
    const { ctx, app } = this;
    const res = await ctx.service.xr.query(
      `SELECT * FROM xr where (issave = 0)  ORDER BY xrid desc limit 1`,
    );
    if (!res.data) {
      return;
    }

    const data = await this.imglist(res.data[0].id);
    await this.getxrimglist();
    ctx.body = res;
  }

  async imglist() {
    const { ctx, app } = this;
    let id = '16218';
    const next = '下页';
    const urls = [];
    const baseurl = await app.redis.get('xrurl');
    // const baseurl = 'https://www.20mn.vip/';
    const baseUrl = preurl + baseurl;
    console.log(baseUrl);
    let index = 0;
    let hasNextPage = true;

    while (hasNextPage) {
      try {
        const url = `${baseUrl}XiuRen/XiuRen/${id}${index ? '_' + index : ''}.html`;
        // console.log(`Fetching: ${url}`);

        const res = await axios.get(url, {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          },
        });

        const $ = cheerio.load(res.data);

        // Collect image URLs from the current page
        $('.imgwebp img').each(function () {
          $(this).attr('src').match('uploadfile')
            ? urls.push($(this).attr('src'))
            : '';
          console.log($(this).attr('src'));
        });

        // Check if there is a next page
        const pageLinks = $('.page>a')
          .map((i, el) => $(el).text())
          .toArray();
        hasNextPage = pageLinks.includes(next);

        if (hasNextPage) {
          index++;
        }
      } catch (error) {
        // console.error(`Error fetching page ${index}:`, error);
        hasNextPage = false; // Stop the loop if an error occurs
      }
    }

    // console.log(urls);
    // for (const url of urls) {
    //   await ctx.service.xr.create('xrinfo', {
    //     xrid: id,
    //     ourl: url,
    //   });
    // }
    ctx.body = urls;
  }

  async sql() {
    const ctx = this.ctx;
    const lock = await ctx.service.xr.query(
      `SELECT COUNT(*) as \`lock\` FROM  xrinfo WHERE reurl IS NOT NULL AND reurl NOT LIKE '%/file/%' AND reurl NOT LIKE '%4040%'`,
    );
    const total = await ctx.service.xr.query(
      `SELECT count(*) as total FROM xrinfo`,
    );
    const reurlisnull = await ctx.service.xr.query(
      `SELECT COUNT(*) as reurlisnull FROM  xrinfo WHERE reurl is null`,
    );
    ctx.body = { total: total, reurlisnull: reurlisnull, lock: lock };
  }

  async getlist() {
    const { ctx, app } = this;
    const baseurl = await app.redis.get('xrurl');
    const baseUrl = preurl + baseurl;
    console.log(baseUrl);
    let page = 1;
    let data = [];
    let ys = +page === 1 ? '' : `list${page}.html`;
    console.log(baseUrl + '/XiuRen/XiuRen/' + ys);
    const response = await axios.get(baseUrl + 'XiuRen/XiuRen/' + ys);
    const odata = cheerio.load(response.data);

    odata('.imgbox').each((index, item) => {
      const fm = odata(item).find('img').attr('src');
      const x = odata(item).children('a').attr('href');
      const xridMatch = x.match(/\d+/);

      const xrid = xridMatch ? parseInt(xridMatch[0], 10) : null;
      if (+xrid === 16003) {
        return;
      }
      if (xrid) {
        data.push({
          xrid: xrid,
          url: '/xr2nei?id=' + xrid,
          img: fm,
        });
      }
    });
    console.log(data);

    for (const v of data) {
      let isexist = await ctx.service.xr.find('xr', {
        xrid: v.xrid,
      });
      if (!isexist.data) {
        let x = await ctx.service.xr.create('xr', {
          xrid: v.xrid,
          fm: v.img,
          issave: 0,
        });
      }
    }
    ctx.body = { data: data };
  }

  async xr2() {
    const { ctx } = this;
    let res = await this.app.mysql.query('select count(xrid) as total from xr');
    let total = res[0].total;
    await ctx.render('xrplus/2', { total: total });
  }

  async xr2nei() {
    const { app, ctx } = this;
    let id = ctx.query.id;
    const next = '下页';
    const urls = [];
    const baseurl = await app.redis.get('xr2url');
    const baseUrl = preurl + baseurl;
    let index = 0;
    let hasNextPage = true;
    let x = [];
    try {
      const url = `${baseUrl}/XiuRen/XiuRen/${id}${index ? '_' + index : ''}.html`;
      const res = await axios.get(url, {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
      });

      const $ = cheerio.load(res.data);

      const pageLinks = $('.page>a')
        .map((i, el) => $(el).text())
        .toArray();
      x = pageLinks;
      hasNextPage = pageLinks.includes(next);

      if (hasNextPage) {
        index++;
      }
    } catch (error) {
      console.error(`Error fetching page ${index}:`, error);
      hasNextPage = false; // Stop the loop if an error occurs
    }
    // console.log(x);
    // console.log(+x[x.length - 2]);
    await ctx.render('xrplus/2nei', { id: id, total: +x[x.length - 3] });
  }

  async xr2getlist() {
    const { ctx, app } = this;
    try {
      const page = ctx.query.page;
      // const baseurl = `https://www.imn05.top`;
      const baseurl = await app.redis.get('xr2url');
      const baseUrl = preurl + baseurl;
      // const baseUrl = baseurl;
      let data = [];
      let ys = +page === 1 ? '' : `list${page}.html`;
      console.log(baseUrl + '/XiuRen/XiuRen/' + ys);
      const response = await axios.get(baseUrl + '/XiuRen/XiuRen/' + ys);
      const odata = cheerio.load(response.data);

      odata('.imgbox').each((index, item) => {
        const fm = odata(item).find('img').attr('src');
        const x = odata(item).children('a').attr('href');
        const xridMatch = x.match(/\d+/);

        const xrid = xridMatch ? parseInt(xridMatch[0], 10) : null;

        if (xrid) {
          data.push({
            xrid: xrid,
            url: '/xr2nei?id=' + xrid,
            img: baseurl + fm,
          });
        }
      });

      ctx.body = { data: data };
    } catch (error) {
      // console.error('Error fetching data:', error);
      ctx.body = { error: 'Failed to fetch data' };
    }
  }

  async xr2imglist() {
    const { ctx, app } = this;
    let id = ctx.query.id;
    const next = '下页';
    const urls = [];
    const baseurl = await app.redis.get('xr2url');
    const baseUrl = preurl + baseurl;
    // const baseUrl = baseurl;
    let index = ctx.query.page || 0;
    let hasNextPage = true;

    try {
      const url = `${baseUrl}/XiuRen/XiuRen/${id}${+index !== 0 ? '_' + index : ''}.html`;
      console.log(`Fetching: ${url}`);

      const res = await axios.get(url, {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
      });

      const $ = cheerio.load(res.data);

      // Collect image URLs from the current page
      $('.imgwebp img').each(function () {
        $(this).attr('src').match('uploadfile')
          ? urls.push(baseUrl + $(this).attr('src'))
          : '';
        // console.log($(this).attr('src'));
      });

      // Check if there is a next page
      // const pageLinks = $('.page>a')
      //   .map((i, el) => $(el).text())
      //   .toArray();
      // hasNextPage = pageLinks.includes(next);
      //
      // if (hasNextPage) {
      //   index++;
      // }
    } catch (error) {
      // console.error(`Error fetching page ${index}:`, error);
      hasNextPage = false; // Stop the loop if an error occurs
    }
    ctx.body = urls;
  }

  async refmurl() {
    const { ctx, app } = this;
    const baseurl = await app.redis.get('xrurl');
    // const baseUrl = preurl + baseurl;
    // console.log(baseurl);
    // console.log(baseUrl);
    // return;
    let result = await ctx.service.xr.query(
      'select * from xr where refm is null limit 10',
    );
    let ids = '';
    for (const r of result) {
      if (!r.refm) {
        ids = ids + r.id + ',';
        // 构建正确的 URL
        // const imageUrl = new URL(r.fm, baseUrl).toString();
        const imageUrl = baseurl + r.fm;
        // console.log(imageUrl);
        const imgurl = await this.upload(imageUrl);
        console.log('imgurl', imgurl);
        const src = imgurl[0].src;
        console.log(src, r.xrid);
        // let result = await ctx.service.xr.update('xr', {
        //   refm: src,
        //   where: {
        //     xrid: r.xrid,
        //   },
        // });
        await ctx.service.xr.query(
          `update xr set refm = ` + src + ` where xrid = ` + r.xrid,
        );
      }
    }
    if (ids === '') {
      return null;
    }
    const idList = ids.split(',').filter(Boolean); // 将逗号分隔的字符串拆分为数组并去掉空字符串

    if (idList) {
      const sqlQuery = `SELECT * FROM xr WHERE id IN (${idList.join(',')})`;
      const [resx] = await ctx.service.xr.query(sqlQuery);
      ctx.body = resx;
    }
    return null;
  }

  async upload(remoteUrl) {
    try {
      // Download remote file
      const response = await axios.get('https://re.101616.xyz/' + remoteUrl, {
        responseType: 'arraybuffer', // Crucial change: Use arraybuffer!
      });

      console.log(response.data);
      const filename = remoteUrl.split('/').pop();

      // Create a Blob from the ArrayBuffer
      const blob = new Blob([response.data], {
        type: response.headers['content-type'],
      }); // Try to get content type from response

      const data = new FormData();
      data.append('file', blob, filename);

      // Build upload config
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://img1.101616.xyz/upload',
        headers: {
          Accept: 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
          Priority: 'u=1, i',
          'Sec-Ch-Ua':
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"Windows"',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'X-Kl-Ajax-Request': 'Ajax_Request',
          Referer: 'https://img1.101616.xyz/',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
        data,
      };

      // Execute upload
      const uploadResponse = await axios(config);

      // Log the response for debugging
      console.log('Upload Response:', uploadResponse.data);

      return uploadResponse.data;
    } catch (error) {
      console.error('Error uploading file:', error.message);
      // You can optionally re-throw the error here
    }
  }

  async xrurl() {
    const { ctx, app } = this;
    let redis_name = `xrurl`;
    let url = await app.redis.get(redis_name);

    if (!url) {
      console.error('Error: URL not found in Redis.');
      return null; // 如果 Redis 中没有 URL，可以提前返回
    }

    try {
      console.log(url);
      const response = await axios.head(url, {
        maxRedirects: 0,
        validateStatus: (status) => status >= 200 && status < 400, // 允许 2xx 和 3xx
      });

      if (
        response.status >= 300 &&
        response.status < 400 &&
        response.headers.location
      ) {
        // 处理重定向
        const finalUrl = new URL(response.headers.location, url).href;
        await app.redis.set(redis_name, finalUrl);
        ctx.body = { url: finalUrl };
        return finalUrl;
      } else if (response.status >= 200 && response.status < 300) {
        // 无重定向，返回原URL
        await app.redis.set(redis_name, url);
        ctx.body = { url: url };
        return url;
      } else {
        // 处理其他非 2xx 和 3xx 状态码
        console.error(`Unexpected status code: ${response.status}`);
        return null;
      }
    } catch (error) {
      console.error('Error:', error.message);
      return null;
    }
  }

  async xr2url() {
    const { ctx, app } = this;
    let url = await app.redis.get('xr2url');

    if (!url) {
      console.error('Error: URL not found in Redis.');
      return null; // 如果 Redis 中没有 URL，可以提前返回
    }

    try {
      const response = await axios.head(url, {
        maxRedirects: 0,
        validateStatus: (status) => status >= 200 && status < 400, // 允许 2xx 和 3xx
      });

      if (
        response.status >= 300 &&
        response.status < 400 &&
        response.headers.location
      ) {
        // 处理重定向
        const finalUrl = new URL(response.headers.location, url).href;
        await app.redis.set('xr2url', finalUrl);
        ctx.body = { url: finalUrl };
        return finalUrl;
      } else if (response.status >= 200 && response.status < 300) {
        // 无重定向，返回原URL
        await app.redis.set('xr2url', url);
        ctx.body = { url: url };
        return url;
      } else {
        // 处理其他非 2xx 和 3xx 状态码
        console.error(`Unexpected status code: ${response.status}`);
        return null;
      }
    } catch (error) {
      console.error('Error:', error.message);
      return null;
    }
  }
}

module.exports = XrplusController;

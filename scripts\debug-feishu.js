#!/usr/bin/env node

/**
 * 调试飞书通知功能
 * 显示详细的错误信息
 */

const axios = require('axios');
const crypto = require('crypto');

// 测试所有飞书webhook
async function testAllFeishuWebhooks() {
  console.log('🔍 测试所有飞书webhook...');
  console.log('='.repeat(50));

  const webhooks = [
    {
      name: 'fs (主要)',
      secret: 'MsrMWf48gWQilMkpJ9lcpe',
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/8df456d9-de0c-4942-b48c-e4e65f77b80c'
    },
    {
      name: 'fs2',
      secret: 'uJ5OihJsGhEjzbJ5k7ewGd',
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/c9bc1206-1824-4dba-b95f-361b737abd0f'
    },
    {
      name: 'fs3',
      secret: 'tnKSokV6SosAfaGdBBuVSc',
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/cd179988-4ce0-4004-88dc-16b15da3feb1'
    }
  ];

  for (const webhook of webhooks) {
    console.log(`\n📡 测试 ${webhook.name}...`);
    await testSingleWebhook(webhook);
  }
}

// 测试单个webhook
async function testSingleWebhook(webhook) {
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const stringToSign = `${timestamp}\n${webhook.secret}`;
    const hmac = crypto.createHmac('sha256', stringToSign);
    const signature = hmac.digest('base64');

    const testMessage = `🧪 调试测试消息

**Webhook**: ${webhook.name}
**时间**: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
**状态**: 测试中

如果您收到这条消息，说明该webhook工作正常！`;

    // 测试文本消息
    console.log(`   📝 测试文本消息...`);
    const textData = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'text',
      content: {
        text: testMessage,
      },
    };

    const textResponse = await axios.post(webhook.url, textData, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`   ✅ 文本消息发送成功`);
    console.log(`   📊 响应状态: ${textResponse.status}`);
    console.log(`   📋 响应数据:`, textResponse.data);

    // 等待一下再发送下一条
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试富文本消息
    console.log(`   📝 测试富文本消息...`);
    const richData = {
      timestamp: timestamp,
      sign: signature,
      msg_type: 'post',
      content: {
        post: {
          zh_cn: {
            title: '🧪 富文本测试',
            content: [
              [
                {
                  tag: 'text',
                  text: '这是富文本测试消息',
                  style: ['bold']
                }
              ],
              [
                {
                  tag: 'text',
                  text: '\n时间: '
                },
                {
                  tag: 'text',
                  text: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
                }
              ],
              [
                {
                  tag: 'text',
                  text: '\n状态: ✅ 正常'
                }
              ]
            ]
          }
        }
      }
    };

    const richResponse = await axios.post(webhook.url, richData, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`   ✅ 富文本消息发送成功`);
    console.log(`   📊 响应状态: ${richResponse.status}`);
    console.log(`   📋 响应数据:`, richResponse.data);

  } catch (error) {
    console.log(`   ❌ 发送失败`);
    console.log(`   🚨 错误类型: ${error.name}`);
    console.log(`   📝 错误信息: ${error.message}`);
    
    if (error.response) {
      console.log(`   📊 HTTP状态: ${error.response.status}`);
      console.log(`   📋 响应数据:`, error.response.data);
    }
    
    if (error.code) {
      console.log(`   🔧 错误代码: ${error.code}`);
    }
  }
}

// 测试应用内的飞书服务
async function testAppFeishuService() {
  console.log('\n🔧 测试应用内飞书服务...');
  console.log('='.repeat(50));

  try {
    // 测试基础文本方法
    console.log('📝 测试 fs() 方法...');
    const response1 = await axios.get('http://localhost:7001/api/notification/test', {
      timeout: 10000
    });
    console.log('✅ fs() 方法调用成功');
    console.log('📋 响应:', response1.data);

    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试Markdown方法
    console.log('\n📝 测试 fsMarkdown() 方法...');
    const response2 = await axios.post('http://localhost:7001/api/notification/alert', {
      action: 'testMarkdown',
      message: '**这是Markdown测试**\n\n✅ 如果您看到格式化效果，说明Markdown功能正常'
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ fsMarkdown() 方法调用成功');
    console.log('📋 响应:', response2.data);

  } catch (error) {
    console.log('❌ 应用服务测试失败');
    console.log('🚨 错误信息:', error.message);
    
    if (error.response) {
      console.log('📊 HTTP状态:', error.response.status);
      console.log('📋 响应数据:', error.response.data);
    }
  }
}

// 检查网络连接
async function checkNetworkConnectivity() {
  console.log('\n🌐 检查网络连接...');
  console.log('='.repeat(50));

  const testUrls = [
    'https://open.feishu.cn',
    'https://www.baidu.com',
    'http://localhost:7001/health'
  ];

  for (const url of testUrls) {
    try {
      console.log(`📡 测试连接: ${url}`);
      const response = await axios.get(url, { 
        timeout: 5000,
        validateStatus: () => true // 接受所有状态码
      });
      console.log(`✅ 连接成功 (状态: ${response.status})`);
    } catch (error) {
      console.log(`❌ 连接失败: ${error.message}`);
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 飞书通知调试工具');
  console.log('用于排查消息发送问题');
  console.log('='.repeat(60));

  // 1. 检查网络连接
  await checkNetworkConnectivity();

  // 2. 测试所有webhook
  await testAllFeishuWebhooks();

  // 3. 测试应用服务
  await testAppFeishuService();

  console.log('\n' + '='.repeat(60));
  console.log('🎉 调试完成！');
  console.log('💡 请检查飞书群是否收到了测试消息');
  console.log('📝 如果仍然没有收到消息，可能的原因：');
  console.log('   1. 飞书机器人未正确添加到群组');
  console.log('   2. webhook地址已失效');
  console.log('   3. 签名验证失败');
  console.log('   4. 网络连接问题');
  console.log('   5. 飞书服务器问题');
}

// 运行调试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testAllFeishuWebhooks, testAppFeishuService, checkNetworkConnectivity };

'use strict';

const Controller = require('egg').Controller;
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const os = require('os');

class LogController extends Controller {
  // 检测操作系统
  isWindows() {
    return os.platform() === 'win32';
  }

  // 获取日志文件路径
  getLogPath(logType) {
    const { app } = this;
    const logDir = this.isWindows() ? path.join(app.baseDir, 'logs', app.name) : '/logs/egg';

    const logFiles = {
      web: path.join(logDir, 'egg-web.log'),
      agent: path.join(logDir, 'egg-agent.log'),
      error: path.join(logDir, 'common-error.log'),
    };

    return logFiles[logType];
  }

  // 显示日志页面
  async index() {
    await this.ctx.render('log.html');
  }

  // 实时日志流 - Web日志
  async webStream() {
    await this.streamLog('web', this.getLogPath('web'));
  }

  // 实时日志流 - Agent日志
  async agentStream() {
    await this.streamLog('agent', this.getLogPath('agent'));
  }

  // 实时日志流 - Error日志
  async errorStream() {
    await this.streamLog('error', this.getLogPath('error'));
  }

  // 通用日志流方法
  async streamLog(logType, logPath) {
    const { ctx } = this;

    try {
      // 强制设置状态码为200，防止404
      ctx.status = 200;

      // 设置SSE响应头
      ctx.set({
        'Content-Type': 'text/event-stream; charset=utf-8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });
    } catch (error) {
      ctx.logger.error(`设置响应头失败: ${error.message}`);
      ctx.status = 500;
      ctx.body = { error: '服务器内部错误' };
      return;
    }

    // 检查日志文件是否存在
    if (!fs.existsSync(logPath)) {
      const errorMsg = this.isWindows()
        ? `日志文件不存在: ${logPath}。请先启动应用生成日志文件。`
        : `日志文件不存在: ${logPath}`;

      ctx.res.write(
        `data: ${JSON.stringify({
          error: errorMsg,
          timestamp: new Date().toISOString(),
          isWindows: this.isWindows(),
          suggestion: this.isWindows()
            ? '在Windows开发环境下，日志文件会在应用启动后自动创建。'
            : null,
        })}\n\n`,
      );

      // 发送结束标记
      ctx.res.write(
        `data: ${JSON.stringify({
          message: '连接已关闭',
          timestamp: new Date().toISOString(),
        })}\n\n`,
      );

      ctx.res.end();
      return;
    }

    // 发送初始连接消息
    ctx.res.write(
      `data: ${JSON.stringify({
        message: `${logType} 日志流已连接`,
        timestamp: new Date().toISOString(),
      })}\n\n`,
    );

    // 读取最后100行作为初始内容
    try {
      const initialLines = await this.getLastLines(logPath, 100);
      initialLines.forEach((line) => {
        if (line.trim()) {
          // 尝试修复可能的编码问题
          let cleanLine = line;

          // 如果检测到乱码，尝试修复
          if (line.includes('鏈') || line.includes('嶅') || line.includes('姟')) {
            try {
              // 尝试从latin1转换为utf8
              cleanLine = Buffer.from(line, 'latin1').toString('utf8');
            } catch (e) {
              // 如果转换失败，使用原始行
              cleanLine = line;
            }
          }

          ctx.res.write(
            `data: ${JSON.stringify({
              line: cleanLine,
              timestamp: new Date().toISOString(),
            })}\n\n`,
          );
        }
      });
    } catch (error) {
      ctx.logger.error(`读取初始日志失败: ${error.message}`);
    }

    // 根据操作系统选择合适的命令实时监控日志
    let tailProcess;
    if (this.isWindows()) {
      // Windows 环境下使用文件监控
      this.watchFileForChanges(ctx, logPath);
      return;
    } else {
      // Linux/Unix 环境下使用 tail 命令
      try {
        tailProcess = spawn('tail', ['-f', logPath], {
          env: { ...process.env, LANG: 'zh_CN.UTF-8', LC_ALL: 'zh_CN.UTF-8' },
        });
      } catch (error) {
        ctx.logger.error(`启动 tail 命令失败: ${error.message}`);
        ctx.res.write(
          `data: ${JSON.stringify({
            error: `启动日志监控失败: ${error.message}`,
            timestamp: new Date().toISOString(),
          })}\n\n`,
        );
        ctx.res.end();
        return;
      }
    }

    // 处理新的日志行
    tailProcess.stdout.setEncoding('utf8');
    tailProcess.stdout.on('data', (data) => {
      const lines = data.toString('utf8').split('\n');
      lines.forEach((line) => {
        if (line.trim()) {
          try {
            // 尝试修复可能的编码问题
            let cleanLine = line;

            // 如果检测到乱码，尝试修复
            if (line.includes('鏈') || line.includes('嶅') || line.includes('姟')) {
              try {
                // 尝试从latin1转换为utf8
                cleanLine = Buffer.from(line, 'latin1').toString('utf8');
              } catch (e) {
                // 如果转换失败，使用原始行
                cleanLine = line;
              }
            }

            ctx.res.write(
              `data: ${JSON.stringify({
                line: cleanLine,
                timestamp: new Date().toISOString(),
              })}\n\n`,
            );
          } catch (error) {
            ctx.logger.error(`发送日志行失败: ${error.message}`);
          }
        }
      });
    });

    // 处理错误
    tailProcess.stderr.on('data', (data) => {
      ctx.logger.error(`tail 命令错误: ${data.toString()}`);
      ctx.res.write(
        `data: ${JSON.stringify({
          error: `监控错误: ${data.toString()}`,
          timestamp: new Date().toISOString(),
        })}\n\n`,
      );
    });

    // 客户端断开连接时清理
    ctx.req.on('close', () => {
      ctx.logger.info(`${logType} 日志流客户端断开连接`);
      if (tailProcess && !tailProcess.killed) {
        tailProcess.kill('SIGTERM');
      }
    });

    ctx.req.on('error', (error) => {
      ctx.logger.error(`${logType} 日志流连接错误: ${error.message}`);
      if (tailProcess && !tailProcess.killed) {
        tailProcess.kill('SIGTERM');
      }
    });

    // 进程退出处理
    tailProcess.on('exit', (code, signal) => {
      ctx.logger.info(`${logType} tail 进程退出: code=${code}, signal=${signal}`);
    });

    // 保持连接活跃
    const keepAlive = setInterval(() => {
      try {
        ctx.res.write(`: heartbeat\n\n`);
      } catch (error) {
        clearInterval(keepAlive);
        if (tailProcess && !tailProcess.killed) {
          tailProcess.kill('SIGTERM');
        }
      }
    }, 30000); // 每30秒发送心跳

    // 清理定时器
    ctx.req.on('close', () => {
      clearInterval(keepAlive);
    });
  }

  // Windows 下的文件监控方法
  watchFileForChanges(ctx, logPath) {
    let lastSize = 0;

    try {
      const stat = fs.statSync(logPath);
      lastSize = stat.size;
    } catch (error) {
      ctx.logger.error(`获取文件大小失败: ${error.message}`);
      return;
    }

    // 使用 fs.watchFile 监控文件变化
    const watcher = fs.watchFile(logPath, { interval: 1000 }, (curr, prev) => {
      if (curr.size > lastSize) {
        // 文件增长了，读取新内容
        const stream = fs.createReadStream(logPath, {
          start: lastSize,
          end: curr.size - 1,
          encoding: 'utf8',
        });

        let buffer = '';

        stream.on('data', (chunk) => {
          buffer += chunk;
          const lines = buffer.split('\n');
          buffer = lines.pop(); // 保留最后一个可能不完整的行

          lines.forEach((line) => {
            if (line.trim()) {
              try {
                ctx.res.write(
                  `data: ${JSON.stringify({
                    line: line,
                    timestamp: new Date().toISOString(),
                  })}\n\n`,
                );
              } catch (error) {
                ctx.logger.error(`发送日志行失败: ${error.message}`);
              }
            }
          });
        });

        stream.on('error', (error) => {
          ctx.logger.error(`读取文件流失败: ${error.message}`);
        });

        lastSize = curr.size;
      }
    });

    // 处理连接关闭
    ctx.req.on('close', () => {
      fs.unwatchFile(logPath);
      ctx.logger.info('客户端断开连接，停止文件监控');
    });

    ctx.req.on('error', () => {
      fs.unwatchFile(logPath);
      ctx.logger.info('连接错误，停止文件监控');
    });
  }

  // 获取文件最后N行
  async getLastLines(filePath, lineCount = 100) {
    if (this.isWindows()) {
      // Windows 环境下使用 Node.js 原生方法
      return this.getLastLinesWindows(filePath, lineCount);
    } else {
      // Linux/Unix 环境下使用 tail 命令
      return this.getLastLinesUnix(filePath, lineCount);
    }
  }

  // Windows 下获取文件最后N行
  async getLastLinesWindows(filePath, lineCount = 100) {
    return new Promise((resolve, reject) => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').filter((line) => line.trim());
        const lastLines = lines.slice(-lineCount);
        resolve(lastLines);
      } catch (error) {
        reject(new Error(`读取文件失败: ${error.message}`));
      }
    });
  }

  // Unix/Linux 下获取文件最后N行
  async getLastLinesUnix(filePath, lineCount = 100) {
    return new Promise((resolve, reject) => {
      const tailProcess = spawn('tail', ['-n', lineCount.toString(), filePath], {
        env: { ...process.env, LANG: 'zh_CN.UTF-8', LC_ALL: 'zh_CN.UTF-8' },
      });
      let output = '';

      tailProcess.stdout.setEncoding('utf8');
      tailProcess.stdout.on('data', (data) => {
        output += data.toString('utf8');
      });

      tailProcess.stderr.on('data', (data) => {
        reject(new Error(data.toString('utf8')));
      });

      tailProcess.on('close', (code) => {
        if (code === 0) {
          const lines = output.split('\n').filter((line) => line.trim());
          resolve(lines);
        } else {
          reject(new Error(`tail 命令退出码: ${code}`));
        }
      });
    });
  }

  // 下载日志文件
  async download() {
    const { ctx } = this;
    const { type } = ctx.query;

    const logPath = this.getLogPath(type);
    if (!logPath || !fs.existsSync(logPath)) {
      ctx.status = 404;
      ctx.body = { error: '日志文件不存在' };
      return;
    }

    const fileName = path.basename(logPath);
    const stat = fs.statSync(logPath);

    ctx.set({
      'Content-Type': 'application/octet-stream',
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Length': stat.size,
    });

    ctx.body = fs.createReadStream(logPath);
  }

  // 清空日志文件
  async clear() {
    const { ctx } = this;
    const { type } = ctx.request.body;

    const logPath = this.getLogPath(type);
    if (!logPath) {
      ctx.status = 400;
      ctx.body = { error: '无效的日志类型' };
      return;
    }

    try {
      // 清空文件内容但保留文件
      fs.writeFileSync(logPath, '');
      ctx.body = { success: true, message: `${type} 日志已清空` };
      ctx.logger.info(`日志文件已清空: ${logPath}`);
    } catch (error) {
      ctx.status = 500;
      ctx.body = { error: `清空日志失败: ${error.message}` };
      ctx.logger.error(`清空日志失败: ${error.message}`);
    }
  }

  // 获取日志文件信息
  async info() {
    const { ctx } = this;

    const logTypes = ['web', 'agent', 'error'];
    const info = {};

    for (const type of logTypes) {
      const logPath = this.getLogPath(type);
      try {
        if (fs.existsSync(logPath)) {
          const stat = fs.statSync(logPath);
          info[type] = {
            exists: true,
            size: stat.size,
            sizeHuman: this.formatFileSize(stat.size),
            modified: stat.mtime,
            path: logPath,
          };
        } else {
          info[type] = {
            exists: false,
            path: logPath,
          };
        }
      } catch (error) {
        info[type] = {
          exists: false,
          error: error.message,
          path: logPath,
        };
      }
    }

    ctx.body = info;
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = LogController;

'use strict';

/**
 * 统一Redis配置模块
 * 提供Redis的基础配置和环境特定的优化配置
 */

// 基础Redis连接配置
const baseConfig = {
  host: '************',
  port: 2639,
  password: '',
  db: '0',
};

// 默认连接配置
const defaultConnectionConfig = {
  connectTimeout: 10000,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
};

// 开发环境优化配置
const developmentConfig = {
  ...defaultConnectionConfig,
  lazyConnect: true, // 延迟连接，提升启动速度
  connectTimeout: 10000,
  maxRetriesPerRequest: 3,
};

// 生产环境优化配置
const productionConfig = {
  ...defaultConnectionConfig,
  lazyConnect: false, // 生产环境立即连接
  connectTimeout: 5000, // 生产环境缩短连接超时
  maxRetriesPerRequest: 5, // 生产环境增加重试次数
  retryDelayOnFailover: 50, // 生产环境缩短重试延迟
};

// 本地环境配置
const localConfig = {
  ...defaultConnectionConfig,
  connectTimeout: 15000, // 本地环境延长超时时间
  maxRetriesPerRequest: 2,
};

/**
 * 根据环境获取Redis配置
 * @param {string} env - 环境名称 (local, dev, prod)
 * @param {object} customConfig - 自定义配置覆盖
 * @returns {object} Redis配置对象
 */
function getRedisConfig(env = 'local', customConfig = {}) {
  let connectionConfig;
  let defaultAgent = false; // 默认不启用agent
  
  switch (env) {
    case 'prod':
    case 'production':
      connectionConfig = productionConfig;
      defaultAgent = true; // 生产环境启用agent
      break;
    case 'dev':
    case 'development':
      connectionConfig = developmentConfig;
      defaultAgent = false; // 开发环境禁用agent
      break;
    case 'local':
    default:
      connectionConfig = localConfig;
      defaultAgent = true; // 本地环境启用agent
  }

  return {
    client: {
      ...baseConfig,
      ...connectionConfig,
      ...customConfig.client,
    },
    agent: customConfig.agent !== undefined ? customConfig.agent : defaultAgent,
  };
}

/**
 * 获取本地环境Redis配置
 */
function getLocalRedisConfig(customConfig = {}) {
  return getRedisConfig('local', customConfig);
}

/**
 * 获取开发环境Redis配置
 */
function getDevelopmentRedisConfig(customConfig = {}) {
  return getRedisConfig('dev', customConfig);
}

/**
 * 获取生产环境Redis配置
 */
function getProductionRedisConfig(customConfig = {}) {
  return getRedisConfig('prod', customConfig);
}

module.exports = {
  baseConfig,
  defaultConnectionConfig,
  developmentConfig,
  productionConfig,
  localConfig,
  getRedisConfig,
  getLocalRedisConfig,
  getDevelopmentRedisConfig,
  getProductionRedisConfig,
};
